<?php

/**
 * 订单表单配置类
 * 用于生成订单相关的表单配置数据
 */
class OrderFormConfig
{
  // 订单类型映射
  public static $typeMap = [
    ['label' => '采购订单', 'value' => '2'],
    ['label' => '销售订单', 'value' => '3'],
    ['label' => '服务订单', 'value' => '27'],
  ];

  // 是否选项
  public static $isYes = [
    ['label' => '是', 'value' => '1'],
    ['label' => '否', 'value' => '0'],
  ];

  // 状态颜色映射
  public static $statusColorMap = [
    '1' => '#52c41a', // 绿色
    '0' => '#ff4d4f', // 红色
  ];

  // 交付方式映射
  public static $deliveryMethodMap = [
    ['label' => 'FOB', 'value' => 'FOB'],
    ['label' => 'CIF', 'value' => 'CIF'],
    ['label' => 'EXW', 'value' => 'EXW'],
  ];

  /**
   * 获取部门树配置
   */
  public static function getDeptTreeConfig($params = [], $multiple = false, $required = false, $editPermission = null): array
  {
    return [
      'url' => '/erp/dept/getTree',
      'params' => $params,
      'labelField' => 'name',
      'valueField' => 'id',
      'childrenField' => 'children',
      'multiple' => $multiple,
      'required' => $required,
      'editPermission' => $editPermission,
    ];
  }

  /**
   * 获取人员配置
   */
  public static function getPersonConfig($params = [], $multiple = false, $editPermission = null): array
  {
    return [
      'url' => '/erp/user/getList',
      'params' => $params,
      'labelField' => 'name',
      'valueField' => 'id',
      'multiple' => $multiple,
      'editPermission' => $editPermission,
    ];
  }

  /**
   * 获取表单数据配置
   */
  public static function getFormData(): array
  {
    //详情
    $mainFormData = [
      0 => [
        'label' => '基本信息',
        'dataItem' => [
          [
            'field' => 'type',
            'type' => 'radio',
            'title' => '订单类型',
            'isonly' => false,
            'required' => true,
            'config' => [
              'options' => self::$typeMap,
              'optionType' => 'button', // 按钮类型: default/button
              'buttonStyle' => 'solid',
              'editPermission' => 'add-only',
            ],
            'default' => '3',
          ],
          [
            'field' => 'project_number',
            'type' => 'apiSelect',
            'title' => '项目ID',
            'isonly' => false,
            'required' => true,
            'config' => [
              'url' => '/erp/pro/getProjectByCrm',
              'labelField' => 'id',
              'valueField' => 'id',
              'linkage' => [
                'engineVersion' => 'v2',
                'rules' => [
                  [
                    'id' => 'project_visibility_rule',
                    'target' => 'project_number',
                    'when' => [
                      ['field' => 'type', 'op' => 'in', 'value' => ['3', '27']]
                    ],
                    'action' => ['type' => 'show'],
                    'description' => '销售订单和服务订单显示项目字段'
                  ]
                ],
              ],
              'editPermission' => 'add-only',
              'linkageAssignment' => [
                'targetFields' => [
                  [
                    'field' => 'project_name',
                    'valueMapping' => 'title',
                    'clearOnEmpty' => true,
                  ],
                  [
                    'field' => 'client_id',
                    'valueMapping' => 'client_id',
                    'clearOnEmpty' => true,
                  ],
                ],
              ],
            ],
          ],
          [
            'field' => 'project_name',
            'type' => 'text',
            'title' => '项目名称',
            'isonly' => false,
            'required' => true,
            'config' => [
              'linkage' => [
                'engineVersion' => 'v2',
                'rules' => [
                  [
                    'id' => 'project_name_visibility_rule',
                    'target' => 'project_name',
                    'when' => [
                      ['field' => 'type', 'op' => 'in', 'value' => ['3', '27']]
                    ],
                    'action' => ['type' => 'show'],
                    'description' => '销售订单和服务订单显示项目名称字段'
                  ]
                ],
              ],
              'editPermission' => 'add-only',
            ],
          ],
          [
            'field' => 'source',
            'type' => 'text',
            'title' => '订单渠道',
            'isonly' => false,
            'required' => false,
            'config' => [
              'editPermission' => 'add-only',
            ],
          ],
          [
            'field' => 'source_uniqid',
            'type' => 'text',
            'title' => '订单渠道单号',
            'options' => [],
            'isonly' => true,
            'required' => true,
            'config' => [
              'editPermission' => 'add-only',
            ],
          ],
          [
            'field' => 'client_id',
            'type' => 'apiSelect',
            'title' => '客户',
            'isonly' => false,
            'required' => false,
            'config' => [
              'url' => '/erp/customermanage/getPageList',
              'params' => [],
              'labelField' => 'name',
              'valueField' => 'id',
              'placeholder' => '请选择人员',
              'allowClear' => true,
              'showSearch' => true,
              'maxTagCount' => 3,
              'multiple' => false,
              'searchFieldName' => 'name',
              'editPermission' => 'add-only',
              'returnParamsField' => 'id',
              'linkage' => [
                'engineVersion' => 'v2',
                'rules' => [
                  [
                    'id' => 'client_disable_rule',
                    'target' => 'client_id',
                    'when' => [
                      ['field' => 'type', 'op' => 'eq', 'value' => '2']
                    ],
                    'action' => ['type' => 'disable'],
                    'description' => '采购订单禁用客户字段'
                  ]
                ],
              ],
            ],
          ],
          [
            'field' => 'dept_id',
            'type' => 'apiTree',
            'title' => '部门',
            'isonly' => false,
            'required' => false,
            'config' => self::getDeptTreeConfig([], false, false, 'add-only'),
          ],
          [
            'field' => 'inCharge',
            'type' => 'apiSelect',
            'title' => '项目经理',
            'isonly' => false,
            'required' => false,
            'config' => self::getPersonConfig([], false, 'add-only'),
          ],
          [
            'field' => 'program_incharge',
            'type' => 'apiSelect',
            'title' => '方案经理',
            'isonly' => false,
            'required' => false,
            'config' => self::getPersonConfig([], false, 'add-only'),
          ],
          [
            'field' => 'delivery_incharge',
            'type' => 'apiSelect',
            'title' => '交付经理',
            'isonly' => false,
            'required' => false,
            'config' => self::getPersonConfig([], false, 'add-only'),
          ],
          [
            'field' => 'creator',
            'type' => 'apiSelect',
            'title' => '创建人',
            'isonly' => false,
            'required' => false,
            'config' => self::getPersonConfig([], false, 'add-only'),
          ],
          [
            'field' => 'currency',
            'type' => 'text',
            'title' => '货币',
            'options' => [],
            'isonly' => false,
            'default' => '人民币',
            'disabled' => true,
          ],
          [
            'field' => 'exchange_rate',
            'type' => 'apiSelect',
            'title' => '汇率',
            'isonly' => false,
            'required' => true,
            'config' => [
              'url' => '/erp/getRmbquot',
              'params' => [],
              'labelField' => 'name',
              'valueField' => 'fBuyPri',
              'placeholder' => '请选择',
              'allowClear' => true,
              'showSearch' => true,
              'multiple' => false,
              'searchFieldName' => 'name',
              'returnParamsField' => 'fBuyPri',
              'editPermission' => 'add-only',
              'afterFetch' => [
                'type' => 'formatOptions',
                'labelFormat' => '{name}-{fBuyPri}',
                'filterInactive' => false,
              ],
              'linkageAssignment' => [
                'targetFields' => [
                  [
                    'field' => 'currency',
                    'valueMapping' => 'name',
                    'clearOnEmpty' => true,
                  ],
                ],
              ],
              'readonly' => true,
            ],
            'default' => '1.000000',
          ],
          [
            'field' => 'receivable_left',
            'type' => 'calculated',
            'title' => '应收金额',
            'isonly' => false,
            'required' => true,
            'disabled' => true,
            'config' => [
              'readonly' => true,
              'prefix' => '¥',
              'calculation' => [
                'type' => 'formula',
                'formula' => 'IF(currency == "人民币", SUM(tableFields.total_amount), SUM(tableFields.foreign_currency_total) * exchange_rate)',
                'triggerFields' => ['currency', 'exchange_rate', 'item_request'],
                'tableFields' => ['total_amount', 'foreign_currency_total'],
                'precision' => 2,
                'defaultValue' => 0,
                'realtime' => true,
              ],
            ],
          ],
          [
            'field' => 'foreign_currency_amount',
            'type' => 'calculated',
            'title' => '外汇总金额',
            'isonly' => false,
            'required' => true,
            'disabled' => true,
            'config' => [
              'readonly' => true,
              'prefix' => '¥',
              'calculation' => [
                'type' => 'sum',
                'tableFields' => ['foreign_currency_total'],
                'triggerFields' => ['item_request'],
                'precision' => 2,
                'defaultValue' => 0,
                'realtime' => true,
              ],
            ],
          ],

          [
            'field' => 'submited_at',
            'type' => 'date',
            'title' => '开单日期',
            'isonly' => false,
            'required' => true,
            'config' => [
              'editPermission' => 'add-only',
            ],
          ],
          [
            'field' => 'country',
            'type' => 'text',
            'title' => '国家',
            'isonly' => false,
            'required' => true,
            'config' => [
              'editPermission' => 'add-only',
            ],
          ],
          [
            'field' => 'trade_methods',
            'type' => 'select',
            'title' => '贸易方式',
            'isonly' => false,
            'required' => true,
            'config' => [
              'options' => self::$deliveryMethodMap,
              'editPermission' => 'add-only'
            ],
          ],
          [
            'field' => 'remark',
            'type' => 'text',
            'title' => '备注',
            'isonly' => false,
            'required' => false,
          ],
          [
            'field' => 'files',
            'type' => 'Upload',
            'title' => '附件',
            'isonly' => false,
            'required' => false,
          ],
          [
            'field' => '_key',
            'type' => 'hidden',
            'title' => 'key',
            'default' => 'item_request',
            'ifShow' => true,
            'required' => false,
          ],
          [
            'field' => '_subkey',
            'type' => 'hidden',
            'title' => 'key',
            'default' => 'main',
            'ifShow' => false,
          ],
        ],
      ],
      1 => [
        'label' => '产品信息',
        'dataItem' => [
          //明细信息
          [
            'field' => 'item_request',
            'type' => 'edittable',
            'title' => '产品信息',
            'isonly' => false,
            'required' => true,
            'config' => [
              //表格列信息
              'columns' => [
                ['field' => 'name', 'type' => 'text', 'title' => '产品名称', 'isonly' => false, 'required' => false],
                ['field' => 'project_name', 'type' => 'text', 'title' => '单位', 'isonly' => false, 'required' => false],
                [
                  'field' => 'is_finish_split',
                  'type' => 'radio',
                  'title' => '是否完成产品拆分',
                  'required' => true,
                  'config' => [
                    'options' => self::$isYes,
                    'optionsColor' => self::$statusColorMap,
                    'allowClear' => true,
                    'showSearch' => false
                  ],
                ],
                // ['field' => 'unit_price_org', 'type' => 'text', 'title' => '原单价', 'isonly' => false, 'required' => false, 'config' => [ 'editPermission' => 'edit-only']],
                ['field' => 'unit_price', 'type' => 'number', 'title' => '单价', 'isonly' => false, 'required' => false],
                ['field' => 'foreign_currency_unit_pirce', 'type' => 'number', 'title' => '外汇单价', 'isonly' => false, 'required' => false],
                // ['field' => 'qty_request_org', 'type' => 'text', 'title' => '原数量', 'isonly' => false, 'required' => false, 'config' => [ 'editPermission' => 'edit-only']],
                // ['field' => 'qty_request', 'type' => 'number', 'title' => '数量', 'isonly' => false, 'required' => false],
                ['field' => 'qty_request_actual', 'type' => 'text', 'title' => '订单实际数量', 'isonly' => false, 'required' => false],
                ['field' => 'length', 'type' => 'number', 'title' => '长度(CM)', 'isonly' => false, 'required' => false],
                ['field' => 'width', 'type' => 'number', 'title' => '宽度(CM)', 'isonly' => false, 'required' => false],
                ['field' => 'height', 'type' => 'number', 'title' => '高度(CM)', 'isonly' => false, 'required' => false],
                ['field' => 'desc', 'type' => 'text', 'title' => '描述', 'isonly' => false, 'required' => false],
                ['field' => 'remark', 'type' => 'text', 'title' => '备注', 'isonly' => false, 'required' => false],
                ['field' => 'imgs', 'type' => 'files', 'title' => '产品图片', 'isonly' => false, 'required' => false],
                ['field' => 'puid', 'type' => 'text', 'title' => '产品编号', 'isonly' => false, 'required' => false],
                ['field' => 'uniqid', 'type' => 'text', 'title' => '产品唯一码', 'isonly' => false, 'required' => false],
                ['field' => 'batch_code', 'type' => 'text', 'title' => '批号', 'isonly' => false, 'required' => false],
                [
                  'field' => 'total_amount',
                  'type' => 'calculated',
                  'title' => '总价金额',
                  'isonly' => false,
                  'required' => false,
                  'disabled' => true,
                  'config' => [
                    'readonly' => true,
                    'calculation' => [
                      'type' => 'product',
                      'sourceFields' => ['unit_price', 'qty_request_actual'],
                      'triggerFields' => ['unit_price', 'qty_request_actual'],
                      'precision' => 2,
                      'defaultValue' => 0,
                    ],
                  ]
                ],
                ['field' => 'location_space', 'type' => 'text', 'title' => '所在空间', 'isonly' => false, 'required' => false, 'config' => ['editPermission' => 'edit-only']],
                [
                  'field' => 'actions',
                  'type' => 'actions',
                  'title' => '操作',
                  'isonly' => false,
                  'required' => false,
                  'options' => [
                    'actionColumnConfig' => [
                      'width' => 300, // 操作列宽度
                      'title' => '操作', // 操作列标题
                      'fixed' => 'right', // 固定在右侧
                      'align' => 'center', // 对齐方式
                    ],
                    'autoAddActionColumn' => true, // 是否自动添加操作列
                  ],
                  'actions' => [
                    [
                      'type' => 'edit',
                      'title' => '编辑',
                      'icon' => 'EditOutlined'
                    ],
                    [
                      'type' => 'delete',
                      'title' => '删除',
                      'icon' => 'DeleteOutlined',
                      'popconfirm' => [
                        'title' => '确定要删除这条记录吗？',
                        'okText' => '确定',
                        'cancelText' => '取消'
                      ]
                    ],
                    [
                      'type' => 'custom',
                      'title' => '复制',
                      'icon' => 'CopyOutlined',
                      'code' => 'copy_row'
                    ]
                  ]
                ],

              ],
              //按钮
              'tablist' => [
                ['title' => '导入', 'type' => 'import', 'key' => 'main', 'bgColor' => '#2D0AB1'],
                ['title' => '新增', 'type' => 'add', 'key' => 'main', 'bgColor' => '#2D0AB1'],
              ],
              //弹窗表单信息
              'form' => [
                [
                  'label' => '其他',
                  'dataItem' => [
                    [
                      'field' => 'name',
                      'type' => 'text',
                      'title' => '产品名称',
                      'isonly' => false,
                      'required' => false,
                    ],
                    [
                      'field' => 'project_name',
                      'type' => 'text',
                      'title' => '单位',
                      'isonly' => false,
                      'required' => false,
                    ],
                    [
                      'field' => 'is_finish_split',
                      'type' => 'radio',
                      'title' => '是否完成产品拆分',
                      'required' => true,
                      'config' => [
                        'options' => self::$isYes,
                        'optionsColor' => self::$statusColorMap,
                        'allowClear' => true,
                        'showSearch' => false,
                      ],
                    ],
                    [
                      'field' => 'unit_price',
                      'type' => 'number',
                      'title' => '单价',
                      'isonly' => false,
                      'required' => false,
                      'config' => [
                        'linkage' => [
                          'triggerFields' => ['currency'],
                          'rules' => [
                            'visibility' => [
                              'showWhen' => [
                                [
                                  'field' => 'currency',
                                  'operator' => 'equals',
                                  'value' => '人民币',
                                ],
                              ],
                              'hideWhen' => [
                                [
                                  'field' => 'currency',
                                  'operator' => '!=',
                                  'value' => '人民币',
                                ],
                              ],
                            ],
                          ],
                        ],
                      ],
                    ],
                    [
                      'field' => 'foreign_currency_unit_price',
                      'type' => 'number',
                      'title' => '外汇单价',
                      'isonly' => false,
                      'required' => false,
                      'config' => [
                        'linkage' => [
                          'engineVersion' => 'v2',
                          'rules' => [
                            [
                              'id' => 'foreign_unit_price_visibility',
                              'target' => 'foreign_currency_unit_price',
                              'when' => [
                                ['field' => 'currency', 'op' => 'ne', 'value' => '人民币']
                              ],
                              'action' => ['type' => 'show'],
                              'description' => '非人民币时显示外汇单价字段'
                            ]
                          ],
                        ],
                      ],
                    ],
                    [
                      'field' => 'qty_request_actual',
                      'type' => 'number',
                      'title' => '订单实际数量',
                      'isonly' => false,
                      'required' => false,
                    ],
                    [
                      'field' => 'length',
                      'type' => 'number',
                      'title' => '长度(CM)',
                      'isonly' => false,
                      'required' => false,
                    ],
                    [
                      'field' => 'width',
                      'type' => 'number',
                      'title' => '宽度(CM)',
                      'isonly' => false,
                      'required' => false,
                    ],
                    [
                      'field' => 'height',
                      'type' => 'number',
                      'title' => '高度(CM)',
                      'isonly' => false,
                      'required' => false,
                    ],
                    [
                      'field' => 'desc',
                      'type' => 'text',
                      'title' => '描述',
                      'isonly' => false,
                      'required' => false,
                    ],
                    [
                      'field' => 'remark',
                      'type' => 'text',
                      'title' => '备注',
                      'isonly' => false,
                      'required' => false,
                    ],
                    [
                      'field' => 'puid',
                      'type' => 'text',
                      'title' => '产品编号',
                      'isonly' => false,
                      'required' => false,
                    ],
                    [
                      'field' => 'uniqid',
                      'type' => 'text',
                      'title' => '产品唯一码',
                      'isonly' => false,
                      'required' => false,
                    ],
                    [
                      'field' => 'batch_code',
                      'type' => 'text',
                      'title' => '批号',
                      'isonly' => false,
                      'required' => false,
                    ],
                    [
                      'field' => 'total_amount',
                      'type' => 'calculated',
                      'title' => '总价金额',
                      'isonly' => false,
                      'required' => false,
                      'disabled' => true,
                      'config' => [
                        'calculation' => [
                          'type' => 'product',
                          'sourceFields' => ['unit_price', 'qty_request_actual'],
                          'triggerFields' => ['unit_price', 'qty_request_actual', 'currency'],
                          'precision' => 2,
                          'defaultValue' => 0,
                          'conditions' => [
                            [
                              'condition' => [
                                'field' => 'currency',
                                'operator' => 'equals',
                                'value' => '人民币',
                              ],
                              'calculation' => [
                                'type' => 'product',
                                'sourceFields' => ['unit_price', 'qty_request_actual'],
                                'precision' => 2,
                                'defaultValue' => 0,
                              ],
                            ],
                          ],
                        ],
                        'linkage' => [
                          'triggerFields' => ['currency'],
                          'rules' => [
                            'visibility' => [
                              'showWhen' => [
                                [
                                  'field' => 'currency',
                                  'operator' => 'equals',
                                  'value' => '人民币',
                                ],
                              ],
                              'hideWhen' => [
                                [
                                  'field' => 'currency',
                                  'operator' => '!=',
                                  'value' => '人民币',
                                ],
                              ],
                            ],
                          ],
                        ],
                      ],
                    ],
                    [
                      'field' => 'foreign_currency_total',
                      'type' => 'calculated',
                      'title' => '外汇总价',
                      'isonly' => false,
                      'required' => false,
                      'disabled' => true,
                      'config' => [
                        'calculation' => [
                          'type' => 'product',
                          'sourceFields' => ['foreign_currency_unit_price', 'qty_request_actual'],
                          'triggerFields' => ['foreign_currency_unit_price', 'qty_request_actual', 'currency'],
                          'precision' => 2,
                          'defaultValue' => 0,
                          'conditions' => [
                            [
                              'condition' => [
                                'field' => 'currency',
                                'operator' => '!=',
                                'value' => '人民币',
                              ],
                              'calculation' => [
                                'type' => 'product',
                                'sourceFields' => ['foreign_currency_unit_price', 'qty_request_actual'],
                                'precision' => 2,
                                'defaultValue' => 0,
                              ],
                            ],
                          ],
                        ],
                        'linkage' => [
                          'triggerFields' => ['currency'],
                          'rules' => [
                            'visibility' => [
                              'showWhen' => [
                                [
                                  'field' => 'currency',
                                  'operator' => '!=',
                                  'value' => '人民币',
                                ],
                              ],
                              'hideWhen' => [
                                [
                                  'field' => 'currency',
                                  'operator' => 'equals',
                                  'value' => '人民币',
                                ],
                              ],
                            ],
                          ],
                        ],
                      ],
                    ],
                    [
                      'field' => 'location_space',
                      'type' => 'text',
                      'title' => '所在空间',
                      'isonly' => false,
                      'required' => false,
                      'config' => [
                        'editPermission' => 'edit-only',
                      ],
                    ],
                    [
                      'field' => 'imgs',
                      'type' => 'Upload',
                      'title' => '产品图片',
                      'isonly' => false,
                      'required' => false,
                      'config' => [
                        'formItemClass' => 'col-span-3',
                      ],
                    ],
                  ],
                ],
                [
                  'label' => '其他',
                  'dataItem' => [],
                ],
              ],
              // 外部联动配置 - 使用联动引擎V2
              'externalLinkage' => [
                'engineVersion' => 'v2',
                'rules' => [
                  [
                    'id' => 'currency_foreign_price_visibility',
                    'target' => 'foreign_currency_unit_price',
                    'when' => [
                      ['field' => 'currency', 'op' => 'ne', 'value' => '人民币']
                    ],
                    'action' => ['type' => 'show'],
                    'description' => '非人民币时显示外汇单价'
                  ],
                  [
                    'id' => 'currency_rmb_total_visibility',
                    'target' => 'total_amount',
                    'when' => [
                      ['field' => 'currency', 'op' => 'eq', 'value' => '人民币']
                    ],
                    'action' => ['type' => 'show'],
                    'description' => '人民币时显示总价金额'
                  ],
                  [
                    'id' => 'currency_foreign_total_visibility',
                    'target' => 'foreign_currency_total',
                    'when' => [
                      ['field' => 'currency', 'op' => 'ne', 'value' => '人民币']
                    ],
                    'action' => ['type' => 'show'],
                    'description' => '非人民币时显示外汇总价'
                  ],
                  [
                    'id' => 'currency_unit_price_visibility',
                    'target' => 'unit_price',
                    'when' => [
                      ['field' => 'currency', 'op' => 'eq', 'value' => '人民币']
                    ],
                    'action' => ['type' => 'show'],
                    'description' => '人民币时显示单价'
                  ]
                ],
              ],
            ],
          ],
        ]
      ]
    ];

    $itemFormData = [
      0 => [
        'label' => '产品信息',
        'dataItem' => [
          //明细信息
          [
            'field' => 'item_request',
            'type' => 'edittable',
            'title' => '产品信息',
            'isonly' => false,
            'required' => true,
            'config' => [
              //表格列信息
              'columns' => [
                ['field' => 'name', 'type' => 'text', 'title' => '产品名称', 'isonly' => false, 'required' => false],
                ['field' => 'project_name', 'type' => 'text', 'title' => '单位', 'isonly' => false, 'required' => false],
                [
                  'field' => 'is_finish_split',
                  'type' => 'radio',
                  'title' => '是否完成产品拆分',
                  'required' => true,
                  'config' => [
                    'options' => self::$isYes,
                    'optionsColor' => self::$statusColorMap,
                    'allowClear' => true,
                    'showSearch' => false
                  ],
                ],
                // ['field' => 'unit_price_org', 'type' => 'number', 'title' => '原单价', 'isonly' => false, 'required' => false, 'config' => [ 'editPermission' => 'edit-only']],
                ['field' => 'unit_price', 'type' => 'number', 'title' => '单价', 'isonly' => false, 'required' => false],
                ['field' => 'foreign_currency_unit_pirce', 'type' => 'number', 'title' => '外汇单价', 'isonly' => false, 'required' => false],
                // ['field' => 'qty_request_org', 'type' => 'number', 'title' => '原数量', 'isonly' => false, 'required' => false, 'config' => [ 'editPermission' => 'edit-only']],
                // ['field' => 'qty_request', 'type' => 'number', 'title' => '数量', 'isonly' => false, 'required' => false],
                ['field' => 'qty_request_actual', 'type' => 'number', 'title' => '订单实际数量', 'isonly' => false, 'required' => false, 'config' => ['editPermission' => 'edit-only']],
                ['field' => 'length', 'type' => 'number', 'title' => '长度(CM)', 'isonly' => false, 'required' => false],
                ['field' => 'width', 'type' => 'number', 'title' => '宽度(CM)', 'isonly' => false, 'required' => false],
                ['field' => 'height', 'type' => 'number', 'title' => '高度(CM)', 'isonly' => false, 'required' => false],
                ['field' => 'desc', 'type' => 'text', 'title' => '描述', 'isonly' => false, 'required' => false],
                ['field' => 'remark', 'type' => 'text', 'title' => '备注', 'isonly' => false, 'required' => false],
                ['field' => 'imgs', 'type' => 'files', 'title' => '产品图片', 'isonly' => false, 'required' => false],
                ['field' => 'puid', 'type' => 'text', 'title' => '产品编号', 'isonly' => false, 'required' => false],
                ['field' => 'uniqid', 'type' => 'text', 'title' => '产品唯一码', 'isonly' => false, 'required' => false],
                ['field' => 'batch_code', 'type' => 'text', 'title' => '批号', 'isonly' => false, 'required' => false],
                [
                  'field' => 'total_amount',
                  'type' => 'calculated',
                  'title' => '总价金额',
                  'isonly' => false,
                  'required' => false,
                  'disabled' => true,
                  'config' => [
                    'calculation' => [
                      'type' => 'product',
                      'sourceFields' => ['unit_price', 'qty_request_actual'],
                      'triggerFields' => ['unit_price', 'qty_request_actual'],
                      'precision' => 2,
                      'defaultValue' => 0,
                    ],
                  ]
                ],
                ['field' => 'location_space', 'type' => 'text', 'title' => '所在空间', 'isonly' => false, 'required' => false, 'config' => ['editPermission' => 'edit-only']],
                [
                  'field' => 'actions',
                  'type' => 'actions',
                  'title' => '操作',
                  'isonly' => false,
                  'required' => false,
                  'options' => [
                    'actionColumnConfig' => [
                      'width' => 300, // 操作列宽度
                      'title' => '操作', // 操作列标题
                      'fixed' => 'right', // 固定在右侧
                      'align' => 'center', // 对齐方式
                    ],
                    'autoAddActionColumn' => true, // 是否自动添加操作列
                  ],
                  'actions' => [
                    [
                      'type' => 'edit',
                      'title' => '编辑',
                      'icon' => 'EditOutlined'
                    ],
                    [
                      'type' => 'delete',
                      'title' => '删除',
                      'icon' => 'DeleteOutlined',
                      'popconfirm' => [
                        'title' => '确定要删除这条记录吗？',
                        'okText' => '确定',
                        'cancelText' => '取消'
                      ]
                    ],
                    [
                      'type' => 'custom',
                      'title' => '复制',
                      'icon' => 'CopyOutlined',
                      'code' => 'copy_row'
                    ]
                  ]
                ],

              ],
              //按钮
              'tablist' => [
                ['title' => '导入', 'type' => 'import', 'key' => 'main', 'bgColor' => '#2D0AB1'],
                ['title' => '新增', 'type' => 'add', 'key' => 'main', 'bgColor' => '#2D0AB1'],
              ],
              //弹窗表单信息
              'form' => [
                0 => [
                  'label' => '其他',
                  'dataItem' => [
                    ['field' => 'name', 'type' => 'text', 'title' => '产品名称', 'isonly' => false, 'required' => false],
                    ['field' => 'project_name', 'type' => 'text', 'title' => '单位', 'isonly' => false, 'required' => false],
                    [
                      'field' => 'is_finish_split',
                      'type' => 'radio',
                      'title' => '是否完成产品拆分',
                      'required' => true,
                      'config' => [
                        'options' => self::$isYes,
                        'optionsColor' => self::$statusColorMap,
                        'allowClear' => true,
                        'showSearch' => false
                      ],
                    ],
                    // ['field' => 'unit_price_org', 'type' => 'number', 'title' => '原单价', 'isonly' => false, 'required' => false],
                    [
                      'field' => 'unit_price',
                      'type' => 'number',
                      'title' => '单价',
                      'isonly' => false,
                      'required' => false,
                      'config' => [
                        'linkage' => [
                          'engineVersion' => 'v2',
                          'rules' => [
                            [
                              'id' => 'popup_unit_price_visibility',
                              'target' => 'unit_price',
                              'when' => [
                                ['field' => 'currency', 'op' => 'eq', 'value' => '人民币']
                              ],
                              'action' => ['type' => 'show'],
                              'description' => '人民币时显示单价字段'
                            ]
                          ],
                        ],
                      ]
                    ],
                    [
                      'field' => 'foreign_currency_unit_pirce',
                      'type' => 'number',
                      'title' => '外汇单价',
                      'isonly' => false,
                      'required' => false,
                      'config' => [
                        'linkage' => [
                          'engineVersion' => 'v2',
                          'rules' => [
                            [
                              'id' => 'popup_foreign_price_visibility',
                              'target' => 'foreign_currency_unit_pirce',
                              'when' => [
                                ['field' => 'currency', 'op' => 'ne', 'value' => '人民币']
                              ],
                              'action' => ['type' => 'show'],
                              'description' => '非人民币时显示外汇单价字段'
                            ]
                          ],
                        ],
                      ]
                    ],
                    // ['field' => 'qty_request_org', 'type' => 'number', 'title' => '原数量', 'isonly' => false, 'required' => false],
                    // ['field' => 'qty_request', 'type' => 'number', 'title' => '数量', 'isonly' => false, 'required' => false],
                    ['field' => 'qty_request_actual', 'type' => 'number', 'title' => '订单实际数量', 'isonly' => false, 'required' => false],
                    ['field' => 'length', 'type' => 'number', 'title' => '长度(CM)', 'isonly' => false, 'required' => false],
                    ['field' => 'width', 'type' => 'number', 'title' => '宽度(CM)', 'isonly' => false, 'required' => false],
                    ['field' => 'height', 'type' => 'number', 'title' => '高度(CM)', 'isonly' => false, 'required' => false],
                    ['field' => 'desc', 'type' => 'text', 'title' => '描述', 'isonly' => false, 'required' => false],
                    ['field' => 'remark', 'type' => 'text', 'title' => '备注', 'isonly' => false, 'required' => false],
                    ['field' => 'puid', 'type' => 'text', 'title' => '产品编号', 'isonly' => false, 'required' => false],
                    ['field' => 'uniqid', 'type' => 'text', 'title' => '产品唯一码', 'isonly' => false, 'required' => false],
                    ['field' => 'batch_code', 'type' => 'text', 'title' => '批号', 'isonly' => false, 'required' => false],
                    [
                      'field' => 'total_amount',
                      'type' => 'calculated',
                      'title' => '总价金额',
                      'isonly' => false,
                      'required' => false,
                      'disabled' => true,
                      'config' => [
                        'calculation' => [
                          'type' => 'product',
                          'sourceFields' => ['unit_price', 'qty_request_actual'],
                          'triggerFields' => ['unit_price', 'qty_request_actual', 'currency'],
                          'precision' => 2,
                          'defaultValue' => 0,
                          'conditions' => [
                            [
                              'condition' => [
                                'field' => 'currency',
                                'operator' => 'equals',
                                'value' => '人民币',
                              ],
                              'calculation' => [
                                'type' => 'product',
                                'sourceFields' => ['unit_price', 'qty_request_actual'],
                                'precision' => 2,
                                'defaultValue' => 0,
                              ],
                            ],
                          ],
                        ],
                        'linkage' => [
                          'engineVersion' => 'v2',
                          'rules' => [
                            [
                              'id' => 'popup_total_amount_visibility',
                              'target' => 'total_amount',
                              'when' => [
                                ['field' => 'currency', 'op' => 'eq', 'value' => '人民币']
                              ],
                              'action' => ['type' => 'show'],
                              'description' => '人民币时显示总价金额字段'
                            ]
                          ],
                        ],
                      ]
                    ],
                    [
                      'field' => 'foreign_currency_total',
                      'type' => 'calculated',
                      'title' => '外汇总价',
                      'isonly' => false,
                      'required' => false,
                      'disabled' => true,
                      'config' => [
                        'calculation' => [
                          'type' => 'product',
                          'sourceFields' => ['foreign_currency_unit_pirce', 'qty_request_actual'],
                          'triggerFields' => ['foreign_currency_unit_pirce', 'qty_request_actual', 'currency'],
                          'precision' => 2,
                          'defaultValue' => 0,
                          'conditions' => [
                            [
                              'condition' => [
                                'field' => 'currency',
                                'operator' => '!=',
                                'value' => '人民币',
                              ],
                              'calculation' => [
                                'type' => 'product',
                                'sourceFields' => ['foreign_currency_unit_pirce', 'qty_request_actual'],
                                'precision' => 2,
                                'defaultValue' => 0,
                              ],
                            ],
                          ],
                        ],
                        'linkage' => [
                          'engineVersion' => 'v2',
                          'rules' => [
                            [
                              'id' => 'popup_foreign_total_visibility',
                              'target' => 'foreign_currency_total',
                              'when' => [
                                ['field' => 'currency', 'op' => 'ne', 'value' => '人民币']
                              ],
                              'action' => ['type' => 'show'],
                              'description' => '非人民币时显示外汇总价字段'
                            ]
                          ],
                        ],
                      ]
                    ],
                    ['field' => 'location_space', 'type' => 'text', 'title' => '所在空间', 'isonly' => false, 'required' => false],
                    [
                      'field' => 'imgs',
                      'type' => 'Upload',
                      'title' => '产品图片',
                      'isonly' => false,
                      'required' => false,
                      'config' => [
                        'formItemClass' => 'col-span-3', //独占一行
                      ]
                    ],

                  ],
                ],
                1 => [
                  'label' => '其他',
                  'dataItem' => [],
                ]
              ],
              // 外部联动配置 - 使用联动引擎V2
              'externalLinkage' => [
                'engineVersion' => 'v2',
                'rules' => [
                  [
                    'id' => 'item_foreign_price_visibility',
                    'target' => 'foreign_currency_unit_pirce',
                    'when' => [
                      ['field' => 'currency', 'op' => 'ne', 'value' => '人民币']
                    ],
                    'action' => ['type' => 'show'],
                    'description' => '非人民币时显示外汇单价'
                  ],
                  [
                    'id' => 'item_rmb_price_visibility',
                    'target' => 'unit_price',
                    'when' => [
                      ['field' => 'currency', 'op' => 'eq', 'value' => '人民币']
                    ],
                    'action' => ['type' => 'show'],
                    'description' => '人民币时显示单价'
                  ],
                  [
                    'id' => 'item_total_amount_visibility',
                    'target' => 'total_amount',
                    'when' => [
                      ['field' => 'currency', 'op' => 'eq', 'value' => '人民币']
                    ],
                    'action' => ['type' => 'show'],
                    'description' => '人民币时显示总价金额'
                  ],
                  [
                    'id' => 'item_foreign_total_visibility',
                    'target' => 'foreign_currency_total',
                    'when' => [
                      ['field' => 'currency', 'op' => 'ne', 'value' => '人民币']
                    ],
                    'action' => ['type' => 'show'],
                    'description' => '非人民币时显示外汇总价'
                  ]
                ],
              ],
            ],
          ],
        ]
      ]
    ];

    return [
      //详情
      'main' => $mainFormData,
      'itemRequest' => $itemFormData,
    ];
  }
}
