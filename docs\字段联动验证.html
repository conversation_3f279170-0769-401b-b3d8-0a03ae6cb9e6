<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>字段联动验证</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background-color: #f5f5f5;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .form-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
      }
      .form-group {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
      }
      .form-group label {
        width: 120px;
        font-weight: bold;
        margin-right: 10px;
      }
      .form-group input,
      .form-group select {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        min-width: 200px;
      }
      .hidden {
        display: none !important;
      }
      .visible {
        display: flex !important;
      }
      .status {
        margin-left: 10px;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
      }
      .status.show {
        background-color: #d4edda;
        color: #155724;
      }
      .status.hide {
        background-color: #f8d7da;
        color: #721c24;
      }
      .log {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 15px;
        max-height: 300px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
      }
      .log-entry {
        margin-bottom: 5px;
        padding: 2px 0;
      }
      .log-entry.info {
        color: #0066cc;
      }
      .log-entry.linkage {
        color: #28a745;
      }
      .log-entry.error {
        color: #dc3545;
      }
      h2 {
        color: #333;
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
      }
      h3 {
        color: #555;
        margin-top: 0;
      }
      .test-buttons {
        margin-bottom: 20px;
      }
      .btn {
        padding: 8px 16px;
        margin-right: 10px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }
      .btn-primary {
        background-color: #007bff;
        color: white;
      }
      .btn-success {
        background-color: #28a745;
        color: white;
      }
      .btn-secondary {
        background-color: #6c757d;
        color: white;
      }
      .field-status {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-left: 5px;
      }
      .field-status.visible {
        background-color: #28a745;
      }
      .field-status.hidden {
        background-color: #dc3545;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h2>🔗 字段联动验证工具</h2>

      <div class="test-buttons">
        <button class="btn btn-primary" onclick="testRMBMode()">
          测试人民币模式
        </button>
        <button class="btn btn-primary" onclick="testUSDMode()">
          测试美元模式
        </button>
        <button class="btn btn-success" onclick="runAllTests()">
          运行所有测试
        </button>
        <button class="btn btn-secondary" onclick="clearLog()">清空日志</button>
      </div>

      <!-- 主表单 -->
      <div class="form-section">
        <h3>主表单字段</h3>

        <div class="form-group">
          <label>货币类型:</label>
          <select id="currency" onchange="handleCurrencyChange()">
            <option value="人民币">人民币</option>
            <option value="美元">美元</option>
            <option value="欧元">欧元</option>
          </select>
          <span class="status show">始终显示</span>
        </div>

        <div class="form-group">
          <label>汇率:</label>
          <input type="number" id="exchange_rate" value="7.2" step="0.000001" />
          <span class="status show">始终显示</span>
        </div>

        <div class="form-group">
          <label>应收金额:</label>
          <input type="text" id="receivable_left" readonly value="0.00" />
          <span class="status show">始终显示 (¥)</span>
        </div>

        <div class="form-group">
          <label>外汇总金额:</label>
          <input
            type="text"
            id="foreign_currency_amount"
            readonly
            value="0.00"
          />
          <span class="status show">始终显示 (动态货币符号)</span>
        </div>
      </div>

      <!-- 表格数据 -->
      <div class="form-section">
        <h3>表格数据 (通过弹窗表单添加)</h3>

        <div style="margin-bottom: 15px">
          <button class="btn btn-success" onclick="addRowFromModal()">
            从弹窗添加数据到表格
          </button>
          <button class="btn btn-secondary" onclick="clearTable()">
            清空表格
          </button>
          <span style="margin-left: 15px; color: #666"
            >表格行数: <span id="rowCount">0</span></span
          >
        </div>

        <table
          style="width: 100%; border-collapse: collapse; border: 1px solid #ddd"
        >
          <thead>
            <tr style="background-color: #f8f9fa">
              <th style="border: 1px solid #ddd; padding: 8px">产品名称</th>
              <th
                style="border: 1px solid #ddd; padding: 8px"
                id="table_unit_price_header"
              >
                单价
              </th>
              <th
                style="border: 1px solid #ddd; padding: 8px; display: none"
                id="table_foreign_price_header"
              >
                外汇单价
              </th>
              <th style="border: 1px solid #ddd; padding: 8px">数量</th>
              <th style="border: 1px solid #ddd; padding: 8px">总价</th>
              <th style="border: 1px solid #ddd; padding: 8px">操作</th>
            </tr>
          </thead>
          <tbody id="tableBody">
            <tr>
              <td
                colspan="6"
                style="text-align: center; padding: 20px; color: #666"
              >
                暂无数据，请通过弹窗表单添加
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 弹窗表单字段 -->
      <div class="form-section">
        <h3>弹窗表单字段</h3>

        <div class="form-group" id="modal_unit_price_group">
          <label>单价:</label>
          <input
            type="number"
            id="modal_unit_price"
            value="200"
            onchange="calculateModalTotal()"
          />
          <span class="status" id="modal_unit_price_status">人民币时显示</span>
          <span class="field-status" id="modal_unit_price_indicator"></span>
        </div>

        <div class="form-group hidden" id="modal_foreign_price_group">
          <label>外汇单价:</label>
          <input
            type="number"
            id="modal_foreign_price"
            value="30"
            onchange="calculateModalTotal()"
          />
          <span class="status" id="modal_foreign_price_status"
            >非人民币时显示</span
          >
          <span class="field-status" id="modal_foreign_price_indicator"></span>
        </div>

        <div class="form-group">
          <label>数量:</label>
          <input
            type="number"
            id="modal_quantity"
            value="5"
            onchange="calculateModalTotal()"
          />
          <span class="status show">始终显示</span>
        </div>

        <div class="form-group">
          <label>总价:</label>
          <input type="text" id="modal_total_amount" readonly value="1000.00" />
          <span class="status show">始终显示 (根据货币类型计算)</span>
        </div>
      </div>

      <!-- 日志区域 -->
      <div class="form-section">
        <h3>联动日志</h3>
        <div class="log" id="testLog">
          <div class="log-entry info">等待测试...</div>
        </div>
      </div>
    </div>

    <script>
      // 日志记录器
      const logger = {
        log: function (message, type = 'info') {
          const logDiv = document.getElementById('testLog');
          const entry = document.createElement('div');
          entry.className = `log-entry ${type}`;
          entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
          logDiv.appendChild(entry);
          logDiv.scrollTop = logDiv.scrollHeight;
        },
        clear: function () {
          document.getElementById('testLog').innerHTML =
            '<div class="log-entry info">日志已清空</div>';
        },
      };

      // 货币符号映射
      const currencySymbols = {
        人民币: '¥',
        美元: '$',
        欧元: '€',
        英镑: '£',
        日元: '¥',
      };

      // 字段显示/隐藏控制
      function toggleField(fieldId, show, reason) {
        const group = document.getElementById(fieldId + '_group');
        const status = document.getElementById(fieldId + '_status');
        const indicator = document.getElementById(fieldId + '_indicator');

        if (group) {
          if (show) {
            group.classList.remove('hidden');
            group.classList.add('visible');
            if (status) {
              status.className = 'status show';
              status.textContent = '显示';
            }
            if (indicator) {
              indicator.className = 'field-status visible';
            }
          } else {
            group.classList.add('hidden');
            group.classList.remove('visible');
            if (status) {
              status.className = 'status hide';
              status.textContent = '隐藏';
            }
            if (indicator) {
              indicator.className = 'field-status hidden';
            }
          }
          logger.log(
            `${fieldId}: ${show ? '显示' : '隐藏'} - ${reason}`,
            'linkage',
          );
        }
      }

      // 更新货币前缀
      function updateCurrencyPrefix() {
        const currency = document.getElementById('currency').value;
        const symbol = currencySymbols[currency] || '';

        // 更新外汇总金额的显示
        const foreignAmountField = document.getElementById(
          'foreign_currency_amount',
        );
        if (foreignAmountField) {
          const value = foreignAmountField.value;
          // 这里可以添加前缀显示逻辑
          logger.log(`外汇总金额货币符号: ${symbol}`, 'info');
        }
      }

      // 货币变化处理
      function handleCurrencyChange() {
        const currency = document.getElementById('currency').value;
        const isRMB = currency === '人民币';

        logger.log(`货币类型变更为: ${currency}`, 'info');

        // 表格字段联动
        toggleField('unit_price', isRMB, '人民币时显示单价字段');
        toggleField('foreign_price', !isRMB, '非人民币时显示外汇单价字段');

        // 弹窗字段联动
        toggleField('modal_unit_price', isRMB, '人民币时显示弹窗单价字段');
        toggleField(
          'modal_foreign_price',
          !isRMB,
          '非人民币时显示弹窗外汇单价字段',
        );

        // 更新货币前缀
        updateCurrencyPrefix();

        // 重新渲染表格和计算
        renderTable();
        calculateModalTotal();
        updateMainFormTotals();
      }

      // 计算表格总价
      function calculateTotal() {
        const currency = document.getElementById('currency').value;
        const quantity =
          parseFloat(document.getElementById('quantity').value) || 0;
        let total = 0;

        if (currency === '人民币') {
          const unitPrice =
            parseFloat(document.getElementById('unit_price').value) || 0;
          total = unitPrice * quantity;
          logger.log(
            `表格计算(人民币): ${unitPrice} × ${quantity} = ${total.toFixed(2)}`,
            'info',
          );
        } else {
          const foreignPrice =
            parseFloat(document.getElementById('foreign_price').value) || 0;
          total = foreignPrice * quantity;
          logger.log(
            `表格计算(外汇): ${foreignPrice} × ${quantity} = ${total.toFixed(2)}`,
            'info',
          );
        }

        document.getElementById('total_amount').value = total.toFixed(2);
        updateMainFormTotals();
      }

      // 计算弹窗总价
      function calculateModalTotal() {
        const currency = document.getElementById('currency').value;
        const quantity =
          parseFloat(document.getElementById('modal_quantity').value) || 0;
        let total = 0;

        if (currency === '人民币') {
          const unitPrice =
            parseFloat(document.getElementById('modal_unit_price').value) || 0;
          total = unitPrice * quantity;
          logger.log(
            `弹窗计算(人民币): ${unitPrice} × ${quantity} = ${total.toFixed(2)}`,
            'info',
          );
        } else {
          const foreignPrice =
            parseFloat(document.getElementById('modal_foreign_price').value) ||
            0;
          total = foreignPrice * quantity;
          logger.log(
            `弹窗计算(外汇): ${foreignPrice} × ${quantity} = ${total.toFixed(2)}`,
            'info',
          );
        }

        document.getElementById('modal_total_amount').value = total.toFixed(2);
      }

      // 表格数据存储
      let tableData = [];
      let nextRowId = 1;

      // 从弹窗添加数据到表格
      function addRowFromModal() {
        const currency = document.getElementById('currency').value;
        const quantity =
          parseFloat(document.getElementById('modal_quantity').value) || 0;
        let unitPrice = 0;
        let total = 0;

        if (currency === '人民币') {
          unitPrice =
            parseFloat(document.getElementById('modal_unit_price').value) || 0;
          total = unitPrice * quantity;
        } else {
          unitPrice =
            parseFloat(document.getElementById('modal_foreign_price').value) ||
            0;
          total = unitPrice * quantity;
        }

        const rowData = {
          id: nextRowId++,
          name: `产品${nextRowId - 1}`,
          unitPrice: unitPrice,
          quantity: quantity,
          total: total,
          currency: currency,
        };

        tableData.push(rowData);
        renderTable();
        updateMainFormTotals();

        logger.log(
          `添加表格行: ${rowData.name}, 单价=${unitPrice}, 数量=${quantity}, 总价=${total.toFixed(2)}`,
          'info',
        );
      }

      // 删除表格行
      function deleteRow(id) {
        tableData = tableData.filter((row) => row.id !== id);
        renderTable();
        updateMainFormTotals();
        logger.log(`删除表格行: ID=${id}`, 'info');
      }

      // 清空表格
      function clearTable() {
        tableData = [];
        renderTable();
        updateMainFormTotals();
        logger.log('清空表格数据', 'info');
      }

      // 渲染表格
      function renderTable() {
        const tbody = document.getElementById('tableBody');
        const currency = document.getElementById('currency').value;
        const isRMB = currency === '人民币';

        // 更新表头显示
        document.getElementById('table_unit_price_header').style.display = isRMB
          ? ''
          : 'none';
        document.getElementById('table_foreign_price_header').style.display =
          isRMB ? 'none' : '';

        if (tableData.length === 0) {
          tbody.innerHTML = `
            <tr>
              <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                暂无数据，请通过弹窗表单添加
              </td>
            </tr>
          `;
        } else {
          tbody.innerHTML = tableData
            .map(
              (row) => `
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;">${row.name}</td>
              <td style="border: 1px solid #ddd; padding: 8px; ${isRMB ? '' : 'display: none;'}">${row.currency === '人民币' ? row.unitPrice.toFixed(2) : '-'}</td>
              <td style="border: 1px solid #ddd; padding: 8px; ${isRMB ? 'display: none;' : ''}">${row.currency !== '人民币' ? row.unitPrice.toFixed(2) : '-'}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${row.quantity}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${row.total.toFixed(2)}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">
                <button class="btn" style="padding: 4px 8px; font-size: 12px; background-color: #dc3545; color: white;" onclick="deleteRow(${row.id})">删除</button>
              </td>
            </tr>
          `,
            )
            .join('');
        }

        document.getElementById('rowCount').textContent = tableData.length;
      }

      // 更新主表单汇总
      function updateMainFormTotals() {
        const currency = document.getElementById('currency').value;
        const exchangeRate =
          parseFloat(document.getElementById('exchange_rate').value) || 1;

        // 计算表格总计
        let rmbTotal = 0;
        let foreignTotal = 0;

        tableData.forEach((row) => {
          if (row.currency === '人民币') {
            rmbTotal += row.total;
          } else {
            foreignTotal += row.total;
          }
        });

        if (currency === '人民币') {
          // 人民币模式：应收金额 = 人民币总计，外汇总金额 = 0
          document.getElementById('receivable_left').value =
            rmbTotal.toFixed(2);
          document.getElementById('foreign_currency_amount').value = '0.00';
          logger.log(
            `主表单汇总(人民币): 应收金额 = ${rmbTotal.toFixed(2)}`,
            'info',
          );
        } else {
          // 外汇模式：应收金额 = 外汇总计 × 汇率，外汇总金额 = 外汇总计
          const receivableAmount = foreignTotal * exchangeRate;
          document.getElementById('receivable_left').value =
            receivableAmount.toFixed(2);
          document.getElementById('foreign_currency_amount').value =
            foreignTotal.toFixed(2);
          logger.log(
            `主表单汇总(外汇): 外汇总额 = ${foreignTotal.toFixed(2)}, 应收金额 = ${receivableAmount.toFixed(2)}`,
            'info',
          );
        }
      }

      // 测试函数
      function testRMBMode() {
        logger.log('=== 开始测试人民币模式 ===', 'info');
        document.getElementById('currency').value = '人民币';
        handleCurrencyChange();

        setTimeout(() => {
          const unitPriceVisible = !document
            .getElementById('unit_price_group')
            .classList.contains('hidden');
          const foreignPriceHidden = document
            .getElementById('foreign_price_group')
            .classList.contains('hidden');

          if (unitPriceVisible && foreignPriceHidden) {
            logger.log(
              '✅ 人民币模式测试通过: 显示单价字段，隐藏外汇单价字段',
              'linkage',
            );
          } else {
            logger.log('❌ 人民币模式测试失败: 字段显示状态不正确', 'error');
          }
        }, 100);
      }

      function testUSDMode() {
        logger.log('=== 开始测试美元模式 ===', 'info');
        document.getElementById('currency').value = '美元';
        handleCurrencyChange();

        setTimeout(() => {
          const unitPriceHidden = document
            .getElementById('unit_price_group')
            .classList.contains('hidden');
          const foreignPriceVisible = !document
            .getElementById('foreign_price_group')
            .classList.contains('hidden');

          if (unitPriceHidden && foreignPriceVisible) {
            logger.log(
              '✅ 美元模式测试通过: 隐藏单价字段，显示外汇单价字段',
              'linkage',
            );
          } else {
            logger.log('❌ 美元模式测试失败: 字段显示状态不正确', 'error');
          }
        }, 100);
      }

      function runAllTests() {
        logger.clear();
        logger.log('🚀 开始运行所有测试...', 'info');

        testRMBMode();
        setTimeout(() => {
          testUSDMode();
          setTimeout(() => {
            // 测试表格功能
            logger.log('=== 测试表格功能 ===', 'info');

            // 清空表格
            clearTable();

            // 添加人民币数据
            document.getElementById('currency').value = '人民币';
            handleCurrencyChange();
            document.getElementById('modal_unit_price').value = '100';
            document.getElementById('modal_quantity').value = '5';
            calculateModalTotal();
            addRowFromModal();

            // 添加美元数据
            setTimeout(() => {
              document.getElementById('currency').value = '美元';
              handleCurrencyChange();
              document.getElementById('modal_foreign_price').value = '20';
              document.getElementById('modal_quantity').value = '3';
              calculateModalTotal();
              addRowFromModal();

              // 验证汇总计算
              setTimeout(() => {
                const foreignAmount = parseFloat(
                  document.getElementById('foreign_currency_amount').value,
                );
                const receivableAmount = parseFloat(
                  document.getElementById('receivable_left').value,
                );
                const exchangeRate = parseFloat(
                  document.getElementById('exchange_rate').value,
                );

                const expectedForeign = 60; // 20 * 3
                const expectedReceivable = expectedForeign * exchangeRate; // 60 * 7.2

                if (
                  Math.abs(foreignAmount - expectedForeign) < 0.01 &&
                  Math.abs(receivableAmount - expectedReceivable) < 0.01
                ) {
                  logger.log('✅ 表格汇总计算测试通过', 'linkage');
                } else {
                  logger.log('❌ 表格汇总计算测试失败', 'error');
                }

                logger.log('🎉 所有测试完成!', 'info');
              }, 500);
            }, 500);
          }, 1000);
        }, 1000);
      }

      function clearLog() {
        logger.clear();
      }

      // 页面加载完成后初始化
      document.addEventListener('DOMContentLoaded', function () {
        logger.log('字段联动验证工具初始化完成', 'info');
        renderTable(); // 初始化表格
        handleCurrencyChange(); // 初始化字段状态
        logger.log(
          '💡 提示: 使用弹窗表单添加数据到表格，然后观察主表单汇总计算',
          'info',
        );
      });
    </script>
  </body>
</html>
