/**
 * 表单转换性能监控工具
 * 提供实时性能监控、统计分析和性能报告功能
 */

/**
 * 性能指标接口
 */
interface PerformanceMetrics {
  executionTime: number; // 执行时间（毫秒）
  memoryUsage: number; // 内存使用量（字节）
  cacheHitRate: number; // 缓存命中率（%）
  objectPoolUtilization: number; // 对象池利用率（%）
  transformCount: number; // 转换次数
  errorCount: number; // 错误次数
  timestamp: number; // 时间戳
}

/**
 * 性能统计信息
 */
interface PerformanceStats {
  totalTransforms: number;
  averageExecutionTime: number;
  maxExecutionTime: number;
  minExecutionTime: number;
  totalMemoryUsed: number;
  averageCacheHitRate: number;
  averageObjectPoolUtilization: number;
  errorRate: number;
  throughput: number; // 每秒转换次数
}

/**
 * 性能阈值配置
 */
interface PerformanceThresholds {
  maxExecutionTime: number; // 最大执行时间（毫秒）
  maxMemoryUsage: number; // 最大内存使用量（字节）
  minCacheHitRate: number; // 最小缓存命中率（%）
  maxErrorRate: number; // 最大错误率（%）
}

/**
 * 性能警告类型
 */
type PerformanceWarningType =
  | 'cache_hit_rate'
  | 'error_rate'
  | 'execution_time'
  | 'memory_usage'
  | 'object_pool_exhaustion';

/**
 * 性能警告接口
 */
interface PerformanceWarning {
  type: PerformanceWarningType;
  message: string;
  value: number;
  threshold: number;
  timestamp: number;
  severity: 'critical' | 'high' | 'low' | 'medium';
}

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private isEnabled = true;
  private maxMetricsHistory = 1000; // 最大保存的指标历史数量
  private maxWarningsHistory = 100; // 最大保存的警告历史数量
  private metrics: PerformanceMetrics[] = [];
  // 默认性能阈值
  private thresholds: PerformanceThresholds = {
    maxExecutionTime: 100, // 100ms
    maxMemoryUsage: 50 * 1024 * 1024, // 50MB
    minCacheHitRate: 70, // 70%
    maxErrorRate: 5, // 5%
  };

  private warnings: PerformanceWarning[] = [];

  private constructor() {
    // 定期清理过期数据
    setInterval(
      () => {
        this.cleanup();
      },
      5 * 60 * 1000,
    ); // 每5分钟清理一次
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * 清除所有数据
   */
  clear(): void {
    this.metrics = [];
    this.warnings = [];
  }

  /**
   * 禁用性能监控
   */
  disable(): void {
    this.isEnabled = false;
  }

  /**
   * 启用性能监控
   */
  enable(): void {
    this.isEnabled = true;
  }

  /**
   * 导出性能数据
   */
  exportData(): {
    exportTime: number;
    metrics: PerformanceMetrics[];
    thresholds: PerformanceThresholds;
    warnings: PerformanceWarning[];
  } {
    return {
      metrics: [...this.metrics],
      warnings: [...this.warnings],
      thresholds: { ...this.thresholds },
      exportTime: Date.now(),
    };
  }

  /**
   * 生成性能报告
   */
  generateReport(timeRange?: { end: number; start: number }): {
    recommendations: string[];
    stats: PerformanceStats;
    warnings: PerformanceWarning[];
  } {
    const stats = this.getStats(timeRange);
    const warnings = timeRange
      ? this.warnings.filter(
          (w) => w.timestamp >= timeRange.start && w.timestamp <= timeRange.end,
        )
      : this.warnings;

    const recommendations: string[] = [];

    // 基于统计数据生成建议
    if (stats.averageExecutionTime > this.thresholds.maxExecutionTime) {
      recommendations.push('考虑优化转换算法或增加缓存策略以减少执行时间');
    }

    if (stats.averageCacheHitRate < this.thresholds.minCacheHitRate) {
      recommendations.push('缓存命中率较低，建议调整缓存策略或增加缓存容量');
    }

    if (stats.errorRate > this.thresholds.maxErrorRate) {
      recommendations.push('错误率较高，建议检查输入数据验证和错误处理逻辑');
    }

    if (stats.averageObjectPoolUtilization > 80) {
      recommendations.push('对象池利用率较高，建议增加对象池容量');
    }

    if (stats.throughput < 10) {
      recommendations.push('转换吞吐量较低，建议优化性能或考虑并行处理');
    }

    return {
      stats,
      warnings,
      recommendations,
    };
  }

  /**
   * 获取最近的警告
   */
  getRecentWarnings(limit = 10): PerformanceWarning[] {
    return this.warnings.slice(-limit).reverse();
  }

  /**
   * 获取性能统计信息
   */
  getStats(timeRange?: { end: number; start: number }): PerformanceStats {
    let filteredMetrics = this.metrics;

    if (timeRange) {
      filteredMetrics = this.metrics.filter(
        (m) => m.timestamp >= timeRange.start && m.timestamp <= timeRange.end,
      );
    }

    if (filteredMetrics.length === 0) {
      return {
        totalTransforms: 0,
        averageExecutionTime: 0,
        maxExecutionTime: 0,
        minExecutionTime: 0,
        totalMemoryUsed: 0,
        averageCacheHitRate: 0,
        averageObjectPoolUtilization: 0,
        errorRate: 0,
        throughput: 0,
      };
    }

    const executionTimes = filteredMetrics.map((m) => m.executionTime);
    const memoryUsages = filteredMetrics.map((m) => m.memoryUsage);
    const cacheHitRates = filteredMetrics.map((m) => m.cacheHitRate);
    const objectPoolUtilizations = filteredMetrics.map(
      (m) => m.objectPoolUtilization,
    );
    const errorCounts = filteredMetrics.map((m) => m.errorCount);
    const transformCounts = filteredMetrics.map((m) => m.transformCount);

    const totalTransforms = transformCounts.reduce(
      (sum, count) => sum + count,
      0,
    );
    const totalErrors = errorCounts.reduce((sum, count) => sum + count, 0);

    // 计算时间范围（秒）
    const timeRangeSeconds = timeRange
      ? (timeRange.end - timeRange.start) / 1000
      : filteredMetrics.length > 1
        ? (filteredMetrics[filteredMetrics.length - 1].timestamp -
            filteredMetrics[0].timestamp) /
          1000
        : 1;

    return {
      totalTransforms,
      averageExecutionTime:
        executionTimes.reduce((sum, time) => sum + time, 0) /
        executionTimes.length,
      maxExecutionTime: Math.max(...executionTimes),
      minExecutionTime: Math.min(...executionTimes),
      totalMemoryUsed: memoryUsages.reduce((sum, usage) => sum + usage, 0),
      averageCacheHitRate:
        cacheHitRates.reduce((sum, rate) => sum + rate, 0) /
        cacheHitRates.length,
      averageObjectPoolUtilization:
        objectPoolUtilizations.reduce((sum, util) => sum + util, 0) /
        objectPoolUtilizations.length,
      errorRate:
        totalTransforms > 0 ? (totalErrors / totalTransforms) * 100 : 0,
      throughput: totalTransforms / Math.max(timeRangeSeconds, 1),
    };
  }

  /**
   * 获取当前阈值配置
   */
  getThresholds(): PerformanceThresholds {
    return { ...this.thresholds };
  }

  /**
   * 获取指定严重程度的警告
   */
  getWarningsBySeverity(
    severity: 'critical' | 'high' | 'low' | 'medium',
  ): PerformanceWarning[] {
    return this.warnings.filter((w) => w.severity === severity);
  }

  /**
   * 获取指定类型的警告
   */
  getWarningsByType(type: PerformanceWarningType): PerformanceWarning[] {
    return this.warnings.filter((w) => w.type === type);
  }

  /**
   * 导入性能数据
   */
  importData(data: {
    metrics?: PerformanceMetrics[];
    thresholds?: PerformanceThresholds;
    warnings?: PerformanceWarning[];
  }): void {
    if (data.metrics) {
      this.metrics = [...data.metrics];
    }
    if (data.warnings) {
      this.warnings = [...data.warnings];
    }
    if (data.thresholds) {
      this.thresholds = { ...data.thresholds };
    }
  }

  /**
   * 记录性能指标
   */
  recordMetrics(metrics: Omit<PerformanceMetrics, 'timestamp'>): void {
    if (!this.isEnabled) return;

    const fullMetrics: PerformanceMetrics = {
      ...metrics,
      timestamp: Date.now(),
    };

    this.metrics.push(fullMetrics);

    // 限制历史记录数量
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.maxMetricsHistory);
    }

    // 检查性能阈值
    this.checkThresholds(fullMetrics);
  }

  /**
   * 设置性能阈值
   */
  setThresholds(thresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...thresholds };
  }

  /**
   * 检查性能阈值并生成警告
   */
  private checkThresholds(metrics: PerformanceMetrics): void {
    const warnings: PerformanceWarning[] = [];

    // 检查执行时间
    if (metrics.executionTime > this.thresholds.maxExecutionTime) {
      warnings.push({
        type: 'execution_time',
        message: `执行时间超过阈值: ${metrics.executionTime}ms > ${this.thresholds.maxExecutionTime}ms`,
        value: metrics.executionTime,
        threshold: this.thresholds.maxExecutionTime,
        timestamp: metrics.timestamp,
        severity: this.getSeverity(
          'execution_time',
          metrics.executionTime,
          this.thresholds.maxExecutionTime,
        ),
      });
    }

    // 检查内存使用量
    if (metrics.memoryUsage > this.thresholds.maxMemoryUsage) {
      warnings.push({
        type: 'memory_usage',
        message: `内存使用量超过阈值: ${this.formatBytes(metrics.memoryUsage)} > ${this.formatBytes(this.thresholds.maxMemoryUsage)}`,
        value: metrics.memoryUsage,
        threshold: this.thresholds.maxMemoryUsage,
        timestamp: metrics.timestamp,
        severity: this.getSeverity(
          'memory_usage',
          metrics.memoryUsage,
          this.thresholds.maxMemoryUsage,
        ),
      });
    }

    // 检查缓存命中率
    if (metrics.cacheHitRate < this.thresholds.minCacheHitRate) {
      warnings.push({
        type: 'cache_hit_rate',
        message: `缓存命中率低于阈值: ${metrics.cacheHitRate.toFixed(1)}% < ${this.thresholds.minCacheHitRate}%`,
        value: metrics.cacheHitRate,
        threshold: this.thresholds.minCacheHitRate,
        timestamp: metrics.timestamp,
        severity: this.getSeverity(
          'cache_hit_rate',
          metrics.cacheHitRate,
          this.thresholds.minCacheHitRate,
          true,
        ),
      });
    }

    // 检查对象池利用率
    if (metrics.objectPoolUtilization > 90) {
      warnings.push({
        type: 'object_pool_exhaustion',
        message: `对象池利用率过高: ${metrics.objectPoolUtilization.toFixed(1)}%`,
        value: metrics.objectPoolUtilization,
        threshold: 90,
        timestamp: metrics.timestamp,
        severity: this.getSeverity(
          'object_pool_exhaustion',
          metrics.objectPoolUtilization,
          90,
        ),
      });
    }

    // 添加警告到历史记录
    this.warnings.push(...warnings);

    // 限制警告历史记录数量
    if (this.warnings.length > this.maxWarningsHistory) {
      this.warnings = this.warnings.slice(-this.maxWarningsHistory);
    }

    // 输出警告到控制台
    warnings.forEach((warning) => {
      const logLevel =
        warning.severity === 'critical'
          ? 'error'
          : warning.severity === 'high'
            ? 'warn'
            : 'info';
      console[logLevel](`[Performance Warning] ${warning.message}`);
    });
  }

  /**
   * 清理过期数据
   */
  private cleanup(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时

    // 清理过期的指标数据
    this.metrics = this.metrics.filter((m) => now - m.timestamp < maxAge);

    // 清理过期的警告数据
    this.warnings = this.warnings.filter((w) => now - w.timestamp < maxAge);
  }

  /**
   * 格式化字节数
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  }

  /**
   * 获取警告严重程度
   */
  private getSeverity(
    type: PerformanceWarningType,
    value: number,
    threshold: number,
    isReverse = false, // 对于缓存命中率等指标，值越低越严重
  ): 'critical' | 'high' | 'low' | 'medium' {
    const ratio = isReverse ? threshold / value : value / threshold;

    if (ratio >= 3) return 'critical';
    if (ratio >= 2) return 'high';
    if (ratio >= 1.5) return 'medium';
    return 'low';
  }
}

// 导出全局实例
export const performanceMonitor = PerformanceMonitor.getInstance();

/**
 * 性能监控装饰器
 * 用于自动监控函数执行性能
 */
export function monitorPerformance<T extends (...args: any[]) => any>(
  target: T,
  options: {
    name?: string;
    trackErrors?: boolean;
    trackMemory?: boolean;
  } = {},
): T {
  const {
    name = target.name || 'anonymous',
    trackMemory = true,
    trackErrors = true,
  } = options;

  return ((...args: any[]) => {
    const startTime = performance.now();
    const startMemory =
      trackMemory && (performance as any).memory
        ? (performance as any).memory.usedJSHeapSize
        : 0;

    let errorCount = 0;
    const transformCount = 1;

    try {
      const result = target(...args);

      // 如果是Promise，等待完成后记录指标
      if (result && typeof result.then === 'function') {
        return result
          .then((value: any) => {
            recordMetrics();
            return value;
          })
          .catch((error: any) => {
            if (trackErrors) {
              errorCount = 1;
            }
            recordMetrics();
            throw error;
          });
      }

      recordMetrics();
      return result;
    } catch (error) {
      if (trackErrors) {
        errorCount = 1;
      }
      recordMetrics();
      throw error;
    }

    function recordMetrics() {
      const endTime = performance.now();
      const endMemory =
        trackMemory && (performance as any).memory
          ? (performance as any).memory.usedJSHeapSize
          : 0;

      performanceMonitor.recordMetrics({
        executionTime: endTime - startTime,
        memoryUsage: Math.max(0, endMemory - startMemory),
        cacheHitRate: 0, // 需要从外部传入
        objectPoolUtilization: 0, // 需要从外部传入
        transformCount,
        errorCount,
      });
    }
  }) as T;
}

/**
 * 创建性能监控中间件
 */
export function createPerformanceMiddleware() {
  return {
    beforeTransform: (data: any, options: any) => {
      // 在转换前记录开始时间
      return {
        startTime: performance.now(),
        startMemory: (performance as any).memory?.usedJSHeapSize || 0,
      };
    },

    afterTransform: (
      result: any,
      context: { startMemory: number; startTime: number },
      cacheStats?: { hitRate: number },
      poolStats?: { utilization: number },
    ) => {
      const endTime = performance.now();
      const endMemory = (performance as any).memory?.usedJSHeapSize || 0;

      performanceMonitor.recordMetrics({
        executionTime: endTime - context.startTime,
        memoryUsage: Math.max(0, endMemory - context.startMemory),
        cacheHitRate: cacheStats?.hitRate || 0,
        objectPoolUtilization: poolStats?.utilization || 0,
        transformCount: 1,
        errorCount: 0,
      });
    },

    onError: (
      error: any,
      context: { startMemory: number; startTime: number },
    ) => {
      const endTime = performance.now();
      const endMemory = (performance as any).memory?.usedJSHeapSize || 0;

      performanceMonitor.recordMetrics({
        executionTime: endTime - context.startTime,
        memoryUsage: Math.max(0, endMemory - context.startMemory),
        cacheHitRate: 0,
        objectPoolUtilization: 0,
        transformCount: 1,
        errorCount: 1,
      });
    },
  };
}
