<?php

namespace App\Http\Controllers\Api\V1\Order;

use App\Http\Controllers\Api\V1\BaseController;
use App\Models\Order\OrderRequest;
use Illuminate\Http\Request;

class OrderRequestController extends BaseController
{
    // 状态映射
    public static $statusMap = [
        0 => '待审核',
        1 => '已审核',
        2 => '已拒绝',
        3 => '已完成',
    ];

    // 是否选项
    public static $isYes = [
        0 => '否',
        1 => '是',
    ];

    // 状态颜色映射
    public static $statusColorMap = [
        0 => '#f50',
        1 => '#87d068',
        2 => '#f50',
        3 => '#108ee9',
    ];

    /**
     * 获取表单配置
     * @return array
     */
    public function getFormConfig()
    {
        $mainFormData = [
            0 => [
                'label' => '基本信息',
                'dataItem' => [
                    [
                        'field' => 'order_type',
                        'type' => 'radio',
                        'title' => '订单类型',
                        'required' => true,
                        'config' => [
                            'options' => [
                                'retail' => '零售订单',
                                'wholesale' => '批发订单',
                                'custom' => '定制订单',
                            ],
                            'optionsColor' => [
                                'retail' => '#87d068',
                                'wholesale' => '#108ee9',
                                'custom' => '#f50',
                            ],
                            'allowClear' => true,
                            'showSearch' => false,
                        ],
                    ],
                    [
                        'field' => 'project_number',
                        'type' => 'text',
                        'title' => '项目编号',
                        'isonly' => false,
                        'required' => false,
                        'config' => [
                            // V2联动配置：根据订单类型控制项目编号字段的显示/隐藏
                            'linkageV2' => [
                                [
                                    'id' => 'project_number_visibility',
                                    'triggerFields' => ['order_type'],
                                    'conditions' => [
                                        'type' => 'single',
                                        'field' => 'order_type',
                                        'operator' => 'in',
                                        'value' => ['wholesale', 'custom']
                                    ],
                                    'actions' => [
                                        [
                                            'type' => 'visibility',
                                            'targetField' => 'project_number',
                                            'show' => true
                                        ]
                                    ],
                                    'elseActions' => [
                                        [
                                            'type' => 'visibility',
                                            'targetField' => 'project_number',
                                            'show' => false
                                        ]
                                    ]
                                ]
                            ]
                        ],
                    ],
                    [
                        'field' => 'client_id',
                        'type' => 'apiSelect',
                        'title' => '客户',
                        'required' => true,
                        'config' => [
                            'url' => '/api/v1/client/list',
                            'labelField' => 'name',
                            'valueField' => 'id',
                            'allowClear' => true,
                            'showSearch' => true,
                            // V2联动配置：根据订单类型控制客户字段的启用/禁用
                            'linkageV2' => [
                                [
                                    'id' => 'client_id_disabled',
                                    'triggerFields' => ['order_type'],
                                    'conditions' => [
                                        'type' => 'single',
                                        'field' => 'order_type',
                                        'operator' => 'equals',
                                        'value' => 'custom'
                                    ],
                                    'actions' => [
                                        [
                                            'type' => 'disabled',
                                            'targetField' => 'client_id',
                                            'disabled' => true
                                        ]
                                    ],
                                    'elseActions' => [
                                        [
                                            'type' => 'disabled',
                                            'targetField' => 'client_id',
                                            'disabled' => false
                                        ]
                                    ]
                                ]
                            ]
                        ],
                    ],
                    [
                        'field' => 'currency',
                        'type' => 'radio',
                        'title' => '币种',
                        'required' => true,
                        'config' => [
                            'options' => [
                                '人民币' => '人民币',
                                '美元' => '美元',
                                '欧元' => '欧元',
                            ],
                            'optionsColor' => [
                                '人民币' => '#87d068',
                                '美元' => '#108ee9',
                                '欧元' => '#f50',
                            ],
                            'allowClear' => true,
                            'showSearch' => false,
                        ],
                    ],
                    [
                        'field' => 'exchange_rate',
                        'type' => 'apiSelect',
                        'title' => '汇率',
                        'required' => false,
                        'config' => [
                            'url' => '/api/v1/exchange-rate/list',
                            'labelField' => 'rate_display',
                            'valueField' => 'rate',
                            'allowClear' => true,
                            'showSearch' => true,
                            // V2联动配置：选择汇率时自动赋值相关字段
                            'linkageV2' => [
                                [
                                    'id' => 'exchange_rate_assignment',
                                    'triggerFields' => ['exchange_rate'],
                                    'conditions' => [
                                        'type' => 'single',
                                        'field' => 'exchange_rate',
                                        'operator' => 'not_empty',
                                        'value' => null
                                    ],
                                    'actions' => [
                                        [
                                            'type' => 'setValue',
                                            'targetField' => 'currency_rate',
                                            'value' => '{{exchange_rate}}',
                                            'valueType' => 'field'
                                        ],
                                        [
                                            'type' => 'setValue',
                                            'targetField' => 'rate_date',
                                            'value' => '{{current_date}}',
                                            'valueType' => 'function'
                                        ]
                                    ]
                                ]
                            ]
                        ],
                    ],
                    [
                        'field' => 'currency_rate',
                        'type' => 'number',
                        'title' => '汇率值',
                        'isonly' => false,
                        'required' => false,
                        'config' => [
                            'precision' => 4,
                            'min' => 0,
                        ],
                    ],
                    [
                        'field' => 'rate_date',
                        'type' => 'date',
                        'title' => '汇率日期',
                        'isonly' => false,
                        'required' => false,
                    ],
                    [
                        'field' => 'total_receivable',
                        'type' => 'calculated',
                        'title' => '应收金额',
                        'isonly' => false,
                        'required' => false,
                        'disabled' => true,
                        'config' => [
                            // V2计算字段配置
                            'calculationV2' => [
                                'id' => 'total_receivable_calc',
                                'type' => 'sum',
                                'sourceFields' => ['item_request.*.total_amount'],
                                'triggerFields' => ['item_request'],
                                'precision' => 2,
                                'defaultValue' => 0,
                                'conditions' => [
                                    [
                                        'condition' => [
                                            'type' => 'single',
                                            'field' => 'currency',
                                            'operator' => 'equals',
                                            'value' => '人民币'
                                        ],
                                        'calculation' => [
                                            'type' => 'sum',
                                            'sourceFields' => ['item_request.*.total_amount'],
                                            'precision' => 2
                                        ]
                                    ]
                                ]
                            ]
                        ],
                    ],
                    [
                        'field' => 'foreign_currency_total',
                        'type' => 'calculated',
                        'title' => '外汇总金额',
                        'isonly' => false,
                        'required' => false,
                        'disabled' => true,
                        'config' => [
                            // V2计算字段配置
                            'calculationV2' => [
                                'id' => 'foreign_currency_total_calc',
                                'type' => 'sum',
                                'sourceFields' => ['item_request.*.foreign_currency_total'],
                                'triggerFields' => ['item_request'],
                                'precision' => 2,
                                'defaultValue' => 0,
                                'conditions' => [
                                    [
                                        'condition' => [
                                            'type' => 'single',
                                            'field' => 'currency',
                                            'operator' => 'not_equals',
                                            'value' => '人民币'
                                        ],
                                        'calculation' => [
                                            'type' => 'sum',
                                            'sourceFields' => ['item_request.*.foreign_currency_total'],
                                            'precision' => 2
                                        ]
                                    ]
                                ]
                            ]
                        ],
                    ],
                    [
                        'field' => 'rmb_equivalent',
                        'type' => 'calculated',
                        'title' => '人民币等值',
                        'isonly' => false,
                        'required' => false,
                        'disabled' => true,
                        'config' => [
                            // V2计算字段配置
                            'calculationV2' => [
                                'id' => 'rmb_equivalent_calc',
                                'type' => 'product',
                                'sourceFields' => ['foreign_currency_total', 'currency_rate'],
                                'triggerFields' => ['foreign_currency_total', 'currency_rate'],
                                'precision' => 2,
                                'defaultValue' => 0,
                                'conditions' => [
                                    [
                                        'condition' => [
                                            'type' => 'and',
                                            'conditions' => [
                                                [
                                                    'type' => 'single',
                                                    'field' => 'currency',
                                                    'operator' => 'not_equals',
                                                    'value' => '人民币'
                                                ],
                                                [
                                                    'type' => 'single',
                                                    'field' => 'currency_rate',
                                                    'operator' => 'greater_than',
                                                    'value' => 0
                                                ]
                                            ]
                                        ],
                                        'calculation' => [
                                            'type' => 'product',
                                            'sourceFields' => ['foreign_currency_total', 'currency_rate'],
                                            'precision' => 2
                                        ]
                                    ]
                                ]
                            ]
                        ],
                    ],
                ],
            ],
            1 => [
                'label' => '产品信息',
                'dataItem' => [
                    //明细信息
                    [
                        'field' => 'item_request',
                        'type' => 'edittable',
                        'title' => '产品信息',
                        'isonly' => false,
                        'required' => true,
                        'config' => [
                            //表格列信息
                            'columns' => [
                                ['field' => 'name', 'type' => 'text', 'title' => '产品名称', 'isonly' => false, 'required' => false],
                                ['field' => 'project_name', 'type' => 'text', 'title' => '单位', 'isonly' => false, 'required' => false],
                                [
                                    'field' => 'is_finish_split',
                                    'type' => 'radio',
                                    'title' => '是否完成产品拆分',
                                    'required' => true,
                                    'config' => [
                                        'options' => self::$isYes,
                                        'optionsColor' => self::$statusColorMap,
                                        'allowClear' => true,
                                        'showSearch' => false,
                                    ],
                                ],
                                [
                                    'field' => 'unit_price',
                                    'type' => 'number',
                                    'title' => '单价',
                                    'isonly' => false,
                                    'required' => false,
                                    'config' => [
                                        // V2联动配置：根据币种控制单价字段的显示/隐藏
                                        'linkageV2' => [
                                            [
                                                'id' => 'unit_price_visibility',
                                                'triggerFields' => ['currency'],
                                                'conditions' => [
                                                    'type' => 'single',
                                                    'field' => 'currency',
                                                    'operator' => 'equals',
                                                    'value' => '人民币'
                                                ],
                                                'actions' => [
                                                    [
                                                        'type' => 'visibility',
                                                        'targetField' => 'unit_price',
                                                        'show' => true
                                                    ]
                                                ],
                                                'elseActions' => [
                                                    [
                                                        'type' => 'visibility',
                                                        'targetField' => 'unit_price',
                                                        'show' => false
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ],
                                ],
                                [
                                    'field' => 'foreign_currency_unit_price',
                                    'type' => 'number',
                                    'title' => '外汇单价',
                                    'isonly' => false,
                                    'required' => false,
                                    'config' => [
                                        // V2联动配置：根据币种控制外汇单价字段的显示/隐藏
                                        'linkageV2' => [
                                            [
                                                'id' => 'foreign_currency_unit_price_visibility',
                                                'triggerFields' => ['currency'],
                                                'conditions' => [
                                                    'type' => 'single',
                                                    'field' => 'currency',
                                                    'operator' => 'not_equals',
                                                    'value' => '人民币'
                                                ],
                                                'actions' => [
                                                    [
                                                        'type' => 'visibility',
                                                        'targetField' => 'foreign_currency_unit_price',
                                                        'show' => true
                                                    ]
                                                ],
                                                'elseActions' => [
                                                    [
                                                        'type' => 'visibility',
                                                        'targetField' => 'foreign_currency_unit_price',
                                                        'show' => false
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ],
                                ],
                                ['field' => 'qty_request_actual', 'type' => 'number', 'title' => '订单实际数量', 'isonly' => false, 'required' => false],
                                ['field' => 'length', 'type' => 'number', 'title' => '长度(CM)', 'isonly' => false, 'required' => false],
                                ['field' => 'width', 'type' => 'number', 'title' => '宽度(CM)', 'isonly' => false, 'required' => false],
                                ['field' => 'height', 'type' => 'number', 'title' => '高度(CM)', 'isonly' => false, 'required' => false],
                                ['field' => 'desc', 'type' => 'text', 'title' => '描述', 'isonly' => false, 'required' => false],
                                ['field' => 'remark', 'type' => 'text', 'title' => '备注', 'isonly' => false, 'required' => false],
                                ['field' => 'imgs', 'type' => 'files', 'title' => '产品图片', 'isonly' => false, 'required' => false],
                                ['field' => 'puid', 'type' => 'text', 'title' => '产品编号', 'isonly' => false, 'required' => false],
                                ['field' => 'uniqid', 'type' => 'text', 'title' => '产品唯一码', 'isonly' => false, 'required' => false],
                                ['field' => 'batch_code', 'type' => 'text', 'title' => '批号', 'isonly' => false, 'required' => false],
                                [
                                    'field' => 'total_amount',
                                    'type' => 'calculated',
                                    'title' => '总价金额',
                                    'isonly' => false,
                                    'required' => false,
                                    'disabled' => true,
                                    'config' => [
                                        // V2计算字段配置
                                        'calculationV2' => [
                                            'id' => 'total_amount_calc',
                                            'type' => 'product',
                                            'sourceFields' => ['unit_price', 'qty_request_actual'],
                                            'triggerFields' => ['unit_price', 'qty_request_actual'],
                                            'precision' => 2,
                                            'defaultValue' => 0,
                                        ],
                                    ]
                                ],
                                ['field' => 'location_space', 'type' => 'text', 'title' => '所在空间', 'isonly' => false, 'required' => false, 'config' => ['editPermission' => 'edit-only']],
                                [
                                    'field' => 'actions',
                                    'type' => 'actions',
                                    'title' => '操作',
                                    'isonly' => false,
                                    'required' => false,
                                    'options' => [
                                        'actionColumnConfig' => [
                                            'width' => 300,           // 操作列宽度
                                            'title' => '操作',        // 操作列标题
                                            'fixed' => 'right',       // 固定在右侧
                                            'align' => 'center',      // 对齐方式
                                        ],
                                        'autoAddActionColumn' => true,  // 是否自动添加操作列
                                    ],
                                    'actions' => [
                                        [
                                            'type' => 'edit',
                                            'title' => '编辑',
                                            'icon' => 'EditOutlined'
                                        ],
                                        [
                                            'type' => 'delete',
                                            'title' => '删除',
                                            'icon' => 'DeleteOutlined',
                                            'popconfirm' => [
                                                'title' => '确定要删除这条记录吗？',
                                                'okText' => '确定',
                                                'cancelText' => '取消'
                                            ]
                                        ],
                                        [
                                            'type' => 'custom',
                                            'title' => '复制',
                                            'icon' => 'CopyOutlined',
                                            'code' => 'copy_row'
                                        ]
                                    ]
                                ],

                            ],
                            //按钮
                            'tablist' => [
                                ['title' => '导入', 'type' => 'import', 'key' => 'main', 'bgColor' => '#2D0AB1'],
                                ['title' => '新增', 'type' => 'add', 'key' => 'main', 'bgColor' => '#2D0AB1'],
                            ],
                            //弹窗表单信息
                            'form' => [
                                [
                                    'label' => '其他',
                                    'dataItem' => [
                                        [
                                            'field' => 'name',
                                            'type' => 'text',
                                            'title' => '产品名称',
                                            'isonly' => false,
                                            'required' => false,
                                        ],
                                        [
                                            'field' => 'project_name',
                                            'type' => 'text',
                                            'title' => '单位',
                                            'isonly' => false,
                                            'required' => false,
                                        ],
                                        [
                                            'field' => 'is_finish_split',
                                            'type' => 'radio',
                                            'title' => '是否完成产品拆分',
                                            'required' => true,
                                            'config' => [
                                                'options' => self::$isYes,
                                                'optionsColor' => self::$statusColorMap,
                                                'allowClear' => true,
                                                'showSearch' => false,
                                            ],
                                        ],
                                        [
                                            'field' => 'unit_price',
                                            'type' => 'number',
                                            'title' => '单价',
                                            'isonly' => false,
                                            'required' => false,
                                            'config' => [
                                                // V2联动配置：根据币种控制单价字段的显示/隐藏
                                                'linkageV2' => [
                                                    [
                                                        'id' => 'unit_price_visibility_form',
                                                        'triggerFields' => ['currency'],
                                                        'conditions' => [
                                                            'type' => 'single',
                                                            'field' => 'currency',
                                                            'operator' => 'equals',
                                                            'value' => '人民币'
                                                        ],
                                                        'actions' => [
                                                            [
                                                                'type' => 'visibility',
                                                                'targetField' => 'unit_price',
                                                                'show' => true
                                                            ]
                                                        ],
                                                        'elseActions' => [
                                                            [
                                                                'type' => 'visibility',
                                                                'targetField' => 'unit_price',
                                                                'show' => false
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ],
                                        ],
                                        [
                                            'field' => 'foreign_currency_unit_price',
                                            'type' => 'number',
                                            'title' => '外汇单价',
                                            'isonly' => false,
                                            'required' => false,
                                            'config' => [
                                                // V2联动配置：根据币种控制外汇单价字段的显示/隐藏
                                                'linkageV2' => [
                                                    [
                                                        'id' => 'foreign_currency_unit_price_visibility_form',
                                                        'triggerFields' => ['currency'],
                                                        'conditions' => [
                                                            'type' => 'single',
                                                            'field' => 'currency',
                                                            'operator' => 'not_equals',
                                                            'value' => '人民币'
                                                        ],
                                                        'actions' => [
                                                            [
                                                                'type' => 'visibility',
                                                                'targetField' => 'foreign_currency_unit_price',
                                                                'show' => true
                                                            ]
                                                        ],
                                                        'elseActions' => [
                                                            [
                                                                'type' => 'visibility',
                                                                'targetField' => 'foreign_currency_unit_price',
                                                                'show' => false
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ],
                                        ],
                                        [
                                            'field' => 'qty_request_actual',
                                            'type' => 'number',
                                            'title' => '订单实际数量',
                                            'isonly' => false,
                                            'required' => false,
                                        ],
                                        [
                                            'field' => 'length',
                                            'type' => 'number',
                                            'title' => '长度(CM)',
                                            'isonly' => false,
                                            'required' => false,
                                        ],
                                        [
                                            'field' => 'width',
                                            'type' => 'number',
                                            'title' => '宽度(CM)',
                                            'isonly' => false,
                                            'required' => false,
                                        ],
                                        [
                                            'field' => 'height',
                                            'type' => 'number',
                                            'title' => '高度(CM)',
                                            'isonly' => false,
                                            'required' => false,
                                        ],
                                        [
                                            'field' => 'desc',
                                            'type' => 'text',
                                            'title' => '描述',
                                            'isonly' => false,
                                            'required' => false,
                                        ],
                                        [
                                            'field' => 'remark',
                                            'type' => 'text',
                                            'title' => '备注',
                                            'isonly' => false,
                                            'required' => false,
                                        ],
                                        [
                                            'field' => 'puid',
                                            'type' => 'text',
                                            'title' => '产品编号',
                                            'isonly' => false,
                                            'required' => false,
                                        ],
                                        [
                                            'field' => 'uniqid',
                                            'type' => 'text',
                                            'title' => '产品唯一码',
                                            'isonly' => false,
                                            'required' => false,
                                        ],
                                        [
                                            'field' => 'batch_code',
                                            'type' => 'text',
                                            'title' => '批号',
                                            'isonly' => false,
                                            'required' => false,
                                        ],
                                        [
                                            'field' => 'total_amount',
                                            'type' => 'calculated',
                                            'title' => '总价金额',
                                            'isonly' => false,
                                            'required' => false,
                                            'disabled' => true,
                                            'config' => [
                                                // V2计算字段配置
                                                'calculationV2' => [
                                                    'id' => 'total_amount_calc_form',
                                                    'type' => 'product',
                                                    'sourceFields' => ['unit_price', 'qty_request_actual'],
                                                    'triggerFields' => ['unit_price', 'qty_request_actual', 'currency'],
                                                    'precision' => 2,
                                                    'defaultValue' => 0,
                                                    'conditions' => [
                                                        [
                                                            'condition' => [
                                                                'type' => 'single',
                                                                'field' => 'currency',
                                                                'operator' => 'equals',
                                                                'value' => '人民币'
                                                            ],
                                                            'calculation' => [
                                                                'type' => 'product',
                                                                'sourceFields' => ['unit_price', 'qty_request_actual'],
                                                                'precision' => 2,
                                                                'defaultValue' => 0
                                                            ]
                                                        ]
                                                    ]
                                                ],
                                                // V2联动配置：根据币种控制总价金额字段的显示/隐藏
                                                'linkageV2' => [
                                                    [
                                                        'id' => 'total_amount_visibility_form',
                                                        'triggerFields' => ['currency'],
                                                        'conditions' => [
                                                            'type' => 'single',
                                                            'field' => 'currency',
                                                            'operator' => 'equals',
                                                            'value' => '人民币'
                                                        ],
                                                        'actions' => [
                                                            [
                                                                'type' => 'visibility',
                                                                'targetField' => 'total_amount',
                                                                'show' => true
                                                            ]
                                                        ],
                                                        'elseActions' => [
                                                            [
                                                                'type' => 'visibility',
                                                                'targetField' => 'total_amount',
                                                                'show' => false
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ],
                                        ],
                                        [
                                            'field' => 'foreign_currency_total',
                                            'type' => 'calculated',
                                            'title' => '外汇总价',
                                            'isonly' => false,
                                            'required' => false,
                                            'disabled' => true,
                                            'config' => [
                                                // V2计算字段配置
                                                'calculationV2' => [
                                                    'id' => 'foreign_currency_total_calc_form',
                                                    'type' => 'product',
                                                    'sourceFields' => ['foreign_currency_unit_price', 'qty_request_actual'],
                                                    'triggerFields' => ['foreign_currency_unit_price', 'qty_request_actual', 'currency'],
                                                    'precision' => 2,
                                                    'defaultValue' => 0,
                                                    'conditions' => [
                                                        [
                                                            'condition' => [
                                                                'type' => 'single',
                                                                'field' => 'currency',
                                                                'operator' => 'not_equals',
                                                                'value' => '人民币'
                                                            ],
                                                            'calculation' => [
                                                                'type' => 'product',
                                                                'sourceFields' => ['foreign_currency_unit_price', 'qty_request_actual'],
                                                                'precision' => 2,
                                                                'defaultValue' => 0
                                                            ]
                                                        ]
                                                    ]
                                                ],
                                                // V2联动配置：根据币种控制外汇总价字段的显示/隐藏
                                                'linkageV2' => [
                                                    [
                                                        'id' => 'foreign_currency_total_visibility_form',
                                                        'triggerFields' => ['currency'],
                                                        'conditions' => [
                                                            'type' => 'single',
                                                            'field' => 'currency',
                                                            'operator' => 'not_equals',
                                                            'value' => '人民币'
                                                        ],
                                                        'actions' => [
                                                            [
                                                                'type' => 'visibility',
                                                                'targetField' => 'foreign_currency_total',
                                                                'show' => true
                                                            ]
                                                        ],
                                                        'elseActions' => [
                                                            [
                                                                'type' => 'visibility',
                                                                'targetField' => 'foreign_currency_total',
                                                                'show' => false
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ],
                                        ],
                                        [
                                            'field' => 'location_space',
                                            'type' => 'text',
                                            'title' => '所在空间',
                                            'isonly' => false,
                                            'required' => false,
                                            'config' => [
                                                'editPermission' => 'edit-only',
                                            ],
                                        ],
                                        [
                                            'field' => 'imgs',
                                            'type' => 'Upload',
                                            'title' => '产品图片',
                                            'isonly' => false,
                                            'required' => false,
                                            'config' => [
                                                'formItemClass' => 'col-span-3',
                                            ],
                                        ],
                                    ],
                                ],
                                [
                                    'label' => '其他',
                                    'dataItem' => [],
                                ],
                            ],
                            // V2外部联动配置
                            'externalLinkageV2' => [
                                [
                                    'id' => 'currency_external_linkage',
                                    'triggerFields' => ['currency'],
                                    'conditions' => [
                                        'type' => 'single',
                                        'field' => 'currency',
                                        'operator' => 'not_equals',
                                        'value' => '人民币'
                                    ],
                                    'actions' => [
                                        [
                                            'type' => 'visibility',
                                            'targetField' => 'foreign_currency_unit_price',
                                            'show' => true
                                        ],
                                        [
                                            'type' => 'visibility',
                                            'targetField' => 'total_amount',
                                            'show' => false
                                        ],
                                        [
                                            'type' => 'visibility',
                                            'targetField' => 'foreign_currency_total',
                                            'show' => true
                                        ]
                                    ],
                                    'elseActions' => [
                                        [
                                            'type' => 'visibility',
                                            'targetField' => 'foreign_currency_unit_price',
                                            'show' => false
                                        ],
                                        [
                                            'type' => 'visibility',
                                            'targetField' => 'total_amount',
                                            'show' => true
                                        ],
                                        [
                                            'type' => 'visibility',
                                            'targetField' => 'foreign_currency_total',
                                            'show' => false
                                        ]
                                    ]
                                ],
                                [
                                    'id' => 'currency_unit_price_disabled',
                                    'triggerFields' => ['currency'],
                                    'conditions' => [
                                        'type' => 'single',
                                        'field' => 'currency',
                                        'operator' => 'equals',
                                        'value' => '人民币'
                                    ],
                                    'actions' => [
                                        [
                                            'type' => 'disabled',
                                            'targetField' => 'unit_price',
                                            'disabled' => false
                                        ]
                                    ],
                                    'elseActions' => [
                                        [
                                            'type' => 'disabled',
                                            'targetField' => 'unit_price',
                                            'disabled' => true
                                        ]
                                    ]
                                ]
                            ],
                        ],
                    ],
                ]
            ]
        ];

        $itemFormData = [
            0 => [
                'label' => '产品信息',
                'dataItem' => [
                    //明细信息
                    [
                        'field' => 'item_request',
                        'type' => 'edittable',
                        'title' => '产品信息',
                        'isonly' => false,
                        'required' => true,
                        'config' => [
                            //表格列信息
                            'columns' => [
                                ['field' => 'name', 'type' => 'text', 'title' => '产品名称', 'isonly' => false, 'required' => false],
                                ['field' => 'project_name', 'type' => 'text', 'title' => '单位', 'isonly' => false, 'required' => false],
                                [
                                    'field' => 'is_finish_split',
                                    'type' => 'radio',
                                    'title' => '是否完成产品拆分',
                                    'required' => true,
                                    'config' => [
                                        'options' => self::$isYes,
                                        'optionsColor' => self::$statusColorMap,
                                        'allowClear' => true,
                                        'showSearch' => false
                                    ],
                                ],
                                // ['field' => 'unit_price_org', 'type' => 'number', 'title' => '原单价', 'isonly' => false, 'required' => false, 'config' => [ 'editPermission' => 'edit-only']],
                                ['field' => 'unit_price', 'type' => 'number', 'title' => '单价', 'isonly' => false, 'required' => false],
                                ['field' => 'foreign_currency_unit_pirce', 'type' => 'number', 'title' => '外汇单价', 'isonly' => false, 'required' => false],
                                // ['field' => 'qty_request_org', 'type' => 'number', 'title' => '原数量', 'isonly' => false, 'required' => false, 'config' => [ 'editPermission' => 'edit-only']],
                                // ['field' => 'qty_request', 'type' => 'number', 'title' => '数量', 'isonly' => false, 'required' => false],
                                ['field' => 'qty_request_actual', 'type' => 'number', 'title' => '订单实际数量', 'isonly' => false, 'required' => false, 'config' => ['editPermission' => 'edit-only']],
                                ['field' => 'length', 'type' => 'number', 'title' => '长度(CM)', 'isonly' => false, 'required' => false],
                                ['field' => 'width', 'type' => 'number', 'title' => '宽度(CM)', 'isonly' => false, 'required' => false],
                                ['field' => 'height', 'type' => 'number', 'title' => '高度(CM)', 'isonly' => false, 'required' => false],
                                ['field' => 'desc', 'type' => 'text', 'title' => '描述', 'isonly' => false, 'required' => false],
                                ['field' => 'remark', 'type' => 'text', 'title' => '备注', 'isonly' => false, 'required' => false],
                                ['field' => 'imgs', 'type' => 'files', 'title' => '产品图片', 'isonly' => false, 'required' => false],
                                ['field' => 'puid', 'type' => 'text', 'title' => '产品编号', 'isonly' => false, 'required' => false],
                                ['field' => 'uniqid', 'type' => 'text', 'title' => '产品唯一码', 'isonly' => false, 'required' => false],
                                ['field' => 'batch_code', 'type' => 'text', 'title' => '批号', 'isonly' => false, 'required' => false],
                                [
                                    'field' => 'total_amount',
                                    'type' => 'calculated',
                                    'title' => '总价金额',
                                    'isonly' => false,
                                    'required' => false,
                                    'disabled' => true,
                                    'config' => [
                                        // V2计算字段配置
                                        'calculationV2' => [
                                            'id' => 'total_amount_calc_item',
                                            'type' => 'product',
                                            'sourceFields' => ['unit_price', 'qty_request_actual'],
                                            'triggerFields' => ['unit_price', 'qty_request_actual'],
                                            'precision' => 2,
                                            'defaultValue' => 0,
                                        ],
                                    ]
                                ],
                                ['field' => 'location_space', 'type' => 'text', 'title' => '所在空间', 'isonly' => false, 'required' => false, 'config' => ['editPermission' => 'edit-only']],
                                [
                                    'field' => 'actions',
                                    'type' => 'actions',
                                    'title' => '操作',
                                    'isonly' => false,
                                    'required' => false,
                                    'options' => [
                                        'actionColumnConfig' => [
                                            'width' => 300,           // 操作列宽度
                                            'title' => '操作',        // 操作列标题
                                            'fixed' => 'right',       // 固定在右侧
                                            'align' => 'center',      // 对齐方式
                                        ],
                                        'autoAddActionColumn' => true,  // 是否自动添加操作列
                                    ],
                                    'actions' => [
                                        [
                                            'type' => 'edit',
                                            'title' => '编辑',
                                            'icon' => 'EditOutlined'
                                        ],
                                        [
                                            'type' => 'delete',
                                            'title' => '删除',
                                            'icon' => 'DeleteOutlined',
                                            'popconfirm' => [
                                                'title' => '确定要删除这条记录吗？',
                                                'okText' => '确定',
                                                'cancelText' => '取消'
                                            ]
                                        ],
                                        [
                                            'type' => 'custom',
                                            'title' => '复制',
                                            'icon' => 'CopyOutlined',
                                            'code' => 'copy_row'
                                        ]
                                    ]
                                ],

                            ],
                            //按钮
                            'tablist' => [
                                ['title' => '导入', 'type' => 'import', 'key' => 'main', 'bgColor' => '#2D0AB1'],
                                ['title' => '新增', 'type' => 'add', 'key' => 'main', 'bgColor' => '#2D0AB1'],
                            ],
                            //弹窗表单信息
                            'form' => [
                                0 => [
                                    'label' => '其他',
                                    'dataItem' => [
                                        ['field' => 'name', 'type' => 'text', 'title' => '产品名称', 'isonly' => false, 'required' => false],
                                        ['field' => 'project_name', 'type' => 'text', 'title' => '单位', 'isonly' => false, 'required' => false],
                                        [
                                            'field' => 'is_finish_split',
                                            'type' => 'radio',
                                            'title' => '是否完成产品拆分',
                                            'required' => true,
                                            'config' => [
                                                'options' => self::$isYes,
                                                'optionsColor' => self::$statusColorMap,
                                                'allowClear' => true,
                                                'showSearch' => false
                                            ],
                                        ],
                                        // ['field' => 'unit_price_org', 'type' => 'number', 'title' => '原单价', 'isonly' => false, 'required' => false],
                                        ['field' => 'unit_price', 'type' => 'number', 'title' => '单价', 'isonly' => false, 'required' => false],
                                        ['field' => 'foreign_currency_unit_pirce', 'type' => 'number', 'title' => '外汇单价', 'isonly' => false, 'required' => false],
                                        // ['field' => 'qty_request_org', 'type' => 'number', 'title' => '原数量', 'isonly' => false, 'required' => false],
                                        // ['field' => 'qty_request', 'type' => 'number', 'title' => '数量', 'isonly' => false, 'required' => false],
                                        ['field' => 'qty_request_actual', 'type' => 'number', 'title' => '订单实际数量', 'isonly' => false, 'required' => false],
                                        ['field' => 'length', 'type' => 'number', 'title' => '长度(CM)', 'isonly' => false, 'required' => false],
                                        ['field' => 'width', 'type' => 'number', 'title' => '宽度(CM)', 'isonly' => false, 'required' => false],
                                        ['field' => 'height', 'type' => 'number', 'title' => '高度(CM)', 'isonly' => false, 'required' => false],
                                        ['field' => 'desc', 'type' => 'text', 'title' => '描述', 'isonly' => false, 'required' => false],
                                        ['field' => 'remark', 'type' => 'text', 'title' => '备注', 'isonly' => false, 'required' => false],
                                        ['field' => 'puid', 'type' => 'text', 'title' => '产品编号', 'isonly' => false, 'required' => false],
                                        ['field' => 'uniqid', 'type' => 'text', 'title' => '产品唯一码', 'isonly' => false, 'required' => false],
                                        ['field' => 'batch_code', 'type' => 'text', 'title' => '批号', 'isonly' => false, 'required' => false],
                                        [
                                            'field' => 'total_amount',
                                            'type' => 'calculated',
                                            'title' => '总价金额',
                                            'isonly' => false,
                                            'required' => false,
                                            'disabled' => true,
                                            'config' => [
                                                // V2计算字段配置
                                                'calculationV2' => [
                                                    'id' => 'total_amount_calc_item_form',
                                                    'type' => 'product',
                                                    'sourceFields' => ['unit_price', 'qty_request_actual'],
                                                    'triggerFields' => ['unit_price', 'qty_request_actual'],
                                                    'precision' => 2,
                                                    'defaultValue' => 0,
                                                ],
                                            ]
                                        ],
                                        ['field' => 'location_space', 'type' => 'text', 'title' => '所在空间', 'isonly' => false, 'required' => false],
                                        [
                                            'field' => 'imgs',
                                            'type' => 'Upload',
                                            'title' => '产品图片',
                                            'isonly' => false,
                                            'required' => false,
                                            'config' => [
                                                'formItemClass' => 'col-span-3',    //独占一行
                                            ]
                                        ],

                                    ],
                                ],
                                1 => [
                                    'label' => '其他',
                                    'dataItem' => [],
                                ]
                            ],
                            // V2外部联动配置
                            'externalLinkageV2' => [
                                [
                                    'id' => 'currency_external_linkage_item',
                                    // 监听的外部表单字段
                                    'triggerFields' => ['currency'],
                                    // 联动规则
                                    'conditions' => [
                                        'type' => 'single',
                                        'field' => 'currency',
                                        'operator' => 'not_equals',
                                        'value' => '人民币'
                                    ],
                                    'actions' => [
                                        // 批发订单才显示批发价
                                        [
                                            'type' => 'visibility',
                                            'targetField' => 'foreign_currency_unit_pirce',
                                            'show' => true
                                        ]
                                    ],
                                    'elseActions' => [
                                        // VIP客户才显示VIP折扣
                                        [
                                            'type' => 'visibility',
                                            'targetField' => 'unit_pirce',
                                            'show' => true
                                        ]
                                    ]
                                ]
                            ],
                        ],
                    ],
                ]
            ]
        ];

        return [
            //详情
            'main' => $mainFormData,
            'itemRequest' => $itemFormData,
        ];
    }
}