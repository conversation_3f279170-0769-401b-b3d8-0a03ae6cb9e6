# PHP后端联动引擎统一配置指南

## 1. 快速入门

### 1.1 基础概念

联动引擎V2是一个高性能的表单字段联动系统，支持：
- 字段显示/隐藏控制
- 动态必填验证
- 选项联动更新
- 字段值计算
- 复杂条件判断

### 1.2 PHP后端配置结构

```php
// 基础配置结构
$linkageConfig = [
    'rules' => [
        [
            'id' => 'rule_1',
            'conditions' => [
                [
                    'field' => 'user_type',
                    'operator' => 'equals',
                    'value' => 'student'
                ]
            ],
            'actions' => [
                [
                    'type' => 'show',
                    'target' => 'student_id'
                ]
            ]
        ]
    ]
];
```

## 2. 条件配置详解

### 2.1 基础操作符

```php
// 等于
['field' => 'status', 'operator' => 'equals', 'value' => 'active']

// 不等于
['field' => 'status', 'operator' => 'not_equals', 'value' => 'inactive']

// 包含
['field' => 'tags', 'operator' => 'contains', 'value' => 'urgent']

// 不包含
['field' => 'tags', 'operator' => 'not_contains', 'value' => 'draft']

// 大于
['field' => 'age', 'operator' => 'greater_than', 'value' => 18]

// 小于
['field' => 'age', 'operator' => 'less_than', 'value' => 65]

// 大于等于
['field' => 'score', 'operator' => 'greater_equal', 'value' => 60]

// 小于等于
['field' => 'score', 'operator' => 'less_equal', 'value' => 100]

// 在范围内
['field' => 'price', 'operator' => 'in_range', 'value' => [100, 500]]

// 不在范围内
['field' => 'price', 'operator' => 'not_in_range', 'value' => [0, 50]]

// 在列表中
['field' => 'category', 'operator' => 'in', 'value' => ['tech', 'science', 'art']]

// 不在列表中
['field' => 'category', 'operator' => 'not_in', 'value' => ['spam', 'adult']]

// 为空
['field' => 'description', 'operator' => 'is_empty']

// 不为空
['field' => 'description', 'operator' => 'is_not_empty']

// 正则匹配
['field' => 'email', 'operator' => 'regex', 'value' => '/^[\w\.-]+@[\w\.-]+\.[a-zA-Z]{2,}$/']
```

### 2.2 复杂条件组合

```php
// AND条件组合
$andConditions = [
    'logic' => 'and',
    'conditions' => [
        ['field' => 'age', 'operator' => 'greater_than', 'value' => 18],
        ['field' => 'status', 'operator' => 'equals', 'value' => 'active']
    ]
];

// OR条件组合
$orConditions = [
    'logic' => 'or',
    'conditions' => [
        ['field' => 'vip_level', 'operator' => 'equals', 'value' => 'gold'],
        ['field' => 'total_spent', 'operator' => 'greater_than', 'value' => 10000]
    ]
];

// 嵌套条件
$nestedConditions = [
    'logic' => 'and',
    'conditions' => [
        ['field' => 'user_type', 'operator' => 'equals', 'value' => 'premium'],
        [
            'logic' => 'or',
            'conditions' => [
                ['field' => 'region', 'operator' => 'equals', 'value' => 'US'],
                ['field' => 'region', 'operator' => 'equals', 'value' => 'EU']
            ]
        ]
    ]
];
```

## 3. 动作配置详解

### 3.1 显示/隐藏控制

```php
// 显示字段
['type' => 'show', 'target' => 'field_name']

// 隐藏字段
['type' => 'hide', 'target' => 'field_name']

// 批量显示
['type' => 'show', 'target' => ['field1', 'field2', 'field3']]

// 批量隐藏
['type' => 'hide', 'target' => ['field1', 'field2', 'field3']]
```

### 3.2 必填验证控制

```php
// 设置必填
['type' => 'required', 'target' => 'field_name', 'value' => true]

// 取消必填
['type' => 'required', 'target' => 'field_name', 'value' => false]

// 批量设置必填
['type' => 'required', 'target' => ['field1', 'field2'], 'value' => true]
```

### 3.3 选项更新

```php
// 静态选项更新
[
    'type' => 'options',
    'target' => 'city',
    'value' => [
        ['label' => '北京', 'value' => 'beijing'],
        ['label' => '上海', 'value' => 'shanghai'],
        ['label' => '广州', 'value' => 'guangzhou']
    ]
]

// 动态选项更新（基于其他字段值）
[
    'type' => 'options',
    'target' => 'city',
    'source' => 'province',
    'mapping' => [
        'guangdong' => [
            ['label' => '广州', 'value' => 'guangzhou'],
            ['label' => '深圳', 'value' => 'shenzhen']
        ],
        'beijing' => [
            ['label' => '朝阳区', 'value' => 'chaoyang'],
            ['label' => '海淀区', 'value' => 'haidian']
        ]
    ]
]
```

### 3.4 字段值设置

```php
// 设置固定值
['type' => 'setValue', 'target' => 'discount', 'value' => 0.1]

// 基于其他字段计算值
[
    'type' => 'setValue',
    'target' => 'total_price',
    'calculation' => [
        'formula' => 'price * quantity * (1 - discount)',
        'fields' => ['price', 'quantity', 'discount']
    ]
]

// 条件值设置
[
    'type' => 'setValue',
    'target' => 'shipping_fee',
    'conditionalValue' => [
        'conditions' => [
            ['field' => 'total_amount', 'operator' => 'greater_than', 'value' => 100]
        ],
        'trueValue' => 0,
        'falseValue' => 10
    ]
]
```

### 3.5 禁用/启用控制

```php
// 禁用字段
['type' => 'disable', 'target' => 'field_name']

// 启用字段
['type' => 'enable', 'target' => 'field_name']
```

## 4. 完整配置示例

### 4.1 用户注册表单

```php
$userRegistrationConfig = [
    'rules' => [
        // 学生用户显示学号字段
        [
            'id' => 'show_student_id',
            'conditions' => [
                ['field' => 'user_type', 'operator' => 'equals', 'value' => 'student']
            ],
            'actions' => [
                ['type' => 'show', 'target' => 'student_id'],
                ['type' => 'required', 'target' => 'student_id', 'value' => true]
            ]
        ],
        // 企业用户显示公司信息
        [
            'id' => 'show_company_info',
            'conditions' => [
                ['field' => 'user_type', 'operator' => 'equals', 'value' => 'enterprise']
            ],
            'actions' => [
                ['type' => 'show', 'target' => ['company_name', 'tax_number']],
                ['type' => 'required', 'target' => ['company_name', 'tax_number'], 'value' => true]
            ]
        ],
        // 年龄限制
        [
            'id' => 'age_validation',
            'conditions' => [
                ['field' => 'age', 'operator' => 'less_than', 'value' => 18]
            ],
            'actions' => [
                ['type' => 'show', 'target' => 'guardian_contact'],
                ['type' => 'required', 'target' => 'guardian_contact', 'value' => true]
            ]
        ]
    ]
];
```

### 4.2 电商订单表单

```php
$orderFormConfig = [
    'rules' => [
        // 省市联动
        [
            'id' => 'province_city_linkage',
            'conditions' => [
                ['field' => 'province', 'operator' => 'is_not_empty']
            ],
            'actions' => [
                [
                    'type' => 'options',
                    'target' => 'city',
                    'source' => 'province',
                    'mapping' => [
                        'guangdong' => [
                            ['label' => '广州', 'value' => 'guangzhou'],
                            ['label' => '深圳', 'value' => 'shenzhen']
                        ],
                        'beijing' => [
                            ['label' => '朝阳区', 'value' => 'chaoyang'],
                            ['label' => '海淀区', 'value' => 'haidian']
                        ]
                    ]
                ]
            ]
        ],
        // 总价计算
        [
            'id' => 'calculate_total',
            'conditions' => [
                ['field' => 'quantity', 'operator' => 'greater_than', 'value' => 0],
                ['field' => 'unit_price', 'operator' => 'greater_than', 'value' => 0]
            ],
            'actions' => [
                [
                    'type' => 'setValue',
                    'target' => 'total_price',
                    'calculation' => [
                        'formula' => 'quantity * unit_price * (1 - discount)',
                        'fields' => ['quantity', 'unit_price', 'discount']
                    ]
                ]
            ]
        ],
        // 免运费条件
        [
            'id' => 'free_shipping',
            'conditions' => [
                ['field' => 'total_price', 'operator' => 'greater_equal', 'value' => 100]
            ],
            'actions' => [
                ['type' => 'setValue', 'target' => 'shipping_fee', 'value' => 0],
                ['type' => 'show', 'target' => 'free_shipping_notice']
            ]
        ]
    ]
];
```

### 4.3 动态表格联动

```php
$tableFormConfig = [
    'rules' => [
        // 表格行联动
        [
            'id' => 'table_row_calculation',
            'conditions' => [
                ['field' => 'items[*].quantity', 'operator' => 'greater_than', 'value' => 0],
                ['field' => 'items[*].price', 'operator' => 'greater_than', 'value' => 0]
            ],
            'actions' => [
                [
                    'type' => 'setValue',
                    'target' => 'items[*].subtotal',
                    'calculation' => [
                        'formula' => 'quantity * price',
                        'fields' => ['quantity', 'price']
                    ]
                ]
            ]
        ],
        // 总计计算
        [
            'id' => 'calculate_grand_total',
            'conditions' => [
                ['field' => 'items', 'operator' => 'is_not_empty']
            ],
            'actions' => [
                [
                    'type' => 'setValue',
                    'target' => 'grand_total',
                    'calculation' => [
                        'formula' => 'sum(items[*].subtotal)',
                        'fields' => ['items']
                    ]
                ]
            ]
        ]
    ]
];
```

## 5. 自定义函数

### 5.1 日期函数

```php
// 工作日判断
[
    'field' => 'date',
    'operator' => 'custom',
    'function' => 'isWorkday',
    'value' => true
]

// 年龄范围
[
    'field' => 'birthday',
    'operator' => 'custom',
    'function' => 'ageInRange',
    'value' => [18, 65]
]

// 日期范围
[
    'field' => 'event_date',
    'operator' => 'custom',
    'function' => 'dateInRange',
    'value' => ['2024-01-01', '2024-12-31']
]
```

### 5.2 业务函数

```php
// 会员等级判断
[
    'field' => 'user_id',
    'operator' => 'custom',
    'function' => 'checkMemberLevel',
    'value' => 'gold'
]

// 库存检查
[
    'field' => 'product_id',
    'operator' => 'custom',
    'function' => 'checkStock',
    'value' => 10
]
```

## 6. 性能优化配置

### 6.1 缓存配置

```php
$performanceConfig = [
    'cache' => [
        'enabled' => true,
        'ttl' => 300, // 5分钟
        'maxSize' => 1000
    ],
    'debounce' => [
        'enabled' => true,
        'delay' => 300 // 300ms
    ],
    'batchUpdate' => [
        'enabled' => true,
        'batchSize' => 50
    ]
];
```

### 6.2 规则优先级

```php
$ruleWithPriority = [
    'id' => 'high_priority_rule',
    'priority' => 1, // 数字越小优先级越高
    'conditions' => [...],
    'actions' => [...]
];
```

## 7. 调试和错误处理

### 7.1 调试配置

```php
$debugConfig = [
    'debug' => [
        'enabled' => true,
        'level' => 'info', // error, warn, info, debug
        'logRuleExecution' => true,
        'logPerformance' => true
    ]
];
```

### 7.2 错误处理

```php
$errorHandling = [
    'onError' => 'continue', // continue, stop, retry
    'maxRetries' => 3,
    'fallbackValue' => null
];
```

## 8. API接口

### 8.1 配置接口

```php
// GET /api/form/linkage-config/{formId}
// 返回表单的联动配置

// POST /api/form/linkage-config/{formId}
// 更新表单的联动配置
{
    "config": {
        "rules": [...],
        "performance": {...},
        "debug": {...}
    }
}
```

### 8.2 验证接口

```php
// POST /api/form/validate-linkage
// 验证联动配置是否正确
{
    "formId": "user_form",
    "config": {...},
    "testData": {...}
}
```

## 9. 常见问题解决

### 9.1 性能问题

**问题**: 联动响应慢
**解决**: 
- 启用缓存和防抖
- 减少复杂条件嵌套
- 使用规则优先级

### 9.2 配置错误

**问题**: 联动不生效
**解决**:
- 检查字段名是否正确
- 验证条件逻辑
- 启用调试模式查看日志

### 9.3 兼容性问题

**问题**: 旧版本迁移
**解决**:
- 使用配置转换工具
- 逐步迁移规则
- 保持向后兼容

## 10. 最佳实践

1. **规则组织**: 按功能模块组织规则，使用有意义的ID
2. **性能优化**: 合理使用缓存，避免过度嵌套
3. **错误处理**: 设置合适的错误处理策略
4. **调试支持**: 开发环境启用详细日志
5. **文档维护**: 及时更新配置文档和注释

---

这个统一指南包含了PHP后端开发者需要的所有联动配置信息，从基础概念到高级用法，确保能够快速上手并解决实际问题。