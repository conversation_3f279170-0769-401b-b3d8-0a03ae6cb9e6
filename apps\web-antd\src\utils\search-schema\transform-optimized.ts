/**
 * 优化版本的表单转换工具
 * 集成了缓存、对象池和memoization机制
 */

import type {
  BackendSearchItem,
  DirectGroupedSearchData,
  GroupedSearchData,
} from './types';

import type { VbenFormSchema } from '#/adapter/form';

import { normalizeOptions } from '#/utils/options';

import {
  createApiSelectPaginatedFunction,
  createListActionFunction,
} from './api-request';
// 导入优化工具
import { transformCache } from './cache/transform-cache';
import { memoize } from './memoization/memoize';
import { poolManager } from './pool/object-pool';
import { TYPE_TO_COMPONENT_MAP } from './types';

/**
 * 转换选项接口
 */
interface TransformOptions {
  enableGrouping?: boolean;
  formMode?: 'add' | 'edit';
  enableCache?: boolean;
  enableObjectPool?: boolean;
}

/**
 * 缓存键生成器
 */
function generateCacheKey(
  data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
  options: TransformOptions,
): string {
  // 使用数据的哈希值和选项生成缓存键
  const dataHash = JSON.stringify(data);
  const optionsHash = JSON.stringify(options);
  // 使用简单的哈希算法替代btoa，避免无效字符问题
  const combined = dataHash + optionsHash;
  let hash = 0;
  for (let i = 0; i < combined.length; i++) {
    const char = combined.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return `transform_${Math.abs(hash).toString(36).slice(0, 16)}`;
}

/**
 * 创建数据处理器函数（优化版本）
 */
const createDataProcessor = memoize(
  (processor: any, _type: 'afterFetch' | 'beforeFetch') => {
    // 如果已经是函数，直接返回
    if (typeof processor === 'function') {
      return processor;
    }

    // 如果是字符串，尝试从全局处理器中获取
    if (typeof processor === 'string') {
      return async (data: any) => {
        // 尝试从多个可能的全局对象中获取处理函数
        const globalProcessors =
          (window as any).apiDataProcessors ||
          (window as any).dataProcessors ||
          {};

        const processorFn = globalProcessors[processor];
        if (typeof processorFn === 'function') {
          return await processorFn(data);
        }

        console.warn(
          `Data processor function "${processor}" not found in global scope`,
        );
        return data;
      };
    }

    // 如果是配置对象，创建对应的处理函数
    if (typeof processor === 'object' && processor !== null) {
      return async (data: any) => {
        const config = processor;

        // 根据配置类型处理数据
        if (config.type === 'formatOptions') {
          return await formatOptionsProcessor(data, config);
        }

        // 可以添加更多配置类型的处理
        console.warn(`Unknown processor config type: ${config.type}`);
        return data;
      };
    }

    // 默认返回原数据
    return async (data: any) => data;
  },
  {
    maxSize: 50,
    ttl: 10 * 60 * 1000, // 10分钟
  },
);

/**
 * 格式化选项处理器（优化版本）
 */
const formatOptionsProcessor = memoize(
  async (response: any, config: any) => {
    let data = Array.isArray(response)
      ? response
      : response.items || response.data || response.list || [];

    // 过滤非活跃项 - 对于没有状态字段的数据，默认认为是活跃的
    if (config.filterInactive) {
      data = data.filter((item: any) => {
        // 如果没有状态字段，默认认为是活跃的
        if (
          !Object.prototype.hasOwnProperty.call(item, 'status') &&
          !Object.prototype.hasOwnProperty.call(item, 'is_active') &&
          !Object.prototype.hasOwnProperty.call(item, 'active') &&
          !Object.prototype.hasOwnProperty.call(item, 'enabled')
        ) {
          return true;
        }

        return (
          item.status === 'active' ||
          item.status === 1 ||
          item.is_active === true ||
          item.is_active === 1 ||
          item.active === true ||
          item.active === 1 ||
          item.enabled === true ||
          item.enabled === 1
        );
      });
    }

    // 过滤数据
    if (config.filterField && config.filterValue !== undefined) {
      data = data.filter(
        (item: any) => item[config.filterField] === config.filterValue,
      );
    }

    // 格式化标签
    if (config.labelFormat) {
      data = data.map((item: any) => ({
        ...item,
        label: config.labelFormat.replaceAll(
          /\{(\w+(?:\.\w+)*)\}/g,
          (_match: string, key: string) => {
            // 支持嵌套属性访问，如 {category.name}
            const keys = key.split('.');
            let value = item;
            for (const k of keys) {
              value = value?.[k];
              if (value === undefined || value === null) break;
            }
            return value || '';
          },
        ),
      }));
    }

    // 排序
    if (config.sortBy) {
      data.sort((a: any, b: any) => {
        const aVal = a[config.sortBy];
        const bVal = b[config.sortBy];
        if (config.sortOrder === 'desc') {
          // eslint-disable-next-line unicorn/no-nested-ternary
          return bVal > aVal ? 1 : bVal < aVal ? -1 : 0;
        }
        // eslint-disable-next-line unicorn/no-nested-ternary
        return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
      });
    }

    // 添加空选项
    if (config.addEmptyOption) {
      data.unshift({
        id: '',
        value: '',
        label: config.emptyOptionText || '请选择',
      });
    }

    return data;
  },
  {
    maxSize: 100,
    ttl: 5 * 60 * 1000, // 5分钟
  },
);

/**
 * 创建分组标题的 schema（优化版本）
 */
const createGroupDivider = memoize(
  (label: string): VbenFormSchema => {
    // 从对象池获取schema对象
    const schema =
      (poolManager.getPool('schema') as any)?.acquireSchema() || {};

    // 重置并设置属性
    Object.assign(schema, {
      fieldName: `groupTitle_${label}`,
      component: 'GroupTitle',
      formItemClass: 'col-span-3',
      hideLabel: true,
      componentProps: {
        title: label,
      },
    });

    return schema as VbenFormSchema;
  },
  {
    maxSize: 20,
    ttl: 30 * 60 * 1000, // 30分钟
  },
);

/**
 * 检查值是否有效（不为 undefined、null、空字符串）
 */
const hasValue = memoize(
  (value: any): boolean => {
    return value !== undefined && value !== null && value !== '';
  },
  {
    maxSize: 1000,
    ttl: 60 * 1000, // 1分钟
  },
);

// 用于跟踪表单是否正在进行批量设置（回显数据）
const formBatchSettingMap = new Map<any, boolean>();

/**
 * 设置表单批量设置状态
 */
export function setFormBatchSetting(formApi: any, isBatchSetting: boolean) {
  if (formApi) {
    formBatchSettingMap.set(formApi, isBatchSetting);

    // 如果设置为批量设置状态，5秒后自动清除（防止状态泄漏）
    if (isBatchSetting) {
      setTimeout(() => {
        formBatchSettingMap.delete(formApi);
      }, 5000);
    }
  }
}

/**
 * 检查表单是否正在批量设置
 */
export function isFormBatchSetting(formApi: any): boolean {
  return formBatchSettingMap.get(formApi) === true;
}

/**
 * 转换 options 配置（优化版本）
 */
const transformOptions = memoize(
  (
    options: any,
    fieldName: string,
    fieldType: string,
  ): Array<{ disabled?: boolean; label: string; value: any }> => {
    return normalizeOptions(options, {
      fieldName,
      fieldType,
      componentName: 'transformBackendSearchToSchema',
    });
  },
  {
    maxSize: 200,
    ttl: 10 * 60 * 1000, // 10分钟
  },
);

/**
 * 深度清理对象中的 undefined 和 null 值（优化版本）
 */
const cleanUndefinedValues = memoize(
  (obj: T): T => {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    const cleaned = {} as T;

    for (const [key, value] of Object.entries(obj)) {
      // 跳过 undefined 和 null 值
      if (value === undefined || value === null) {
        continue;
      }

      // 如果是数组，递归清理数组中的每个元素
      if (Array.isArray(value)) {
        const cleanedArray = value
          .map((item) =>
            typeof item === 'object' && item !== null
              ? cleanUndefinedValues(item)
              : item,
          )
          .filter((item) => item !== undefined && item !== null);

        if (cleanedArray.length > 0) {
          (cleaned as any)[key] = cleanedArray;
        }
      }
      // 如果是对象，递归清理
      else if (typeof value === 'object') {
        const cleanedObject = cleanUndefinedValues(value);
        // 只有当清理后的对象不为空时才添加
        if (Object.keys(cleanedObject).length > 0) {
          (cleaned as any)[key] = cleanedObject;
        }
      }
      // 其他类型的值直接添加
      else {
        (cleaned as any)[key] = value;
      }
    }

    return cleaned;
  },
  {
    maxSize: 100,
    ttl: 5 * 60 * 1000, // 5分钟
  },
);

/**
 * 转换单个搜索条件（优化版本）
 */
const transformSingleItem = memoize(
  (
    item: BackendSearchItem,
    formMode?: 'add' | 'edit',
  ): null | VbenFormSchema => {
    // 处理 ifShow 为 false 的情况：直接剔除该字段
    if (
      item.ifShow !== undefined &&
      typeof item.ifShow === 'boolean' &&
      item.ifShow === false
    ) {
      return null;
    }

    const component = TYPE_TO_COMPONENT_MAP[item.type] || 'Input';
    // 创建 config 的副本，避免修改原始数据
    const config = { ...item.config };

    // 处理编辑权限控制
    if (config.editPermission && formMode) {
      const shouldHide =
        (config.editPermission === 'edit-only' && formMode === 'add') ||
        (config.editPermission === 'add-only' && formMode === 'edit') ||
        config.editPermission === 'none';

      if (shouldHide) {
        return null;
      }
    }

    // 从对象池获取schema对象
    const schema =
      (poolManager.getPool('schema') as any)?.acquireSchema() || {};

    // 重置并设置基础属性
    Object.assign(schema, {
      fieldName: item.field,
      label: item.title,
      component,
    });

    // 设置默认值（保持向后兼容性）
    schema.defaultValue = item.default;

    // 处理 formItemClass
    if (hasValue(config.formItemClass)) {
      schema.formItemClass = `${config.formItemClass} w-full`.trim();
    } else if (item.type === 'hidden') {
      schema.formItemClass = 'hidden';
    } else {
      schema.formItemClass = 'w-full';
    }

    // 从对象池获取组件属性对象
    const componentProps =
      (poolManager.getPool('componentProps') as any)?.acquireComponentProps() ||
      {};

    // 重置组件属性对象（不设置默认class）

    // 添加自定义组件属性（如果有值）
    if (
      config.componentProps &&
      Object.keys(config.componentProps).length > 0
    ) {
      Object.assign(componentProps, config.componentProps);
    }

    // 根据类型设置特定属性
    switch (item.type) {
      case 'apiSelect':
      case 'apiselect': {
        componentProps.placeholder =
          config.placeholder || `请选择${item.title}`;
        componentProps.allowClear = hasValue(config.allowClear)
          ? config.allowClear
          : true;
        componentProps.showSearch = hasValue(config.showSearch)
          ? config.showSearch
          : !!(config.searchable || config.url);
        componentProps.filterOption = hasValue(config.filterOption)
          ? config.filterOption
          : !config.url;

        // 多选模式处理
        if (config.multiple === true) {
          componentProps.mode = 'multiple';
        } else if (hasValue(config.mode)) {
          componentProps.mode = config.mode;
        }

        // 多选相关配置
        if (hasValue(config.maxTagCount)) {
          componentProps.maxTagCount = config.maxTagCount;
        }
        if (hasValue(config.maxTagTextLength)) {
          componentProps.maxTagTextLength = config.maxTagTextLength;
        }
        if (hasValue(config.maxTagPlaceholder)) {
          componentProps.maxTagPlaceholder = config.maxTagPlaceholder;
        }

        // 处理 API 相关配置
        if (hasValue(config.url) && config.url) {
          if (
            (config.showSearch || config.searchable) &&
            config.disableSearchRequest !== true
          ) {
            const searchParamName =
              config.searchParamName || config.searchFieldName || 'search';

            const finalPageSize =
              config.pagesize ||
              config.pageSize ||
              config.params?.pageSize ||
              config.params?.pagesize ||
              20;
            const apiConfig = {
              pageSize: finalPageSize,
              pageParamName: 'page',
              pageSizeParamName: 'pageSize',
              searchParamName,
            };

            componentProps.api = createApiSelectPaginatedFunction(
              config.url,
              config.params || {},
              apiConfig,
            );

            if (hasValue(config.searchFieldName)) {
              componentProps.searchFieldName = config.searchFieldName;
            }
            if (hasValue(config.searchParamName)) {
              componentProps.searchParamName = config.searchParamName;
            }
          } else {
            componentProps.api = createListActionFunction(
              config.url,
              config.params || {},
            );
          }
        } else if (hasValue(config.api)) {
          componentProps.api = config.api;
        }

        // API 相关配置
        const hasAfterFetchFormatting =
          config.afterFetch &&
          typeof config.afterFetch === 'object' &&
          (config.afterFetch as any).type === 'formatOptions' &&
          (config.afterFetch as any).labelFormat;

        componentProps.labelField = hasAfterFetchFormatting
          ? 'label'
          : config.labelField || 'name';
        componentProps.valueField = config.valueField || 'id';

        if (hasValue(config.resultField)) {
          componentProps.resultField = config.resultField;
        }

        // 其他API配置
        if (hasValue(config.labelFormatter)) {
          componentProps.labelFormatter = config.labelFormatter;
        }
        if (hasValue(config.immediate)) {
          componentProps.immediate = config.immediate;
        }
        if (hasValue(config.alwaysLoad)) {
          componentProps.alwaysLoad = config.alwaysLoad;
        }
        if (hasValue(config.autoSelect)) {
          componentProps.autoSelect = config.autoSelect;
        }
        if (hasValue(config.beforeFetch)) {
          componentProps.beforeFetch = createDataProcessor(
            config.beforeFetch,
            'beforeFetch',
          );
        }
        if (hasValue(config.afterFetch)) {
          componentProps.afterFetch = createDataProcessor(
            config.afterFetch,
            'afterFetch',
          );
        }
        if (hasValue(config.returnParamsField)) {
          componentProps.returnParamsField = config.returnParamsField;
        }
        break;
      }

      // 其他组件类型的处理...
      default: {
        // 处理通用属性
        if (hasValue(config.placeholder)) {
          componentProps.placeholder = config.placeholder;
        }
        if (hasValue(config.disabled)) {
          componentProps.disabled = config.disabled;
        }
        if (hasValue(config.readonly)) {
          componentProps.readonly = config.readonly;
        }
        // 处理选项（用于select等组件）
        if (hasValue(config.options)) {
          componentProps.options = config.options;
        }
        break;
      }
    }

    // 设置组件属性（只在有属性时才设置）
    if (Object.keys(componentProps).length > 0) {
      schema.componentProps = cleanUndefinedValues(componentProps);
    }

    // 处理其他schema属性
    if (hasValue(config.rules)) {
      schema.rules = config.rules;
    }
    if (hasValue(config.dependencies)) {
      schema.dependencies = config.dependencies;
    }
    if (hasValue(item.ifShow)) {
      schema.ifShow = item.ifShow;
    }

    return cleanUndefinedValues(schema) as VbenFormSchema;
  },
  {
    maxSize: 500,
    ttl: 10 * 60 * 1000, // 10分钟
  },
);

/**
 * 主要的转换函数（优化版本）
 */
export function transformBackendSearchToSchema(
  data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
  options: TransformOptions = {},
): VbenFormSchema[] {
  const {
    enableGrouping = true,
    formMode,
    enableCache = true,
    enableObjectPool = true,
  } = options;

  // 如果启用缓存，先检查缓存
  if (enableCache) {
    const cacheKey = generateCacheKey(data, options);
    const cached = transformCache.get(cacheKey);
    if (cached) {
      return cached;
    }
  }

  // 从对象池获取数组
  const newSchema = enableObjectPool
    ? (poolManager.getPool('array') as any)?.acquireArray() || []
    : [];

  try {
    // 如果是简单数组格式
    if (Array.isArray(data)) {
      // 检查是否是分组数组格式
      if (data.length > 0 && data[0] && 'dataItem' in data[0]) {
        (data as GroupedSearchData[]).forEach((group) => {
          if (group.dataItem && Array.isArray(group.dataItem)) {
            // 总是添加分组标题（保持向后兼容性）
            if (group.label && enableGrouping) {
              newSchema.push(createGroupDivider(group.label));
            }
            
            const groupItems = group.dataItem
              .map((item) => transformSingleItem(item, formMode))
              .filter((item): item is VbenFormSchema => item !== null);

            newSchema.push(...groupItems);
          }
        });
      } else {
        const backendItems = data as BackendSearchItem[];
        const items = backendItems
          .map((item) => transformSingleItem(item, formMode))
          .filter((item): item is VbenFormSchema => item !== null);
        newSchema.push(...items);
      }
    }

    // 如果是包装在 schema 属性中的分组格式
    if ('schema' in data && Array.isArray(data.schema)) {
      (data as DirectGroupedSearchData).schema.forEach((group) => {
        if (group.dataItem && Array.isArray(group.dataItem)) {
          // 总是添加分组标题（保持向后兼容性）
          if (group.label && enableGrouping) {
            newSchema.push(createGroupDivider(group.label));
          }
          
          const groupItems = group.dataItem
            .map((item) => transformSingleItem(item, formMode))
            .filter((item): item is VbenFormSchema => item !== null);

          newSchema.push(...groupItems);
        }
      });
    }

    // 如果启用缓存，将结果存入缓存
    if (enableCache) {
      const cacheKey = generateCacheKey(data, options);
      transformCache.set(cacheKey, [...newSchema]); // 存储副本
    }

    return newSchema;
  } catch (error) {
    // 发生错误时，释放对象池资源
    if (enableObjectPool) {
      (poolManager.getPool('array') as any)?.releaseArray(newSchema);
    }
    throw error;
  }
}

/**
 * 为表单 API 设置初始化跟踪的包装函数
 */
export function setSchemaWithTracking(formApi: any, schema: any[]) {
  if (!formApi) {
    return;
  }
  formApi.setState({ schema });
}

/**
 * 带分组的转换函数
 */
export function transformBackendSearchToSchemaWithGrouping(
  data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
  formMode?: 'add' | 'edit',
): VbenFormSchema[] {
  return transformBackendSearchToSchema(data, {
    enableGrouping: true,
    formMode,
  });
}

/**
 * 不分组的转换函数
 */
export function transformBackendSearchToSchemaWithoutGrouping(
  data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
  formMode?: 'add' | 'edit',
): VbenFormSchema[] {
  return transformBackendSearchToSchema(data, {
    enableGrouping: false,
    formMode,
  });
}

/**
 * 性能监控和统计
 */
export function getTransformPerformanceStats() {
  return {
    cache: transformCache.getStats(),
    objectPool: poolManager.getStats(),
    memoization: {
      createDataProcessor: createDataProcessor.getStats(),
      formatOptionsProcessor: formatOptionsProcessor.getStats(),
      createGroupDivider: createGroupDivider.getStats(),
      hasValue: hasValue.getStats(),
      transformOptions: transformOptions.getStats(),
      cleanUndefinedValues: cleanUndefinedValues.getStats(),
      transformSingleItem: transformSingleItem.getStats(),
    },
  };
}

/**
 * 清理所有缓存
 */
export function clearAllCaches() {
  transformCache.clear();
  createDataProcessor.clearCache();
  formatOptionsProcessor.clearCache();
  createGroupDivider.clearCache();
  hasValue.clearCache();
  transformOptions.clearCache();
  cleanUndefinedValues.clearCache();
  transformSingleItem.clearCache();
}

/**
 * 重置对象池
 */
export function resetObjectPools() {
  poolManager.clearAllPools();
}