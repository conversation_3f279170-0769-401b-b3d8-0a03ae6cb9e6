/**
 * 联动引擎V2使用示例
 * 本文件展示了如何使用新的联动引擎V2来实现简单和复杂的联动规则
 */

import { LinkageEngineV2 } from './linkage-engine-v2';
import { ConditionBuilder, ActionBuilder, RuleBuilder } from './linkage-config-builder';
import { SimpleLinkageRule, LinkageConfig, SimpleCondition, LinkageAction } from './linkage-engine-v2';

// ======== 示例1: 基本使用 ========

/**
 * 示例1: 基本使用 - 创建一个简单的联动规则
 * 场景: 当选择了"其他"选项时，显示备注字段
 */
export function basicUsageExample() {
  // 创建联动引擎实例
  const engine = new LinkageEngineV2();
  
  // 设置初始表单值
  engine.setValues({
    category: '',
    remark: ''
  });
  
  // 定义一个简单的联动规则
  const rule: SimpleLinkageRule = {
    id: 'show-remark-when-other',
    triggerFields: ['category'],  // 触发字段
    conditions: [
      { field: 'category', operator: 'equals', value: 'other' }
    ],
    actions: [
      { field: 'remark', property: 'visible', value: true }
    ]
  };
  
  // 添加规则到引擎
  engine.setRules([rule]);
  
  // 监听字段状态变化
  engine.onFieldStateChange('remark', (state) => {
    console.log('备注字段状态变化:', state);
  });
  
  // 更新表单值，触发联动
  engine.updateValues({ category: 'other' });
  
  // 获取字段当前状态
  const remarkState = engine.getFieldState('remark');
  console.log('备注字段当前状态:', remarkState);
  
  return engine;
}

// ======== 示例2: 使用配置构建器 ========

/**
 * 示例2: 使用配置构建器 - 使用链式API创建联动规则
 * 场景: 根据用户类型和年龄显示不同的表单字段
 */
export function builderExample() {
  const engine = new LinkageEngineV2();
  
  // 设置初始表单值
  engine.setValues({
    userType: '',
    age: 0,
    companyInfo: '',
    schoolInfo: '',
    seniorDiscount: false
  });
  
  // 使用构建器创建规则
  const rule1 = new RuleBuilder()
    .id('show-company-info')
    .when('userType').equals('employee')
    .then('companyInfo').setVisible(true)
    .and('schoolInfo').setVisible(false)
    .build();
  
  const rule2 = new RuleBuilder()
    .id('show-school-info')
    .when('userType').equals('student')
    .then('schoolInfo').setVisible(true)
    .and('companyInfo').setVisible(false)
    .build();
  
  const rule3 = new RuleBuilder()
    .id('senior-discount')
    .when('age').greaterThan(60)
    .then('seniorDiscount').setVisible(true)
    .build();
  
  // 添加规则到引擎
  engine.setRules([rule1, rule2, rule3]);
  
  // 更新表单值，触发联动
  engine.updateValues({ userType: 'employee', age: 65 });
  
  return engine;
}

// ======== 示例3: 复杂联动 ========

/**
 * 示例3: 复杂联动 - 实现多条件、多动作的复杂联动
 * 场景: 订单表单中，根据产品类型、数量和客户类型计算折扣和总价
 */
export function complexLinkageExample() {
  const engine = new LinkageEngineV2();
  
  // 设置初始表单值
  engine.setValues({
    productType: '',
    quantity: 1,
    customerType: 'normal',
    unitPrice: 0,
    discount: 0,
    totalPrice: 0,
    shippingMethod: '',
    expressOptions: []
  });
  
  // 产品类型影响单价
  const rule1 = new RuleBuilder()
    .id('set-unit-price')
    .when('productType').equals('typeA')
    .then('unitPrice').setValue(100)
    .build();
  
  const rule2 = new RuleBuilder()
    .id('set-unit-price-b')
    .when('productType').equals('typeB')
    .then('unitPrice').setValue(200)
    .build();
  
  // 客户类型和数量影响折扣
  const rule3 = new RuleBuilder()
    .id('calculate-discount')
    .when('customerType').equals('vip')
    .and('quantity').greaterThanOrEqual(10)
    .then('discount').setValue(0.2)  // 20%折扣
    .build();
  
  const rule4 = new RuleBuilder()
    .id('calculate-discount-normal')
    .when('customerType').equals('normal')
    .and('quantity').greaterThanOrEqual(20)
    .then('discount').setValue(0.1)  // 10%折扣
    .build();
  
  // 计算总价
  const rule5: SimpleLinkageRule = {
    id: 'calculate-total-price',
    triggerFields: ['unitPrice', 'quantity', 'discount'],
    conditions: [],  // 无条件，总是执行
    actions: [
      {
        field: 'totalPrice',
        property: 'value',
        // 使用函数计算总价
        valueFunction: (values) => {
          const unitPrice = values.unitPrice || 0;
          const quantity = values.quantity || 0;
          const discount = values.discount || 0;
          return unitPrice * quantity * (1 - discount);
        }
      }
    ]
  };
  
  // 根据产品类型设置配送方式选项
  const rule6 = new RuleBuilder()
    .id('set-shipping-options')
    .when('productType').equals('typeA')
    .then('expressOptions').setOptions([
      { label: '普通快递', value: 'normal' },
      { label: '加急快递', value: 'express' }
    ])
    .build();
  
  const rule7 = new RuleBuilder()
    .id('set-shipping-options-b')
    .when('productType').equals('typeB')
    .then('expressOptions').setOptions([
      { label: '普通快递', value: 'normal' },
      { label: '加急快递', value: 'express' },
      { label: '特殊物流', value: 'special' }
    ])
    .build();
  
  // 添加规则到引擎
  engine.setRules([rule1, rule2, rule3, rule4, rule5, rule6, rule7]);
  
  // 更新表单值，触发联动
  engine.updateValues({
    productType: 'typeB',
    quantity: 15,
    customerType: 'vip'
  });
  
  return engine;
}

// ======== 示例4: 表格联动 ========

/**
 * 示例4: 表格联动 - 实现表格数据的联动计算
 * 场景: 订单明细表格，计算每行小计和总计
 */
export function tableCalculationExample() {
  const engine = new LinkageEngineV2();
  
  // 设置初始表单值，包含表格数据
  engine.setValues({
    orderItems: [
      { id: 1, product: 'A', price: 100, quantity: 2, subtotal: 0 },
      { id: 2, product: 'B', price: 200, quantity: 1, subtotal: 0 }
    ],
    totalAmount: 0
  });
  
  // 计算每行小计
  const rule1: SimpleLinkageRule = {
    id: 'calculate-subtotal',
    triggerFields: ['orderItems'],
    conditions: [],
    actions: [
      {
        field: 'orderItems',
        property: 'value',
        // 使用函数处理表格数据
        valueFunction: (values) => {
          const items = values.orderItems || [];
          return items.map(item => ({
            ...item,
            subtotal: (item.price || 0) * (item.quantity || 0)
          }));
        }
      }
    ]
  };
  
  // 计算总计
  const rule2: SimpleLinkageRule = {
    id: 'calculate-total',
    triggerFields: ['orderItems'],
    conditions: [],
    actions: [
      {
        field: 'totalAmount',
        property: 'value',
        valueFunction: (values) => {
          const items = values.orderItems || [];
          return items.reduce((sum, item) => sum + (item.subtotal || 0), 0);
        }
      }
    ]
  };
  
  // 添加规则到引擎
  engine.setRules([rule1, rule2]);
  
  // 更新表格数据，触发联动
  engine.updateValues({
    orderItems: [
      { id: 1, product: 'A', price: 100, quantity: 3, subtotal: 0 },
      { id: 2, product: 'B', price: 200, quantity: 2, subtotal: 0 },
      { id: 3, product: 'C', price: 150, quantity: 1, subtotal: 0 }
    ]
  });
  
  return engine;
}

// ======== 示例5: 性能优化 ========

/**
 * 示例5: 性能优化 - 展示如何使用批量更新和缓存优化性能
 * 场景: 大表单中的多字段联动
 */
export function performanceOptimizationExample() {
  const engine = new LinkageEngineV2();
  
  // 创建大量字段和规则
  const initialValues: Record<string, any> = {};
  const rules: SimpleLinkageRule[] = [];
  
  // 创建50个字段和规则
  for (let i = 1; i <= 50; i++) {
    const fieldName = `field${i}`;
    initialValues[fieldName] = '';
    
    // 每个字段都有一个依赖于field1的规则
    if (i > 1) {
      rules.push({
        id: `rule-${i}`,
        triggerFields: ['field1'],
        conditions: [
          { field: 'field1', operator: 'equals', value: `value${i}` }
        ],
        actions: [
          { field: fieldName, property: 'visible', value: true }
        ]
      });
    }
  }
  
  // 设置初始值和规则
  engine.setValues(initialValues);
  engine.setRules(rules);
  
  // 启用性能监控
  engine.enablePerformanceMonitoring();
  
  // 批量更新多个字段
  const batchUpdates = {
    field1: 'value10',
    field2: 'test',
    field3: 'test'
  };
  
  console.time('批量更新');
  engine.updateValues(batchUpdates);
  console.timeEnd('批量更新');
  
  // 获取性能指标
  const metrics = engine.getPerformanceMetrics();
  console.log('性能指标:', metrics);
  
  // 清除缓存并再次更新
  engine.clearCache();
  console.time('无缓存更新');
  engine.updateValues({ field1: 'value20' });
  console.timeEnd('无缓存更新');
  
  return engine;
}

// ======== 示例6: 调试支持 ========

/**
 * 示例6: 调试支持 - 展示如何使用调试功能
 * 场景: 排查联动规则问题
 */
export function debuggingExample() {
  const engine = new LinkageEngineV2();
  
  // 设置初始表单值
  engine.setValues({
    type: '',
    subtype: '',
    options: []
  });
  
  // 定义规则
  const rule1 = new RuleBuilder()
    .id('set-subtype-options')
    .when('type').equals('A')
    .then('options').setOptions([
      { label: 'A1', value: 'a1' },
      { label: 'A2', value: 'a2' }
    ])
    .build();
  
  const rule2 = new RuleBuilder()
    .id('set-subtype-options-b')
    .when('type').equals('B')
    .then('options').setOptions([
      { label: 'B1', value: 'b1' },
      { label: 'B2', value: 'b2' }
    ])
    .build();
  
  // 添加规则到引擎
  engine.setRules([rule1, rule2]);
  
  // 启用调试模式
  engine.enableDebugging({
    level: 'verbose',
    logToConsole: true
  });
  
  // 更新表单值，触发联动
  engine.updateValues({ type: 'A' });
  
  // 获取调试信息
  const debugInfo = engine.getDebugInfo();
  console.log('调试信息:', debugInfo);
  
  // 获取特定规则的执行历史
  const ruleHistory = engine.getRuleExecutionHistory('set-subtype-options');
  console.log('规则执行历史:', ruleHistory);
  
  return engine;
}

// ======== 示例7: 从旧版本迁移 ========

/**
 * 示例7: 从旧版本迁移 - 展示如何将旧版本的联动配置转换为新版本
 * 场景: 将旧版本的联动规则转换为新版本
 */
export function migrationExample() {
  // 旧版本的联动配置
  const oldConfig = {
    displayRules: [
      {
        showWhen: [
          {
            field: 'paymentMethod',
            operator: 'equals',
            value: 'creditCard'
          }
        ],
        fields: ['cardNumber', 'expiryDate', 'cvv']
      }
    ],
    requiredRules: [
      {
        requiredWhen: [
          {
            field: 'hasAttachment',
            operator: 'equals',
            value: true
          }
        ],
        fields: ['attachmentName']
      }
    ],
    calculationRules: [
      {
        type: 'sum',
        targetField: 'total',
        sourceFields: ['price', 'tax']
      }
    ]
  };
  
  // 转换为新版本的联动规则
  const newRules: SimpleLinkageRule[] = [];
  
  // 转换显示规则
  oldConfig.displayRules.forEach((rule, index) => {
    rule.fields.forEach(field => {
      newRules.push({
        id: `display-rule-${index}-${field}`,
        triggerFields: rule.showWhen.map(condition => condition.field),
        conditions: rule.showWhen.map(condition => ({
          field: condition.field,
          operator: condition.operator,
          value: condition.value
        })),
        actions: [
          { field, property: 'visible', value: true }
        ]
      });
    });
  });
  
  // 转换必填规则
  oldConfig.requiredRules.forEach((rule, index) => {
    rule.fields.forEach(field => {
      newRules.push({
        id: `required-rule-${index}-${field}`,
        triggerFields: rule.requiredWhen.map(condition => condition.field),
        conditions: rule.requiredWhen.map(condition => ({
          field: condition.field,
          operator: condition.operator,
          value: condition.value
        })),
        actions: [
          { field, property: 'required', value: true }
        ]
      });
    });
  });
  
  // 转换计算规则
  oldConfig.calculationRules.forEach((rule, index) => {
    if (rule.type === 'sum') {
      newRules.push({
        id: `calculation-rule-${index}`,
        triggerFields: rule.sourceFields,
        conditions: [],
        actions: [
          {
            field: rule.targetField,
            property: 'value',
            valueFunction: (values) => {
              return rule.sourceFields.reduce((sum, field) => {
                return sum + (parseFloat(values[field]) || 0);
              }, 0);
            }
          }
        ]
      });
    }
  });
  
  // 创建新引擎并设置规则
  const engine = new LinkageEngineV2();
  engine.setRules(newRules);
  
  return engine;
}