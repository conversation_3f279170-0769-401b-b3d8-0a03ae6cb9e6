/**
 * 联动配置构建器单元测试
 * 
 * 测试联动配置构建器的各种功能和API
 */

import { describe, it, expect } from 'vitest';
import { 
  createLinkageBuilder,
  type LinkageRule,
  type LinkageCondition,
  type LinkageAction
} from '../linkage-config-builder';

// ==================== 基础构建器测试 ====================

describe('LinkageConfigBuilder - 基础功能', () => {
  it('应该正确创建联动构建器', () => {
    const builder = createLinkageBuilder();
    expect(builder).toBeDefined();
    expect(typeof builder.createRule).toBe('function');
  });

  it('应该正确创建基础规则', () => {
    const builder = createLinkageBuilder();
    
    const rule = builder
      .createRule('test_rule', '测试规则')
      .when(field => field('category').eq('electronics'))
      .then(action => action.show('subcategory'))
      .build();

    expect(rule).toEqual({
      id: 'test_rule',
      target: 'subcategory',
      when: {
        field: 'category',
        operator: 'eq',
        value: 'electronics'
      },
      then: {
        type: 'show'
      },
      description: '测试规则'
    });
  });

  it('应该正确设置规则优先级和防抖', () => {
    const builder = createLinkageBuilder();
    
    const rule = builder
      .createRule('priority_rule', '优先级规则')
      .when(field => field('test').eq('value'))
      .then(action => action.show('target'))
      .priority('high')
      .debounce(500)
      .build();

    expect(rule.priority).toBe('high');
    expect(rule.debounce).toBe(500);
  });
});

// ==================== 条件构建器测试 ====================

describe('LinkageConfigBuilder - 条件构建', () => {
  let builder: ReturnType<typeof createLinkageBuilder>;

  beforeEach(() => {
    builder = createLinkageBuilder();
  });

  it('应该正确构建等于条件', () => {
    const rule = builder
      .createRule('eq_rule', '等于条件')
      .when(field => field('status').eq('active'))
      .then(action => action.show('details'))
      .build();

    expect(rule.when).toEqual({
      field: 'status',
      operator: 'eq',
      value: 'active'
    });
  });

  it('应该正确构建不等于条件', () => {
    const rule = builder
      .createRule('ne_rule', '不等于条件')
      .when(field => field('status').ne('inactive'))
      .then(action => action.show('details'))
      .build();

    expect(rule.when).toEqual({
      field: 'status',
      operator: 'ne',
      value: 'inactive'
    });
  });

  it('应该正确构建大于/小于条件', () => {
    const gtRule = builder
      .createRule('gt_rule', '大于条件')
      .when(field => field('price').gt(100))
      .then(action => action.show('discount'))
      .build();

    expect(gtRule.when).toEqual({
      field: 'price',
      operator: 'gt',
      value: 100
    });

    const ltRule = builder
      .createRule('lt_rule', '小于条件')
      .when(field => field('age').lt(18))
      .then(action => action.hide('adult_content'))
      .build();

    expect(ltRule.when).toEqual({
      field: 'age',
      operator: 'lt',
      value: 18
    });
  });

  it('应该正确构建大于等于/小于等于条件', () => {
    const gteRule = builder
      .createRule('gte_rule', '大于等于条件')
      .when(field => field('score').gte(60))
      .then(action => action.show('pass_message'))
      .build();

    expect(gteRule.when).toEqual({
      field: 'score',
      operator: 'gte',
      value: 60
    });

    const lteRule = builder
      .createRule('lte_rule', '小于等于条件')
      .when(field => field('quantity').lte(10))
      .then(action => action.show('low_stock_warning'))
      .build();

    expect(lteRule.when).toEqual({
      field: 'quantity',
      operator: 'lte',
      value: 10
    });
  });

  it('应该正确构建包含条件', () => {
    const inRule = builder
      .createRule('in_rule', '包含条件')
      .when(field => field('category').in(['electronics', 'computers', 'phones']))
      .then(action => action.show('tech_specs'))
      .build();

    expect(inRule.when).toEqual({
      field: 'category',
      operator: 'in',
      value: ['electronics', 'computers', 'phones']
    });

    const notInRule = builder
      .createRule('not_in_rule', '不包含条件')
      .when(field => field('status').notIn(['deleted', 'archived']))
      .then(action => action.show('edit_button'))
      .build();

    expect(notInRule.when).toEqual({
      field: 'status',
      operator: 'notIn',
      value: ['deleted', 'archived']
    });
  });

  it('应该正确构建字符串条件', () => {
    const containsRule = builder
      .createRule('contains_rule', '包含字符串条件')
      .when(field => field('description').contains('urgent'))
      .then(action => action.show('priority_field'))
      .build();

    expect(containsRule.when).toEqual({
      field: 'description',
      operator: 'contains',
      value: 'urgent'
    });

    const startsWithRule = builder
      .createRule('starts_with_rule', '开始字符串条件')
      .when(field => field('code').startsWith('PRE'))
      .then(action => action.show('prefix_info'))
      .build();

    expect(startsWithRule.when).toEqual({
      field: 'code',
      operator: 'startsWith',
      value: 'PRE'
    });

    const endsWithRule = builder
      .createRule('ends_with_rule', '结束字符串条件')
      .when(field => field('filename').endsWith('.pdf'))
      .then(action => action.show('pdf_viewer'))
      .build();

    expect(endsWithRule.when).toEqual({
      field: 'filename',
      operator: 'endsWith',
      value: '.pdf'
    });
  });

  it('应该正确构建空值条件', () => {
    const isEmptyRule = builder
      .createRule('is_empty_rule', '为空条件')
      .when(field => field('optional_field').isEmpty())
      .then(action => action.hide('dependent_field'))
      .build();

    expect(isEmptyRule.when).toEqual({
      field: 'optional_field',
      operator: 'isEmpty'
    });

    const isNotEmptyRule = builder
      .createRule('is_not_empty_rule', '不为空条件')
      .when(field => field('required_field').isNotEmpty())
      .then(action => action.show('next_step'))
      .build();

    expect(isNotEmptyRule.when).toEqual({
      field: 'required_field',
      operator: 'isNotEmpty'
    });
  });

  it('应该正确构建范围条件', () => {
    const betweenRule = builder
      .createRule('between_rule', '范围条件')
      .when(field => field('age').inRange(18, 65))
      .then(action => action.show('employment_section'))
      .build();

    expect(betweenRule.when).toEqual({
      field: 'age',
      operator: 'inRange',
      value: [18, 65]
    });
  });

  it('应该正确构建自定义条件', () => {
    const customFn = (value: any) => value && value.length > 5;
    
    const customRule = builder
      .createRule('custom_rule', '自定义条件')
      .when(field => field('password').custom(customFn))
      .then(action => action.show('strength_indicator'))
      .build();

    expect(customRule.when).toEqual({
      field: 'password',
      operator: 'custom',
      value: customFn
    });
  });
});

// ==================== 复合条件测试 ====================

describe('LinkageConfigBuilder - 复合条件', () => {
  let builder: ReturnType<typeof createLinkageBuilder>;

  beforeEach(() => {
    builder = createLinkageBuilder();
  });

  it('应该正确构建AND条件', () => {
    const rule = builder
      .createRule('and_rule', 'AND条件')
      .when(field => 
        field('userType').eq('vip')
          .and(field('orderAmount').gt(1000))
      )
      .then(action => action.show('vip_discount'))
      .build();

    expect(rule.when).toEqual({
      type: 'and',
      conditions: [
        {
          field: 'userType',
          operator: 'eq',
          value: 'vip'
        },
        {
          field: 'orderAmount',
          operator: 'gt',
          value: 1000
        }
      ]
    });
  });

  it('应该正确构建OR条件', () => {
    const rule = builder
      .createRule('or_rule', 'OR条件')
      .when(field => 
        field('paymentMethod').eq('credit_card')
          .or(field('paymentMethod').eq('paypal'))
      )
      .then(action => action.show('online_payment_info'))
      .build();

    expect(rule.when).toEqual({
      type: 'or',
      conditions: [
        {
          field: 'paymentMethod',
          operator: 'eq',
          value: 'credit_card'
        },
        {
          field: 'paymentMethod',
          operator: 'eq',
          value: 'paypal'
        }
      ]
    });
  });

  it('应该正确构建嵌套复合条件', () => {
    const rule = builder
      .createRule('nested_rule', '嵌套条件')
      .when(field => 
        field('userType').eq('premium')
          .and(
            field('region').eq('US')
              .or(field('region').eq('EU'))
          )
      )
      .then(action => action.show('premium_features'))
      .build();

    expect(rule.when).toEqual({
      type: 'and',
      conditions: [
        {
          field: 'userType',
          operator: 'eq',
          value: 'premium'
        },
        {
          type: 'or',
          conditions: [
            {
              field: 'region',
              operator: 'eq',
              value: 'US'
            },
            {
              field: 'region',
              operator: 'eq',
              value: 'EU'
            }
          ]
        }
      ]
    });
  });

  it('应该正确构建复杂的多层嵌套条件', () => {
    const rule = builder
      .createRule('complex_nested_rule', '复杂嵌套条件')
      .when(field => 
        field('status').eq('active')
          .and(
            field('userType').eq('vip')
              .or(
                field('totalSpent').gt(5000)
                  .and(field('membershipYears').gte(2))
              )
          )
      )
      .then(action => action.show('exclusive_offers'))
      .build();

    expect(rule.when).toEqual({
      type: 'and',
      conditions: [
        {
          field: 'status',
          operator: 'eq',
          value: 'active'
        },
        {
          type: 'or',
          conditions: [
            {
              field: 'userType',
              operator: 'eq',
              value: 'vip'
            },
            {
              type: 'and',
              conditions: [
                {
                  field: 'totalSpent',
                  operator: 'gt',
                  value: 5000
                },
                {
                  field: 'membershipYears',
                  operator: 'gte',
                  value: 2
                }
              ]
            }
          ]
        }
      ]
    });
  });
});

// ==================== 动作构建器测试 ====================

describe('LinkageConfigBuilder - 动作构建', () => {
  let builder: ReturnType<typeof createLinkageBuilder>;

  beforeEach(() => {
    builder = createLinkageBuilder();
  });

  it('应该正确构建显示/隐藏动作', () => {
    const showRule = builder
      .createRule('show_rule', '显示动作')
      .when(field => field('trigger').eq('show'))
      .then(action => action.show('target_field'))
      .build();

    expect(showRule.then).toEqual({ type: 'show' });
    expect(showRule.target).toBe('target_field');

    const hideRule = builder
      .createRule('hide_rule', '隐藏动作')
      .when(field => field('trigger').eq('hide'))
      .then(action => action.hide('target_field'))
      .build();

    expect(hideRule.then).toEqual({ type: 'hide' });
    expect(hideRule.target).toBe('target_field');
  });

  it('应该正确构建启用/禁用动作', () => {
    const enableRule = builder
      .createRule('enable_rule', '启用动作')
      .when(field => field('permission').eq('admin'))
      .then(action => action.enable('admin_panel'))
      .build();

    expect(enableRule.then).toEqual({ type: 'enable' });
    expect(enableRule.target).toBe('admin_panel');

    const disableRule = builder
      .createRule('disable_rule', '禁用动作')
      .when(field => field('readonly').eq(true))
      .then(action => action.disable('edit_button'))
      .build();

    expect(disableRule.then).toEqual({ type: 'disable' });
    expect(disableRule.target).toBe('edit_button');
  });

  it('应该正确构建设置值动作', () => {
    const setValueRule = builder
      .createRule('set_value_rule', '设置值动作')
      .when(field => field('auto_fill').eq(true))
      .then(action => action.setValue('target_field', 'default_value'))
      .build();

    expect(setValueRule.then).toEqual({
      type: 'setValue',
      value: 'default_value'
    });
    expect(setValueRule.target).toBe('target_field');
  });

  it('应该正确构建清空值动作', () => {
    const clearValueRule = builder
      .createRule('clear_value_rule', '清空值动作')
      .when(field => field('reset').eq(true))
      .then(action => action.clearValue('target_field'))
      .build();

    expect(clearValueRule.then).toEqual({ type: 'clearValue' });
    expect(clearValueRule.target).toBe('target_field');
  });

  it('应该正确构建更新选项动作', () => {
    const options = [
      { label: '选项1', value: '1' },
      { label: '选项2', value: '2' }
    ];

    const updateOptionsRule = builder
      .createRule('update_options_rule', '更新选项动作')
      .when(field => field('category').eq('type1'))
      .then(action => action.updateOptions('subcategory', options))
      .build();

    expect(updateOptionsRule.then).toEqual({
      type: 'updateOptions',
      options
    });
    expect(updateOptionsRule.target).toBe('subcategory');
  });

  it('应该正确构建计算动作', () => {
    const calculateFn = (values: any) => values.price * values.quantity;

    const calculateRule = builder
      .createRule('calculate_rule', '计算动作')
      .when(field => field('price').isNotEmpty().and(field('quantity').isNotEmpty()))
      .then(action => action.calculate('total', calculateFn))
      .build();

    expect(calculateRule.then).toEqual({
      type: 'calculate',
      expression: calculateFn
    });
    expect(calculateRule.target).toBe('total');
  });

  it('应该正确构建设置必填动作', () => {
    const requiredRule = builder
      .createRule('required_rule', '设置必填动作')
      .when(field => field('type').eq('required_type'))
      .then(action => action.setRequired('target_field', true))
      .build();

    expect(requiredRule.then).toEqual({
      type: 'setRequired',
      required: true
    });
    expect(requiredRule.target).toBe('target_field');

    const optionalRule = builder
      .createRule('optional_rule', '设置可选动作')
      .when(field => field('type').eq('optional_type'))
      .then(action => action.setRequired('target_field', false))
      .build();

    expect(optionalRule.then).toEqual({
      type: 'setRequired',
      required: false
    });
  });

  it('应该正确构建批量更新动作', () => {
    const updates = [
      { target: 'field1', action: { type: 'show' as const } },
      { target: 'field2', action: { type: 'setValue' as const, value: 'test' } },
      { target: 'field3', action: { type: 'hide' as const } }
    ];

    const batchUpdateRule = builder
      .createRule('batch_update_rule', '批量更新动作')
      .when(field => field('trigger').eq('batch'))
      .then(action => action.batchUpdate('*', updates))
      .build();

    expect(batchUpdateRule.then).toEqual({
      type: 'batchUpdate',
      updates
    });
    expect(batchUpdateRule.target).toBe('*');
  });
});

// ==================== 链式调用测试 ====================

describe('LinkageConfigBuilder - 链式调用', () => {
  it('应该支持复杂的链式调用', () => {
    const builder = createLinkageBuilder();
    
    const rule = builder
      .createRule('complex_chain_rule', '复杂链式调用规则')
      .when(field => 
        field('userType').eq('premium')
          .and(field('region').in(['US', 'EU', 'JP']))
          .and(
            field('orderAmount').gt(500)
              .or(field('loyaltyPoints').gte(1000))
          )
      )
      .then(action => 
        action.show('premium_shipping')
          .setValue('shipping_cost', 0)
          .updateOptions('shipping_method', [
            { label: '免费快递', value: 'free_express' },
            { label: '免费标准', value: 'free_standard' }
          ])
      )
      .priority('high')
      .debounce(200)
      .build();

    expect(rule.id).toBe('complex_chain_rule');
    expect(rule.description).toBe('复杂链式调用规则');
    expect(rule.priority).toBe('high');
    expect(rule.debounce).toBe(200);
    expect(rule.when.type).toBe('and');
    expect(rule.then.type).toBe('batchUpdate');
  });

  it('应该正确处理多个动作的链式调用', () => {
    const builder = createLinkageBuilder();
    
    const rule = builder
      .createRule('multi_action_rule', '多动作规则')
      .when(field => field('status').eq('approved'))
      .then(action => 
        action.show('approval_date')
          .show('approver_name')
          .enable('submit_button')
          .setValue('workflow_status', 'ready_for_submission')
          .setRequired('final_review', true)
      )
      .build();

    expect(rule.then.type).toBe('batchUpdate');
    expect(rule.then.updates).toHaveLength(5);
    
    const updates = rule.then.updates!;
    expect(updates[0]).toEqual({ target: 'approval_date', action: { type: 'show' } });
    expect(updates[1]).toEqual({ target: 'approver_name', action: { type: 'show' } });
    expect(updates[2]).toEqual({ target: 'submit_button', action: { type: 'enable' } });
    expect(updates[3]).toEqual({ 
      target: 'workflow_status', 
      action: { type: 'setValue', value: 'ready_for_submission' } 
    });
    expect(updates[4]).toEqual({ 
      target: 'final_review', 
      action: { type: 'setRequired', required: true } 
    });
  });
});

// ==================== 错误处理测试 ====================

describe('LinkageConfigBuilder - 错误处理', () => {
  it('应该在缺少必要参数时抛出错误', () => {
    const builder = createLinkageBuilder();
    
    expect(() => {
      builder.createRule('', '').build();
    }).toThrow();
    
    expect(() => {
      builder
        .createRule('test', '测试')
        .when(field => field('test').eq('value'))
        .build(); // 缺少then
    }).toThrow();
    
    expect(() => {
      builder
        .createRule('test', '测试')
        .then(action => action.show('target'))
        .build(); // 缺少when
    }).toThrow();
  });

  it('应该在无效的字段名时抛出错误', () => {
    const builder = createLinkageBuilder();
    
    expect(() => {
      builder
        .createRule('test', '测试')
        .when(field => field('').eq('value'))
        .then(action => action.show('target'))
        .build();
    }).toThrow();
  });

  it('应该在无效的目标字段时抛出错误', () => {
    const builder = createLinkageBuilder();
    
    expect(() => {
      builder
        .createRule('test', '测试')
        .when(field => field('source').eq('value'))
        .then(action => action.show(''))
        .build();
    }).toThrow();
  });
});

// ==================== 实际使用场景测试 ====================

describe('LinkageConfigBuilder - 实际使用场景', () => {
  it('应该正确构建用户注册表单联动', () => {
    const builder = createLinkageBuilder();
    
    const rules = [
      // 用户类型联动
      builder
        .createRule('user_type_company', '企业用户显示公司信息')
        .when(field => field('userType').eq('company'))
        .then(action => 
          action.show('companyName')
            .show('taxId')
            .show('businessLicense')
            .setRequired('companyName', true)
            .setRequired('taxId', true)
        )
        .build(),
      
      // 地区城市联动
      builder
        .createRule('region_city_linkage', '地区城市联动')
        .when(field => field('region').eq('guangdong'))
        .then(action => action.updateOptions('city', [
          { label: '广州', value: 'guangzhou' },
          { label: '深圳', value: 'shenzhen' },
          { label: '珠海', value: 'zhuhai' }
        ]))
        .build(),
      
      // 年龄验证联动
      builder
        .createRule('age_verification', '年龄验证')
        .when(field => field('age').lt(18))
        .then(action => 
          action.show('guardian_info')
            .setRequired('guardian_name', true)
            .setRequired('guardian_phone', true)
            .hide('adult_services')
        )
        .build()
    ];

    expect(rules).toHaveLength(3);
    expect(rules[0].id).toBe('user_type_company');
    expect(rules[1].id).toBe('region_city_linkage');
    expect(rules[2].id).toBe('age_verification');
  });

  it('应该正确构建电商订单表单联动', () => {
    const builder = createLinkageBuilder();
    
    const rules = [
      // 商品分类联动
      builder
        .createRule('product_category_linkage', '商品分类联动')
        .when(field => field('category').eq('electronics'))
        .then(action => action.updateOptions('subcategory', [
          { label: '手机', value: 'phone' },
          { label: '电脑', value: 'computer' },
          { label: '平板', value: 'tablet' }
        ]))
        .build(),
      
      // 价格折扣联动
      builder
        .createRule('price_discount_linkage', '价格折扣联动')
        .when(field => 
          field('totalAmount').gt(1000)
            .and(field('userLevel').in(['vip', 'premium']))
        )
        .then(action => 
          action.show('discountCode')
            .setValue('discountRate', 0.1)
            .calculate('finalAmount', (values) => {
              const total = Number(values.totalAmount) || 0;
              const discount = Number(values.discountRate) || 0;
              return total * (1 - discount);
            })
        )
        .priority('high')
        .build(),
      
      // 配送方式联动
      builder
        .createRule('shipping_method_linkage', '配送方式联动')
        .when(field => 
          field('region').eq('local')
            .and(field('urgency').eq('same_day'))
        )
        .then(action => action.updateOptions('shippingMethod', [
          { label: '同城快递', value: 'local_express' },
          { label: '专人配送', value: 'personal_delivery' }
        ]))
        .build()
    ];

    expect(rules).toHaveLength(3);
    
    // 验证复杂条件
    const priceRule = rules[1];
    expect(priceRule.when.type).toBe('and');
    expect(priceRule.then.type).toBe('batchUpdate');
    expect(priceRule.priority).toBe('high');
  });
});