import { defineConfig } from '@vben/vite-config';

import { loadEnv } from 'vite';
import fs from 'fs';
import path from 'path';

import { ProxyManager } from './proxy-manager';

export default defineConfig(async (config) => {
  const mode = config?.mode || 'development';

  // 正确加载环境变量
  const env = loadEnv(mode, '.', '');

  const proxyConfig = ProxyManager.getProxyConfig(env);
  
  // 获取当前时间戳作为构建时间
  const buildTime = Date.now();
  const appVersion = process.env.npm_package_version || '1.0.0';

  return {
    application: {},
    vite: {
      define: {
        __BUILD_TIME__: JSON.stringify(buildTime),
        __APP_VERSION__: JSON.stringify(appVersion)
      },
      resolve: {
        alias: {
          '#': '/src', // 假设 # 指向 src 目录
        },
      },
      server: {
        proxy: proxyConfig,
      },
      // 构建时生成版本文件
      plugins: [
        {
          name: 'generate-version-file',
          writeBundle() {
            const versionInfo = {
              version: appVersion,
              buildTime: buildTime,
              timestamp: new Date().toISOString()
            };
            const outputPath = path.resolve(__dirname, 'dist/version.json');
            fs.writeFileSync(outputPath, JSON.stringify(versionInfo, null, 2));
            console.log('✅ version.json 文件已生成');
          }
        }
      ]
    },
  };
});
