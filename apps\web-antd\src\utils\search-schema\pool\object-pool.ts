/**
 * 对象池管理器
 * 实现对象池模式，减少对象创建和垃圾回收的开销
 */

import type { VbenFormSchema } from '#/adapter/form';

/**
 * 对象池配置接口
 */
interface PoolConfig<T> {
  maxSize: number;                  // 最大池大小
  createFn: () => T;                // 创建函数
  resetFn: (obj: T) => void;        // 重置函数
  validateFn?: (obj: T) => boolean; // 验证函数
}

/**
 * 对象池指标接口
 */
interface PoolMetrics {
  totalCreated: number;             // 总创建数
  totalAcquired: number;            // 总获取数
  totalReleased: number;            // 总释放数
  currentPoolSize: number;          // 当前池大小
  hitRate: number;                  // 命中率
}

/**
 * 通用对象池类
 * 使用泛型支持不同类型的对象池化
 */
export class ObjectPool<T> {
  private pool: T[] = [];
  private config: PoolConfig<T>;
  private metrics: PoolMetrics = {
    totalCreated: 0,
    totalAcquired: 0,
    totalReleased: 0,
    currentPoolSize: 0,
    hitRate: 0,
  };

  constructor(config: PoolConfig<T>) {
    this.config = config;
    this.preAllocate();
  }

  /**
   * 预分配对象
   * 在池初始化时预先创建一些对象
   */
  private preAllocate(): void {
    const preAllocateCount = Math.min(10, this.config.maxSize);
    for (let i = 0; i < preAllocateCount; i++) {
      const obj = this.config.createFn();
      this.pool.push(obj);
      this.metrics.totalCreated++;
    }
    this.metrics.currentPoolSize = this.pool.length;
  }

  /**
   * 获取对象
   * 从池中获取对象，如果池为空则创建新对象
   */
  acquire(): T {
    this.metrics.totalAcquired++;
    
    if (this.pool.length > 0) {
      const obj = this.pool.pop()!;
      this.metrics.currentPoolSize = this.pool.length;
      this.updateHitRate();
      return obj;
    }
    
    // 池为空，创建新对象
    this.metrics.totalCreated++;
    this.updateHitRate();
    return this.config.createFn();
  }

  /**
   * 释放对象
   * 将对象重置后放回池中
   */
  release(obj: T): void {
    if (!obj) return;
    
    // 验证对象是否有效
    if (this.config.validateFn && !this.config.validateFn(obj)) {
      return;
    }
    
    // 如果池未满，重置对象并放回池中
    if (this.pool.length < this.config.maxSize) {
      this.config.resetFn(obj);
      this.pool.push(obj);
      this.metrics.totalReleased++;
      this.metrics.currentPoolSize = this.pool.length;
    }
    // 如果池已满，让对象被垃圾回收
  }

  /**
   * 更新命中率
   */
  private updateHitRate(): void {
    if (this.metrics.totalAcquired > 0) {
      const hits = this.metrics.totalAcquired - this.metrics.totalCreated + this.pool.length;
      this.metrics.hitRate = (hits / this.metrics.totalAcquired) * 100;
    }
  }

  /**
   * 清空池
   */
  clear(): void {
    this.pool.length = 0;
    this.metrics.currentPoolSize = 0;
  }

  /**
   * 获取池统计信息
   */
  getMetrics(): PoolMetrics {
    return { ...this.metrics };
  }

  /**
   * 获取当前池大小
   */
  get size(): number {
    return this.pool.length;
  }

  /**
   * 获取最大池大小
   */
  get maxSize(): number {
    return this.config.maxSize;
  }
}

/**
 * 组件属性对象池
 * 专门用于管理组件属性对象的池化
 */
class ComponentPropsPool extends ObjectPool<Record<string, any>> {
  constructor(maxSize = 100) {
    super({
      maxSize,
      createFn: () => ({}),
      resetFn: (obj) => {
        // 清空对象的所有属性
        Object.keys(obj).forEach(key => delete obj[key]);
      },
      validateFn: (obj) => typeof obj === 'object' && obj !== null,
    });
  }

  /**
   * 获取组件属性对象
   * 返回一个干净的对象，可以安全地添加属性
   */
  acquireComponentProps(): Record<string, any> {
    const props = this.acquire();
    // 确保对象是干净的
    if (Object.keys(props).length > 0) {
      Object.keys(props).forEach(key => delete props[key]);
    }
    return props;
  }

  /**
   * 释放组件属性对象
   * 清理对象后放回池中
   */
  releaseComponentProps(props: Record<string, any>): void {
    if (props && typeof props === 'object') {
      this.release(props);
    }
  }
}

/**
 * Schema对象池
 * 专门用于管理VbenFormSchema对象的池化
 */
class SchemaPool extends ObjectPool<Partial<VbenFormSchema>> {
  constructor(maxSize = 50) {
    super({
      maxSize,
      createFn: () => ({}),
      resetFn: (obj) => {
        // 清空schema对象的所有属性
        Object.keys(obj).forEach(key => delete (obj as any)[key]);
      },
      validateFn: (obj) => typeof obj === 'object' && obj !== null,
    });
  }

  /**
   * 获取Schema对象
   * 返回一个干净的Schema对象
   */
  acquireSchema(): Partial<VbenFormSchema> {
    const schema = this.acquire();
    // 确保对象是干净的
    if (Object.keys(schema).length > 0) {
      Object.keys(schema).forEach(key => delete (schema as any)[key]);
    }
    return schema;
  }

  /**
   * 释放Schema对象
   * 清理对象后放回池中
   */
  releaseSchema(schema: Partial<VbenFormSchema>): void {
    if (schema && typeof schema === 'object') {
      this.release(schema);
    }
  }
}

/**
 * 数组对象池
 * 用于管理数组对象的池化
 */
class ArrayPool extends ObjectPool<any[]> {
  constructor(maxSize = 30) {
    super({
      maxSize,
      createFn: () => [],
      resetFn: (arr) => {
        // 清空数组
        arr.length = 0;
      },
      validateFn: (arr) => Array.isArray(arr),
    });
  }

  /**
   * 获取数组
   * 返回一个空数组
   */
  acquireArray(): any[] {
    const arr = this.acquire();
    // 确保数组是空的
    if (arr.length > 0) {
      arr.length = 0;
    }
    return arr;
  }

  /**
   * 释放数组
   * 清空数组后放回池中
   */
  releaseArray(arr: any[]): void {
    if (Array.isArray(arr)) {
      this.release(arr);
    }
  }
}

// 创建全局对象池实例
export const componentPropsPool = new ComponentPropsPool();
export const schemaPool = new SchemaPool();
export const arrayPool = new ArrayPool();

/**
 * 对象池管理器
 * 统一管理所有对象池
 */
export class PoolManager {
  private static instance: PoolManager;
  private pools: Map<string, ObjectPool<any>> = new Map();

  private constructor() {
    // 注册默认对象池
    this.pools.set('componentProps', componentPropsPool);
    this.pools.set('schema', schemaPool);
    this.pools.set('array', arrayPool);
  }

  /**
   * 获取单例实例
   */
  static getInstance(): PoolManager {
    if (!PoolManager.instance) {
      PoolManager.instance = new PoolManager();
    }
    return PoolManager.instance;
  }

  /**
   * 注册对象池
   */
  registerPool<T>(name: string, pool: ObjectPool<T>): void {
    this.pools.set(name, pool);
  }

  /**
   * 获取对象池
   */
  getPool<T>(name: string): ObjectPool<T> | undefined {
    return this.pools.get(name);
  }

  /**
   * 获取所有对象池的统计信息
   */
  getAllMetrics(): Record<string, PoolMetrics> {
    const metrics: Record<string, PoolMetrics> = {};
    for (const [name, pool] of this.pools.entries()) {
      metrics[name] = pool.getMetrics();
    }
    return metrics;
  }

  /**
   * 清空所有对象池
   */
  clearAllPools(): void {
    for (const pool of this.pools.values()) {
      pool.clear();
    }
  }

  /**
   * 获取统计信息（别名）
   */
  getStats() {
    return this.getTotalStats();
  }

  /**
   * 获取总体统计信息
   */
  getTotalStats() {
    const allMetrics = this.getAllMetrics();
    const totalStats = {
      totalPools: this.pools.size,
      totalObjects: 0,
      totalCreated: 0,
      totalAcquired: 0,
      totalReleased: 0,
      averageHitRate: 0,
    };

    let totalHitRate = 0;
    let poolsWithHitRate = 0;

    for (const metrics of Object.values(allMetrics)) {
      totalStats.totalObjects += metrics.currentPoolSize;
      totalStats.totalCreated += metrics.totalCreated;
      totalStats.totalAcquired += metrics.totalAcquired;
      totalStats.totalReleased += metrics.totalReleased;
      
      if (metrics.hitRate > 0) {
        totalHitRate += metrics.hitRate;
        poolsWithHitRate++;
      }
    }

    if (poolsWithHitRate > 0) {
      totalStats.averageHitRate = totalHitRate / poolsWithHitRate;
    }

    return totalStats;
  }
}

// 导出全局池管理器实例
export const poolManager = PoolManager.getInstance();

/**
 * 便捷函数：获取组件属性对象
 */
export function acquireComponentProps(): Record<string, any> {
  return componentPropsPool.acquireComponentProps();
}

/**
 * 便捷函数：释放组件属性对象
 */
export function releaseComponentProps(props: Record<string, any>): void {
  componentPropsPool.releaseComponentProps(props);
}

/**
 * 便捷函数：获取Schema对象
 */
export function acquireSchema(): Partial<VbenFormSchema> {
  return schemaPool.acquireSchema();
}

/**
 * 便捷函数：释放Schema对象
 */
export function releaseSchema(schema: Partial<VbenFormSchema>): void {
  schemaPool.releaseSchema(schema);
}

/**
 * 便捷函数：获取数组
 */
export function acquireArray(): any[] {
  return arrayPool.acquireArray();
}

/**
 * 便捷函数：释放数组
 */
export function releaseArray(arr: any[]): void {
  arrayPool.releaseArray(arr);
}