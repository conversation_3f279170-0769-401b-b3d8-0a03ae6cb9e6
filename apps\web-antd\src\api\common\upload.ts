import { requestClient } from '#/api/request';

enum Api {
  CommonFileUpload = '/oss/putFile',
  CommonImgUpload = '/oss/putImg',
}

// 暴露 Api enum 供中间件使用
export { Api };

/**
 * 附件上传
 */
export async function CommonFileUpload(params: any) {
  return requestClient.post(Api.CommonFileUpload, params);
}

/**
 * 图片上传
 */
export async function CommonImgUpload(params: any) {
  return requestClient.post(Api.CommonImgUpload, params);
}
