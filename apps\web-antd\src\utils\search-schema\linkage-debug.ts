/**
 * 联动引擎调试和错误处理工具
 * 
 * 提供详细的调试信息、错误追踪和问题诊断功能
 * 降低后端调试成本，提升开发效率
 */

import type { LinkageEngineV2, SimpleLinkageRule, SimpleCondition, LinkageAction } from './linkage-engine-v2';
import type { PerformanceMetrics } from './linkage-performance';

// ==================== 调试类型定义 ====================

/** 调试级别 */
export type DebugLevel = 'error' | 'warn' | 'info' | 'debug' | 'trace';

/** 调试事件 */
export interface DebugEvent {
  id: string;
  timestamp: number;
  level: DebugLevel;
  category: 'rule' | 'condition' | 'action' | 'cache' | 'performance' | 'error';
  message: string;
  data?: any;
  stack?: string;
  ruleId?: string;
  fieldName?: string;
  duration?: number;
}

/** 规则执行上下文 */
export interface RuleExecutionContext {
  ruleId: string;
  target: string;
  triggerField: string;
  triggerValue: any;
  conditions: SimpleCondition[];
  actions: LinkageAction[];
  startTime: number;
  endTime?: number;
  success: boolean;
  error?: Error;
  conditionResults?: boolean[];
  actionResults?: any[];
}

/** 字段状态快照 */
export interface FieldStateSnapshot {
  timestamp: number;
  fieldName: string;
  value: any;
  visible: boolean;
  disabled: boolean;
  required: boolean;
  options?: Array<{ label: string; value: any }>;
  props?: Record<string, any>;
  changeReason: string;
  triggeredBy?: string;
}

/** 调试配置 */
export interface DebugConfig {
  enabled: boolean;
  level: DebugLevel;
  categories: string[];
  maxEvents: number;
  captureSnapshots: boolean;
  logToConsole: boolean;
  logToStorage: boolean;
}

// ==================== 调试器实现 ====================

export class LinkageDebugger {
  private events: DebugEvent[] = [];
  private snapshots: FieldStateSnapshot[] = [];
  private executionContexts: Map<string, RuleExecutionContext> = new Map();
  private config: DebugConfig = {
    enabled: false,
    level: 'info',
    categories: ['rule', 'condition', 'action', 'error'],
    maxEvents: 1000,
    captureSnapshots: true,
    logToConsole: true,
    logToStorage: false
  };
  private eventIdCounter = 0;

  /**
   * 配置调试器
   */
  configure(config: Partial<DebugConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 启用调试
   */
  enable(level: DebugLevel = 'info'): void {
    this.config.enabled = true;
    this.config.level = level;
    this.log('info', 'debug', '联动调试器已启用', { level });
  }

  /**
   * 禁用调试
   */
  disable(): void {
    this.config.enabled = false;
    this.log('info', 'debug', '联动调试器已禁用');
  }

  /**
   * 清空调试数据
   */
  clear(): void {
    this.events = [];
    this.snapshots = [];
    this.executionContexts.clear();
    this.log('info', 'debug', '调试数据已清空');
  }

  /**
   * 记录调试事件
   */
  log(
    level: DebugLevel,
    category: string,
    message: string,
    data?: any,
    ruleId?: string,
    fieldName?: string
  ): void {
    if (!this.config.enabled || !this.shouldLog(level, category)) {
      return;
    }

    const event: DebugEvent = {
      id: `debug_${++this.eventIdCounter}`,
      timestamp: Date.now(),
      level,
      category: category as any,
      message,
      data,
      ruleId,
      fieldName
    };

    // 添加错误堆栈
    if (level === 'error' && data instanceof Error) {
      event.stack = data.stack;
    }

    this.events.push(event);

    // 限制事件数量
    if (this.events.length > this.config.maxEvents) {
      this.events.shift();
    }

    // 输出到控制台
    if (this.config.logToConsole) {
      this.logToConsole(event);
    }

    // 保存到本地存储
    if (this.config.logToStorage) {
      this.logToStorage(event);
    }
  }

  /**
   * 开始规则执行追踪
   */
  startRuleExecution(rule: SimpleLinkageRule, triggerField: string, triggerValue: any): string {
    const contextId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const context: RuleExecutionContext = {
      ruleId: rule.id,
      target: rule.target,
      triggerField,
      triggerValue,
      conditions: Array.isArray(rule.when) ? rule.when : [rule.when],
      actions: Array.isArray(rule.then) ? rule.then : [rule.then],
      startTime: performance.now(),
      success: false
    };

    this.executionContexts.set(contextId, context);
    
    this.log('debug', 'rule', `开始执行规则: ${rule.id}`, {
      target: rule.target,
      triggerField,
      triggerValue,
      description: rule.description
    }, rule.id);

    return contextId;
  }

  /**
   * 记录条件评估结果
   */
  logConditionResult(contextId: string, conditionIndex: number, condition: SimpleCondition, result: boolean): void {
    const context = this.executionContexts.get(contextId);
    if (!context) return;

    if (!context.conditionResults) {
      context.conditionResults = [];
    }
    context.conditionResults[conditionIndex] = result;

    this.log('trace', 'condition', `条件评估: ${condition.field} ${condition.operator} ${condition.value} = ${result}`, {
      condition,
      result,
      index: conditionIndex
    }, context.ruleId, condition.field);
  }

  /**
   * 记录动作执行结果
   */
  logActionResult(contextId: string, actionIndex: number, action: LinkageAction, result: any): void {
    const context = this.executionContexts.get(contextId);
    if (!context) return;

    if (!context.actionResults) {
      context.actionResults = [];
    }
    context.actionResults[actionIndex] = result;

    this.log('trace', 'action', `动作执行: ${action.type}`, {
      action,
      result,
      index: actionIndex
    }, context.ruleId, context.target);
  }

  /**
   * 结束规则执行追踪
   */
  endRuleExecution(contextId: string, success: boolean, error?: Error): void {
    const context = this.executionContexts.get(contextId);
    if (!context) return;

    context.endTime = performance.now();
    context.success = success;
    context.error = error;

    const duration = context.endTime - context.startTime;

    if (success) {
      this.log('debug', 'rule', `规则执行成功: ${context.ruleId}`, {
        duration: `${duration.toFixed(2)}ms`,
        conditionResults: context.conditionResults,
        actionResults: context.actionResults
      }, context.ruleId);
    } else {
      this.log('error', 'rule', `规则执行失败: ${context.ruleId}`, {
        duration: `${duration.toFixed(2)}ms`,
        error: error?.message,
        stack: error?.stack
      }, context.ruleId);
    }

    // 清理上下文
    this.executionContexts.delete(contextId);
  }

  /**
   * 记录字段状态快照
   */
  captureFieldSnapshot(
    fieldName: string,
    value: any,
    state: {
      visible: boolean;
      disabled: boolean;
      required: boolean;
      options?: Array<{ label: string; value: any }>;
      props?: Record<string, any>;
    },
    changeReason: string,
    triggeredBy?: string
  ): void {
    if (!this.config.enabled || !this.config.captureSnapshots) {
      return;
    }

    const snapshot: FieldStateSnapshot = {
      timestamp: Date.now(),
      fieldName,
      value,
      ...state,
      changeReason,
      triggeredBy
    };

    this.snapshots.push(snapshot);

    // 限制快照数量
    if (this.snapshots.length > this.config.maxEvents) {
      this.snapshots.shift();
    }

    this.log('trace', 'field', `字段状态变更: ${fieldName}`, {
      value,
      state,
      changeReason,
      triggeredBy
    }, undefined, fieldName);
  }

  /**
   * 获取调试事件
   */
  getEvents(filter?: {
    level?: DebugLevel;
    category?: string;
    ruleId?: string;
    fieldName?: string;
    startTime?: number;
    endTime?: number;
  }): DebugEvent[] {
    let events = [...this.events];

    if (filter) {
      events = events.filter(event => {
        if (filter.level && !this.isLevelIncluded(event.level, filter.level)) {
          return false;
        }
        if (filter.category && event.category !== filter.category) {
          return false;
        }
        if (filter.ruleId && event.ruleId !== filter.ruleId) {
          return false;
        }
        if (filter.fieldName && event.fieldName !== filter.fieldName) {
          return false;
        }
        if (filter.startTime && event.timestamp < filter.startTime) {
          return false;
        }
        if (filter.endTime && event.timestamp > filter.endTime) {
          return false;
        }
        return true;
      });
    }

    return events;
  }

  /**
   * 获取字段状态历史
   */
  getFieldHistory(fieldName: string): FieldStateSnapshot[] {
    return this.snapshots.filter(snapshot => snapshot.fieldName === fieldName);
  }

  /**
   * 获取规则执行统计
   */
  getRuleStats(): Map<string, {
    executionCount: number;
    successCount: number;
    failureCount: number;
    averageDuration: number;
    lastExecution?: number;
  }> {
    const stats = new Map();
    
    const ruleEvents = this.events.filter(e => e.category === 'rule' && e.ruleId);
    
    for (const event of ruleEvents) {
      const ruleId = event.ruleId!;
      
      if (!stats.has(ruleId)) {
        stats.set(ruleId, {
          executionCount: 0,
          successCount: 0,
          failureCount: 0,
          averageDuration: 0,
          durations: []
        });
      }
      
      const stat = stats.get(ruleId);
      
      if (event.message.includes('开始执行')) {
        stat.executionCount++;
        stat.lastExecution = event.timestamp;
      } else if (event.message.includes('执行成功')) {
        stat.successCount++;
        if (event.duration) {
          stat.durations.push(event.duration);
        }
      } else if (event.message.includes('执行失败')) {
        stat.failureCount++;
      }
    }
    
    // 计算平均执行时间
    for (const [ruleId, stat] of stats) {
      if (stat.durations.length > 0) {
        stat.averageDuration = stat.durations.reduce((sum: number, d: number) => sum + d, 0) / stat.durations.length;
      }
      delete stat.durations;
    }
    
    return stats;
  }

  /**
   * 生成调试报告
   */
  generateReport(): string {
    const stats = this.getRuleStats();
    const errorEvents = this.getEvents({ level: 'error' });
    const warnEvents = this.getEvents({ level: 'warn' });
    
    let report = '# 联动引擎调试报告\n\n';
    
    // 基本统计
    report += '## 基本统计\n\n';
    report += `- 总事件数: ${this.events.length}\n`;
    report += `- 错误事件: ${errorEvents.length}\n`;
    report += `- 警告事件: ${warnEvents.length}\n`;
    report += `- 字段快照: ${this.snapshots.length}\n`;
    report += `- 活跃规则: ${stats.size}\n\n`;
    
    // 规则执行统计
    if (stats.size > 0) {
      report += '## 规则执行统计\n\n';
      report += '| 规则ID | 执行次数 | 成功次数 | 失败次数 | 平均耗时(ms) |\n';
      report += '|--------|----------|----------|----------|--------------|\n';
      
      for (const [ruleId, stat] of stats) {
        report += `| ${ruleId} | ${stat.executionCount} | ${stat.successCount} | ${stat.failureCount} | ${stat.averageDuration.toFixed(2)} |\n`;
      }
      report += '\n';
    }
    
    // 错误详情
    if (errorEvents.length > 0) {
      report += '## 错误详情\n\n';
      errorEvents.slice(-10).forEach((event, index) => {
        report += `### 错误 ${index + 1}\n\n`;
        report += `- **时间**: ${new Date(event.timestamp).toLocaleString()}\n`;
        report += `- **规则**: ${event.ruleId || 'N/A'}\n`;
        report += `- **字段**: ${event.fieldName || 'N/A'}\n`;
        report += `- **消息**: ${event.message}\n`;
        if (event.stack) {
          report += `- **堆栈**: \`\`\`\n${event.stack}\n\`\`\`\n`;
        }
        report += '\n';
      });
    }
    
    return report;
  }

  /**
   * 导出调试数据
   */
  exportData(): {
    events: DebugEvent[];
    snapshots: FieldStateSnapshot[];
    stats: any;
    config: DebugConfig;
  } {
    return {
      events: [...this.events],
      snapshots: [...this.snapshots],
      stats: Object.fromEntries(this.getRuleStats()),
      config: { ...this.config }
    };
  }

  /**
   * 导入调试数据
   */
  importData(data: {
    events?: DebugEvent[];
    snapshots?: FieldStateSnapshot[];
    config?: Partial<DebugConfig>;
  }): void {
    if (data.events) {
      this.events = [...data.events];
    }
    if (data.snapshots) {
      this.snapshots = [...data.snapshots];
    }
    if (data.config) {
      this.config = { ...this.config, ...data.config };
    }
  }

  /**
   * 判断是否应该记录日志
   */
  private shouldLog(level: DebugLevel, category: string): boolean {
    if (!this.config.categories.includes(category)) {
      return false;
    }
    
    return this.isLevelIncluded(level, this.config.level);
  }

  /**
   * 判断日志级别是否包含
   */
  private isLevelIncluded(eventLevel: DebugLevel, configLevel: DebugLevel): boolean {
    const levels = ['error', 'warn', 'info', 'debug', 'trace'];
    const eventIndex = levels.indexOf(eventLevel);
    const configIndex = levels.indexOf(configLevel);
    return eventIndex <= configIndex;
  }

  /**
   * 输出到控制台
   */
  private logToConsole(event: DebugEvent): void {
    const prefix = `[Linkage ${event.level.toUpperCase()}]`;
    const message = `${prefix} ${event.message}`;
    
    switch (event.level) {
      case 'error':
        console.error(message, event.data);
        break;
      case 'warn':
        console.warn(message, event.data);
        break;
      case 'info':
        console.info(message, event.data);
        break;
      case 'debug':
      case 'trace':
        console.debug(message, event.data);
        break;
    }
  }

  /**
   * 保存到本地存储
   */
  private logToStorage(event: DebugEvent): void {
    try {
      const key = `linkage_debug_${new Date().toISOString().split('T')[0]}`;
      const existing = localStorage.getItem(key);
      const events = existing ? JSON.parse(existing) : [];
      events.push(event);
      
      // 限制存储大小
      if (events.length > 500) {
        events.splice(0, events.length - 500);
      }
      
      localStorage.setItem(key, JSON.stringify(events));
    } catch (error) {
      console.warn('无法保存调试日志到本地存储:', error);
    }
  }
}

// ==================== 错误处理器 ====================

/**
 * 联动错误类
 */
export class LinkageError extends Error {
  constructor(
    message: string,
    public code: string,
    public ruleId?: string,
    public fieldName?: string,
    public context?: any
  ) {
    super(message);
    this.name = 'LinkageError';
  }
}

/**
 * 错误处理器
 */
export class LinkageErrorHandler {
  private debuggerInstance?: LinkageDebugger;

  constructor(debuggerInstance?: LinkageDebugger) {
    this.debuggerInstance = debuggerInstance;
  }

  /**
   * 处理错误
   */
  handleError(error: Error | LinkageError, context?: any): void {
    if (error instanceof LinkageError) {
      this.handleLinkageError(error);
    } else {
      this.handleGenericError(error, context);
    }
  }

  /**
   * 处理联动错误
   */
  private handleLinkageError(error: LinkageError): void {
    this.debuggerInstance?.log('error', 'error', error.message, {
      code: error.code,
      context: error.context,
      stack: error.stack
    }, error.ruleId, error.fieldName);

    // 根据错误代码提供具体的解决建议
    const suggestion = this.getErrorSuggestion(error.code);
    if (suggestion) {
      this.debuggerInstance?.log('info', 'error', `解决建议: ${suggestion}`, {
        errorCode: error.code
      }, error.ruleId, error.fieldName);
    }
  }

  /**
   * 处理通用错误
   */
  private handleGenericError(error: Error, context?: any): void {
    this.debuggerInstance?.log('error', 'error', error.message, {
      context,
      stack: error.stack
    });
  }

  /**
   * 获取错误解决建议
   */
  private getErrorSuggestion(errorCode: string): string | null {
    const suggestions: Record<string, string> = {
      'RULE_NOT_FOUND': '检查规则ID是否正确，确保规则已正确注册',
      'FIELD_NOT_FOUND': '检查字段名是否正确，确保字段存在于表单中',
      'CONDITION_EVAL_ERROR': '检查条件表达式语法，确保操作符和值类型匹配',
      'ACTION_EXEC_ERROR': '检查动作配置，确保动作类型和参数正确',
      'CIRCULAR_DEPENDENCY': '检查规则依赖关系，移除循环依赖',
      'INVALID_OPERATOR': '检查条件操作符，使用支持的操作符类型',
      'TYPE_MISMATCH': '检查值类型，确保条件值与字段类型匹配'
    };

    return suggestions[errorCode] || null;
  }
}

// ==================== 导出工具函数 ====================

/**
 * 创建调试器
 */
export function createDebugger(config?: Partial<DebugConfig>): LinkageDebugger {
  const debuggerInstance = new LinkageDebugger();
  if (config) {
    debuggerInstance.configure(config);
  }
  return debuggerInstance;
}

/**
 * 创建错误处理器
 */
export function createErrorHandler(debuggerInstance?: LinkageDebugger): LinkageErrorHandler {
  return new LinkageErrorHandler(debuggerInstance);
}

/**
 * 包装联动引擎以添加调试支持
 */
export function withDebugging(engine: LinkageEngineV2, config?: Partial<DebugConfig>): {
  engine: LinkageEngineV2;
  debugger: LinkageDebugger;
  errorHandler: LinkageErrorHandler;
} {
  const debuggerInstance = createDebugger(config);
  const errorHandler = createErrorHandler(debuggerInstance);
  
  // 这里可以添加引擎方法的包装逻辑
  // 由于需要修改引擎内部实现，这里只返回调试器和错误处理器
  
  return { engine, debugger: debuggerInstance, errorHandler };
}