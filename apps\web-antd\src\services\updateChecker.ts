import { notification } from 'ant-design-vue';
import { h } from 'vue';

interface VersionInfo {
  version: string;
  buildTime: number;
  timestamp: string;
}

class UpdateChecker {
  private checkInterval: number = 30000; // 30秒检查一次
  private timer: NodeJS.Timeout | null = null;
  private currentBuildTime: number;
  private isChecking: boolean = false;
  
  constructor() {
    this.currentBuildTime = Number(__BUILD_TIME__);
  }
  
  /**
   * 启动版本检测
   */
  start(): void {
    if (this.timer) {
      this.stop();
    }
    
    this.timer = setInterval(() => {
      this.checkForUpdates();
    }, this.checkInterval);
    
    // 立即执行一次检查
    setTimeout(() => this.checkForUpdates(), 5000);
  }
  
  /**
   * 停止版本检测
   */
  stop(): void {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }
  
  /**
   * 检查更新
   */
  private async checkForUpdates(): Promise<void> {
    if (this.isChecking) return;
    
    this.isChecking = true;
    
    try {
      const response = await fetch(`/version.json?t=${Date.now()}`, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache'
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch version info');
      }
      
      const versionInfo: VersionInfo = await response.json();
      
      // 比较构建时间
      if (versionInfo.buildTime !== this.currentBuildTime) {
        this.showUpdateNotification(versionInfo);
        this.stop(); // 停止检查，避免重复提示
      }
    } catch (error) {
      console.warn('检查更新失败:', error);
    } finally {
      this.isChecking = false;
    }
  }
  
  /**
   * 显示更新通知
   */
  private showUpdateNotification(versionInfo: VersionInfo): void {
    const key = 'app-update-notification';
    
    notification.info({
      key,
      message: '发现新版本',
      description: `系统已更新到 v${versionInfo.version}，建议刷新页面获取最新功能`,
      duration: 0, // 不自动关闭
      placement: 'topRight',
      btn: h('div', { style: 'display: flex; gap: 8px;' }, [
        h('button', {
          class: 'ant-btn ant-btn-primary ant-btn-sm',
          onClick: () => {
            notification.close(key);
            this.refreshApp();
          }
        }, '立即刷新'),
        h('button', {
          class: 'ant-btn ant-btn-default ant-btn-sm',
          onClick: () => {
            notification.close(key);
            this.snoozeUpdate();
          }
        }, '稍后提醒')
      ])
    });
  }
  
  /**
   * 刷新应用
   */
  private refreshApp(): void {
    window.location.reload();
  }
  
  /**
   * 延迟提醒
   */
  private snoozeUpdate(): void {
    // 10分钟后再次提醒
    setTimeout(() => {
      this.start();
    }, 10 * 60 * 1000);
  }
  
  /**
   * 设置检查间隔
   */
  setCheckInterval(interval: number): void {
    this.checkInterval = interval;
    if (this.timer) {
      this.stop();
      this.start();
    }
  }
}

export const updateChecker = new UpdateChecker();