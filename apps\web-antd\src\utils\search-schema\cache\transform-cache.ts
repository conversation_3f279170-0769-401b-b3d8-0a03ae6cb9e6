/**
 * 表单转换结果缓存管理器
 * 实现LRU（最近最少使用）缓存策略，提升转换性能
 */

import type {
  BackendSearchItem,
  DirectGroupedSearchData,
  GroupedSearchData,
} from '../types';

import type { VbenFormSchema } from '#/adapter/form';

/**
 * 缓存条目接口
 */
interface CacheEntry {
  key: string;
  value: VbenFormSchema[];
  timestamp: number;
  accessCount: number;
  size: number;
}

/**
 * 转换选项接口
 */
interface TransformOptions {
  enableGrouping?: boolean;
  formMode?: 'add' | 'edit';
}

/**
 * LRU缓存管理器
 * 使用Map数据结构实现LRU算法，提供高效的缓存操作
 */
export class TransformCache {
  /**
   * 检查缓存是否为空
   */
  get isEmpty(): boolean {
    return this.cache.size === 0;
  }
  /**
   * 获取缓存大小
   */
  get size(): number {
    return this.cache.size;
  }
  private cache = new Map<string, CacheEntry>();
  private hitCount = 0;
  private maxSize: number;
  private missCount = 0;

  private totalSize = 0;

  private ttl: number; // 缓存过期时间（毫秒）

  constructor(maxSize = 100, ttl = 5 * 60 * 1000) {
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  /**
   * 清理过期缓存
   * 定期调用以释放内存
   */
  cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.ttl) {
        keysToDelete.push(key);
      }
    }

    for (const key of keysToDelete) {
      const entry = this.cache.get(key)!;
      this.totalSize -= entry.size;
      this.cache.delete(key);
    }
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.totalSize = 0;
    this.hitCount = 0;
    this.missCount = 0;
  }

  /**
   * 删除指定缓存
   */
  delete(key: string): boolean {
    const entry = this.cache.get(key);
    if (entry) {
      this.totalSize -= entry.size;
      return this.cache.delete(key);
    }
    return false;
  }

  /**
   * 获取缓存值
   * 如果缓存命中，更新访问时间和计数
   */
  get(key: string): undefined | VbenFormSchema[] {
    const entry = this.cache.get(key);

    if (!entry) {
      this.missCount++;
      return undefined;
    }

    // 检查是否过期
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(key);
      this.totalSize -= entry.size;
      this.missCount++;
      return undefined;
    }

    // 更新访问信息（LRU策略）
    entry.timestamp = Date.now();
    entry.accessCount++;

    // 重新插入到Map末尾（LRU策略）
    this.cache.delete(key);
    this.cache.set(key, entry);

    this.hitCount++;
    return entry.value;
  }

  /**
   * 生成缓存键
   * 基于数据内容和选项生成唯一的缓存键
   */
  getCacheKey(
    data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
    options: TransformOptions = {},
  ): string {
    try {
      // 创建一个简化的数据结构用于生成键，避免深度序列化的性能开销
      const keyData = {
        dataType: Array.isArray(data) ? 'array' : 'object',
        dataLength: Array.isArray(data)
          ? data.length
          : Object.keys(data).length,
        options,
        // 添加数据的简单哈希
        dataHash: this.simpleHash(JSON.stringify(data)),
      };

      return JSON.stringify(keyData);
    } catch (error) {
      console.warn('Failed to generate cache key:', error);
      // 如果序列化失败，使用时间戳作为键（不缓存）
      return `fallback_${Date.now()}_${Math.random()}`;
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const totalRequests = this.hitCount + this.missCount;
    const hitRate =
      totalRequests > 0 ? (this.hitCount / totalRequests) * 100 : 0;

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: Number(hitRate.toFixed(2)),
      totalSize: this.totalSize,
      averageSize:
        this.cache.size > 0 ? Math.round(this.totalSize / this.cache.size) : 0,
    };
  }

  /**
   * 获取所有缓存键
   */
  keys(): string[] {
    return [...this.cache.keys()];
  }

  /**
   * 设置缓存值
   * 如果缓存已满，删除最旧的条目
   */
  set(key: string, value: VbenFormSchema[]): void {
    // 计算数据大小（估算）
    const size = this.estimateSize(value);

    // 如果键已存在，先删除旧值
    if (this.cache.has(key)) {
      const oldEntry = this.cache.get(key)!;
      this.totalSize -= oldEntry.size;
      this.cache.delete(key);
    }

    // 如果缓存已满，删除最旧的条目
    while (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        const firstEntry = this.cache.get(firstKey)!;
        this.totalSize -= firstEntry.size;
        this.cache.delete(firstKey);
      }
    }

    // 添加新条目
    const entry: CacheEntry = {
      key,
      value,
      timestamp: Date.now(),
      accessCount: 1,
      size,
    };

    this.cache.set(key, entry);
    this.totalSize += size;
  }

  /**
   * 估算数据大小
   * 用于内存管理和缓存策略
   */
  private estimateSize(value: VbenFormSchema[]): number {
    try {
      // 简单估算：JSON字符串长度作为大小指标
      return JSON.stringify(value).length;
    } catch {
      // 如果序列化失败，使用数组长度 * 平均对象大小估算
      return value.length * 500; // 假设每个schema对象约500字符
    }
  }

  /**
   * 简单哈希函数
   * 用于生成数据的简单哈希值，减少缓存键的长度
   */
  private simpleHash(str: string): number {
    let hash = 0;
    if (str.length === 0) return hash;

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }

    return Math.abs(hash);
  }
}

// 创建全局缓存实例
export const transformCache = new TransformCache();

// 定期清理过期缓存（每5分钟）
if (typeof window !== 'undefined') {
  setInterval(
    () => {
      transformCache.cleanup();
    },
    5 * 60 * 1000,
  );
}
