# 联动引擎V2技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[前端表单组件] --> B[联动引擎V2核心]
    B --> C[规则解析器]
    B --> D[条件评估器]
    B --> E[动作执行器]
    B --> F[缓存管理器]
    B --> G[性能监控器]
    
    C --> H[规则预编译]
    D --> I[条件匹配算法]
    E --> J[DOM操作优化]
    F --> K[LRU缓存策略]
    G --> L[性能指标收集]
    
    subgraph "核心引擎层"
        B
        C
        D
        E
    end
    
    subgraph "优化层"
        F
        G
        H
        I
        J
        K
        L
    end
```

## 2. 技术描述

- **前端框架**: React@18 + TypeScript@5
- **表单库**: Ant Design + @ant-design/pro-form
- **状态管理**: 内置状态管理（基于React Hooks）
- **性能优化**: 内存缓存 + 对象池 + Memoization
- **调试工具**: 内置调试面板和日志系统

## 3. 核心模块设计

### 3.1 联动引擎核心 (LinkageEngineV2)

```typescript
interface LinkageEngineV2 {
  // 初始化引擎
  initialize(config: LinkageConfig): void;
  
  // 执行联动规则
  execute(fieldName: string, value: any, formValues: Record<string, any>): void;
  
  // 添加规则
  addRule(rule: LinkageRule): void;
  
  // 移除规则
  removeRule(ruleId: string): void;
  
  // 清理资源
  dispose(): void;
}
```

### 3.2 规则解析器 (RuleParser)

```typescript
interface RuleParser {
  // 解析联动配置
  parse(config: LinkageConfig): CompiledRule[];
  
  // 预编译规则
  compile(rule: LinkageRule): CompiledRule;
  
  // 验证规则
  validate(rule: LinkageRule): ValidationResult;
}
```

### 3.3 条件评估器 (ConditionEvaluator)

```typescript
interface ConditionEvaluator {
  // 评估单个条件
  evaluateCondition(condition: Condition, formValues: Record<string, any>): boolean;
  
  // 评估条件组
  evaluateConditions(conditions: Condition[], logic: 'and' | 'or', formValues: Record<string, any>): boolean;
  
  // 支持的操作符
  getSupportedOperators(): string[];
}
```

### 3.4 动作执行器 (ActionExecutor)

```typescript
interface ActionExecutor {
  // 执行动作
  executeAction(action: Action, target: string, context: ExecutionContext): void;
  
  // 批量执行动作
  executeActions(actions: Action[], context: ExecutionContext): void;
  
  // 支持的动作类型
  getSupportedActions(): string[];
}
```

## 4. 数据模型定义

### 4.1 核心接口

```typescript
// 联动配置接口
interface LinkageConfig {
  rules: LinkageRule[];
  debug?: boolean;
  performance?: PerformanceConfig;
}

// 联动规则接口
interface LinkageRule {
  id?: string;
  name?: string;
  conditions: Condition[];
  logic?: 'and' | 'or';
  actions: Action[];
  priority?: number;
  enabled?: boolean;
}

// 条件接口
interface Condition {
  field: string;
  operator: ConditionOperator;
  value: any;
  valueType?: 'static' | 'field' | 'function';
}

// 动作接口
interface Action {
  type: ActionType;
  target: string;
  [key: string]: any;
}

// 条件操作符
type ConditionOperator = 
  | 'eq'      // 等于
  | 'ne'      // 不等于
  | 'gt'      // 大于
  | 'gte'     // 大于等于
  | 'lt'      // 小于
  | 'lte'     // 小于等于
  | 'in'      // 包含
  | 'notIn'   // 不包含
  | 'contains'// 字符串包含
  | 'startsWith' // 以...开始
  | 'endsWith'   // 以...结束
  | 'isEmpty'    // 为空
  | 'isNotEmpty' // 不为空
  | 'regex';     // 正则匹配

// 动作类型
type ActionType = 
  | 'show'              // 显示字段
  | 'hide'              // 隐藏字段
  | 'enable'            // 启用字段
  | 'disable'           // 禁用字段
  | 'setRequired'       // 设置必填
  | 'setOptional'       // 设置可选
  | 'setValue'          // 设置字段值
  | 'setOptions'        // 设置选项
  | 'setComponentProps' // 设置组件属性
  | 'validate'          // 触发验证
  | 'focus'             // 聚焦字段
  | 'blur';             // 失焦字段
```

### 4.2 性能配置

```typescript
interface PerformanceConfig {
  // 缓存配置
  cache?: {
    enabled: boolean;
    maxSize: number;
    ttl: number; // 生存时间（毫秒）
  };
  
  // 对象池配置
  objectPool?: {
    enabled: boolean;
    maxSize: number;
  };
  
  // 批处理配置
  batch?: {
    enabled: boolean;
    delay: number; // 延迟时间（毫秒）
  };
  
  // 调试配置
  debug?: {
    enabled: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
    showPerformanceMetrics: boolean;
  };
}
```

## 5. 性能优化机制

### 5.1 缓存策略

```typescript
// LRU缓存实现
class LRUCache<K, V> {
  private capacity: number;
  private cache: Map<K, V>;
  
  constructor(capacity: number) {
    this.capacity = capacity;
    this.cache = new Map();
  }
  
  get(key: K): V | undefined {
    if (this.cache.has(key)) {
      // 移动到最前面
      const value = this.cache.get(key)!;
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return undefined;
  }
  
  set(key: K, value: V): void {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.capacity) {
      // 删除最久未使用的项
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }
}
```

### 5.2 对象池管理

```typescript
// 对象池实现
class ObjectPool<T> {
  private pool: T[];
  private factory: () => T;
  private reset: (obj: T) => void;
  private maxSize: number;
  
  constructor(factory: () => T, reset: (obj: T) => void, maxSize: number = 100) {
    this.pool = [];
    this.factory = factory;
    this.reset = reset;
    this.maxSize = maxSize;
  }
  
  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.factory();
  }
  
  release(obj: T): void {
    if (this.pool.length < this.maxSize) {
      this.reset(obj);
      this.pool.push(obj);
    }
  }
}
```

### 5.3 Memoization优化

```typescript
// Memoization装饰器
function memoize<T extends (...args: any[]) => any>(fn: T): T {
  const cache = new Map();
  
  return ((...args: Parameters<T>) => {
    const key = JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    const result = fn(...args);
    cache.set(key, result);
    return result;
  }) as T;
}

// 使用示例
class ConditionEvaluator {
  @memoize
  evaluateCondition(condition: Condition, formValues: Record<string, any>): boolean {
    // 条件评估逻辑
    return this.doEvaluate(condition, formValues);
  }
}
```

## 6. 调试和监控

### 6.1 调试工具

```typescript
interface DebugTool {
  // 开启调试模式
  enableDebug(): void;
  
  // 关闭调试模式
  disableDebug(): void;
  
  // 记录执行日志
  log(level: LogLevel, message: string, data?: any): void;
  
  // 获取性能指标
  getPerformanceMetrics(): PerformanceMetrics;
  
  // 导出调试信息
  exportDebugInfo(): DebugInfo;
}
```

### 6.2 性能监控

```typescript
interface PerformanceMetrics {
  // 执行次数
  executionCount: number;
  
  // 平均执行时间
  averageExecutionTime: number;
  
  // 缓存命中率
  cacheHitRate: number;
  
  // 内存使用情况
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  
  // 规则执行统计
  ruleStats: {
    [ruleId: string]: {
      executionCount: number;
      averageTime: number;
      lastExecuted: Date;
    };
  };
}
```

## 7. 使用示例

### 7.1 基础使用

```typescript
import { LinkageEngineV2 } from './linkage-engine-v2';

// 创建联动引擎实例
const linkageEngine = new LinkageEngineV2({
  rules: [
    {
      id: 'show-company-fields',
      conditions: [
        { field: 'userType', operator: 'eq', value: 'enterprise' }
      ],
      actions: [
        { type: 'show', target: 'companyName' },
        { type: 'show', target: 'companyAddress' },
        { type: 'setRequired', target: 'companyName', required: true }
      ]
    }
  ],
  debug: true,
  performance: {
    cache: { enabled: true, maxSize: 1000, ttl: 300000 },
    objectPool: { enabled: true, maxSize: 100 },
    batch: { enabled: true, delay: 10 }
  }
});

// 在表单值变化时执行联动
const handleFieldChange = (fieldName: string, value: any, allValues: Record<string, any>) => {
  linkageEngine.execute(fieldName, value, allValues);
};
```

### 7.2 高级使用

```typescript
// 动态添加规则
linkageEngine.addRule({
  id: 'dynamic-discount',
  conditions: [
    { field: 'orderAmount', operator: 'gt', value: 1000 },
    { field: 'memberLevel', operator: 'in', value: ['gold', 'platinum'] }
  ],
  logic: 'and',
  actions: [
    { type: 'show', target: 'discountCode' },
    { type: 'setComponentProps', target: 'discount', props: { max: 20 } }
  ]
});

// 获取性能指标
const metrics = linkageEngine.getPerformanceMetrics();
console.log('缓存命中率:', metrics.cacheHitRate);
console.log('平均执行时间:', metrics.averageExecutionTime);

// 导出调试信息
const debugInfo = linkageEngine.exportDebugInfo();
console.log('调试信息:', debugInfo);
```

## 8. 最佳实践

### 8.1 规则设计原则

1. **单一职责**：每个规则只处理一个特定的联动场景
2. **条件简化**：避免过于复杂的条件组合
3. **性能优先**：优先使用缓存友好的条件
4. **可读性**：使用有意义的规则ID和名称

### 8.2 性能优化建议

1. **合理使用缓存**：为频繁执行的规则启用缓存
2. **批处理优化**：对于大量字段变化，使用批处理模式
3. **内存管理**：定期清理不再使用的规则和缓存
4. **监控指标**：持续监控性能指标，及时发现问题

### 8.3 调试技巧

1. **开启调试模式**：在开发环境中启用详细日志
2. **分步测试**：将复杂规则拆分为多个简单规则进行测试
3. **性能分析**：使用内置的性能监控工具分析瓶颈
4. **错误处理**：为规则执行添加适当的错误处理机制

## 9. 总结

联动引擎V2采用了现代化的架构设计，通过以下技术手段实现了高性能和易用性：

1. **模块化设计**：清晰的职责分离，便于维护和扩展
2. **性能优化**：多层次的缓存和优化机制
3. **类型安全**：完整的TypeScript类型定义
4. **调试友好**：内置的调试和监控工具
5. **扩展性强**：支持自定义操作符和动作类型

这些特性使得联动引擎V2能够满足复杂业务场景的需求，同时保持良好的性能和开发体验。