/**
 * 联动引擎V2与现有表单系统集成示例
 * 
 * 展示如何在实际项目中使用新的联动引擎V2
 */

import type { VbenFormSchema } from '#/adapter/form';
import { 
  transformBackendSearchToSchema,
  setSchemaWithTracking,
  enableLinkageEngineV2,
  getLinkageEngine,
  cleanupLinkageEngine
} from './transform';
import { createLinkageBuilder } from './linkage-config-builder';
import type { BackendSearchItem } from './types';

// ==================== 基础集成示例 ====================

/**
 * 示例1：基础表单联动集成
 * 展示如何在表单中启用联动引擎V2
 */
export function basicFormIntegrationExample() {
  // 模拟后端数据
  const backendData: BackendSearchItem[] = [
    {
      field: 'category',
      title: '商品分类',
      type: 'select',
      config: {
        options: [
          { label: '电子产品', value: 'electronics' },
          { label: '服装', value: 'clothing' },
          { label: '食品', value: 'food' }
        ],
        // 使用联动引擎V2
        linkage: {
          engineVersion: 'v2',
          rules: [
            {
              id: 'category_subcategory_linkage',
              target: 'subcategory',
              when: {
                field: 'category',
                operator: 'eq',
                value: 'electronics'
              },
              then: {
                type: 'updateOptions',
                options: [
                  { label: '手机', value: 'phone' },
                  { label: '电脑', value: 'computer' },
                  { label: '平板', value: 'tablet' }
                ]
              },
              description: '根据商品分类更新子分类选项'
            }
          ]
        }
      }
    },
    {
      field: 'subcategory',
      title: '子分类',
      type: 'select',
      config: {
        options: []
      }
    },
    {
      field: 'price',
      title: '价格',
      type: 'number',
      config: {
        linkage: {
          engineVersion: 'v2',
          rules: [
            {
              id: 'price_discount_linkage',
              target: 'discount',
              when: {
                field: 'price',
                operator: 'gt',
                value: 1000
              },
              then: {
                type: 'show'
              },
              description: '价格大于1000时显示折扣字段'
            }
          ]
        }
      }
    },
    {
      field: 'discount',
      title: '折扣',
      type: 'number',
      config: {
        visible: false
      }
    }
  ];

  // 转换为前端schema
  const schema = transformBackendSearchToSchema(backendData);

  // 表单初始化函数
  const initializeForm = (formApi: any) => {
    const formId = 'product_form_' + Date.now();
    
    // 使用增强的setSchemaWithTracking，自动启用联动引擎V2
    setSchemaWithTracking(formApi, schema, {
      formId,
      enableLinkageV2: true,
      enableDebug: true, // 开发环境启用调试
      enableMonitoring: false // 生产环境可启用性能监控
    });

    console.log('表单初始化完成，联动引擎V2已启用');
    
    // 返回清理函数
    return () => {
      cleanupLinkageEngine(formId);
    };
  };

  return { schema, initializeForm };
}

// ==================== 高级集成示例 ====================

/**
 * 示例2：复杂联动场景集成
 * 展示多字段联动、条件组合等高级功能
 */
export function advancedFormIntegrationExample() {
  // 使用联动配置构建器创建复杂规则
  const builder = createLinkageBuilder();
  
  // 构建复杂联动规则
  const complexRules = [
    // 规则1：地区联动
    builder
      .createRule('region_city_linkage', '根据地区更新城市选项')
      .when(field => field('region').eq('guangdong'))
      .then(action => action.updateOptions('city', [
        { label: '广州', value: 'guangzhou' },
        { label: '深圳', value: 'shenzhen' },
        { label: '珠海', value: 'zhuhai' }
      ]))
      .build(),
    
    // 规则2：多条件联动
    builder
      .createRule('vip_discount_linkage', 'VIP用户且订单金额大于500时显示特殊折扣')
      .when(field => 
        field('userType').eq('vip')
          .and(field('orderAmount').gt(500))
      )
      .then(action => 
        action.show('specialDiscount')
          .setValue('specialDiscount', 0.15)
      )
      .build(),
    
    // 规则3：计算规则
    builder
      .createRule('total_calculation', '计算订单总金额')
      .when(field => 
        field('orderAmount').isNotEmpty()
          .or(field('discount').isNotEmpty())
      )
      .then(action => 
        action.calculate('totalAmount', (values) => {
          const amount = Number(values.orderAmount) || 0;
          const discount = Number(values.discount) || 0;
          return amount * (1 - discount);
        })
      )
      .build()
  ];

  // 后端数据配置
  const backendData: BackendSearchItem[] = [
    {
      field: 'userType',
      title: '用户类型',
      type: 'select',
      config: {
        options: [
          { label: '普通用户', value: 'normal' },
          { label: 'VIP用户', value: 'vip' },
          { label: '企业用户', value: 'enterprise' }
        ]
      }
    },
    {
      field: 'region',
      title: '地区',
      type: 'select',
      config: {
        options: [
          { label: '广东省', value: 'guangdong' },
          { label: '北京市', value: 'beijing' },
          { label: '上海市', value: 'shanghai' }
        ]
      }
    },
    {
      field: 'city',
      title: '城市',
      type: 'select',
      config: {
        options: []
      }
    },
    {
      field: 'orderAmount',
      title: '订单金额',
      type: 'number',
      config: {
        placeholder: '请输入订单金额'
      }
    },
    {
      field: 'discount',
      title: '折扣率',
      type: 'number',
      config: {
        placeholder: '请输入折扣率（0-1）'
      }
    },
    {
      field: 'specialDiscount',
      title: '特殊折扣',
      type: 'number',
      config: {
        visible: false
      }
    },
    {
      field: 'totalAmount',
      title: '总金额',
      type: 'number',
      config: {
        disabled: true
      }
    }
  ];

  // 将规则注入到配置中
  backendData[0].config.linkage = {
    engineVersion: 'v2',
    rules: complexRules
  };

  const schema = transformBackendSearchToSchema(backendData);

  const initializeForm = (formApi: any) => {
    const formId = 'advanced_form_' + Date.now();
    
    setSchemaWithTracking(formApi, schema, {
      formId,
      enableLinkageV2: true,
      enableDebug: true,
      enableMonitoring: true // 启用性能监控
    });

    // 获取联动引擎实例进行高级操作
    const engine = getLinkageEngine(formId);
    if (engine) {
      // 监听字段状态变化
      engine.onFieldStateChange((fieldName, state) => {
        console.log(`字段 ${fieldName} 状态变化:`, state);
      });
    }

    return () => cleanupLinkageEngine(formId);
  };

  return { schema, initializeForm, complexRules };
}

// ==================== 性能优化集成示例 ====================

/**
 * 示例3：性能优化集成
 * 展示如何在大型表单中优化联动性能
 */
export function performanceOptimizedIntegrationExample() {
  // 创建大量字段的表单
  const backendData: BackendSearchItem[] = [];
  
  // 生成100个字段
  for (let i = 1; i <= 100; i++) {
    backendData.push({
      field: `field_${i}`,
      title: `字段 ${i}`,
      type: 'input',
      config: {
        placeholder: `请输入字段${i}`
      }
    });
  }

  // 添加一些关键联动字段
  backendData.push({
    field: 'trigger_field',
    title: '触发字段',
    type: 'select',
    config: {
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
        { label: '选项3', value: '3' }
      ],
      linkage: {
        engineVersion: 'v2',
        rules: [
          {
            id: 'batch_show_hide',
            target: '*', // 影响所有字段
            when: {
              field: 'trigger_field',
              operator: 'eq',
              value: '1'
            },
            then: {
              type: 'batchUpdate',
              updates: [
                { target: 'field_1', action: { type: 'show' } },
                { target: 'field_2', action: { type: 'show' } },
                { target: 'field_3', action: { type: 'hide' } }
              ]
            },
            description: '批量显示/隐藏字段',
            // 性能优化配置
            debounce: 300, // 防抖300ms
            priority: 'high' // 高优先级
          }
        ]
      }
    }
  });

  const schema = transformBackendSearchToSchema(backendData);

  const initializeForm = (formApi: any) => {
    const formId = 'performance_form_' + Date.now();
    
    setSchemaWithTracking(formApi, schema, {
      formId,
      enableLinkageV2: true,
      enableDebug: false, // 大型表单关闭调试以提升性能
      enableMonitoring: true // 启用性能监控
    });

    // 获取性能监控器
    const engine = getLinkageEngine(formId);
    if (engine) {
      // 定期输出性能报告
      const performanceTimer = setInterval(() => {
        const debugInfo = engine.getDebugInfo();
        console.log('联动引擎性能报告:', debugInfo);
      }, 10000); // 每10秒输出一次

      // 返回清理函数
      return () => {
        clearInterval(performanceTimer);
        cleanupLinkageEngine(formId);
      };
    }

    return () => cleanupLinkageEngine(formId);
  };

  return { schema, initializeForm };
}

// ==================== 实际项目集成指南 ====================

/**
 * 实际项目集成指南
 * 提供在真实项目中集成联动引擎V2的最佳实践
 */
export const integrationGuide = {
  /**
   * 步骤1：在组件中集成
   */
  componentIntegration: `
    // 在Vue组件中使用
    <template>
      <VbenForm ref="formRef" />
    </template>
    
    <script setup>
    import { ref, onMounted, onUnmounted } from 'vue';
    import { transformBackendSearchToSchema, setSchemaWithTracking } from '@/utils/search-schema/transform';
    
    const formRef = ref();
    let cleanup: (() => void) | null = null;
    
    onMounted(async () => {
      // 获取后端数据
      const backendData = await fetchFormConfig();
      
      // 转换为schema
      const schema = transformBackendSearchToSchema(backendData);
      
      // 初始化表单和联动引擎
      const formApi = formRef.value?.formApi;
      if (formApi) {
        cleanup = setSchemaWithTracking(formApi, schema, {
          formId: 'my_form_' + Date.now(),
          enableLinkageV2: true,
          enableDebug: process.env.NODE_ENV === 'development',
          enableMonitoring: true
        });
      }
    });
    
    onUnmounted(() => {
      // 清理资源
      cleanup?.();
    });
    </script>
  `,

  /**
   * 步骤2：后端配置格式
   */
  backendConfigFormat: {
    // 新格式：使用联动引擎V2
    newFormat: {
      field: 'category',
      title: '分类',
      type: 'select',
      config: {
        options: [],
        linkage: {
          engineVersion: 'v2', // 标识使用V2引擎
          rules: [
            {
              id: 'category_subcategory',
              target: 'subcategory',
              when: { field: 'category', operator: 'eq', value: 'electronics' },
              then: { type: 'updateOptions', options: [] },
              description: '分类联动'
            }
          ]
        }
      }
    },
    
    // 旧格式：自动兼容
    oldFormat: {
      field: 'category',
      title: '分类',
      type: 'select',
      config: {
        options: [],
        linkage: {
          triggerFields: ['category'],
          conditions: [],
          actions: []
        }
      }
    }
  },

  /**
   * 步骤3：性能优化建议
   */
  performanceOptimization: {
    // 大型表单优化
    largeForm: {
      enableDebug: false, // 关闭调试
      enableMonitoring: true, // 启用监控
      debounce: 300, // 增加防抖时间
      batchUpdate: true // 启用批量更新
    },
    
    // 复杂联动优化
    complexLinkage: {
      useRulePriority: true, // 使用规则优先级
      enableCache: true, // 启用缓存
      maxCacheSize: 1000 // 限制缓存大小
    }
  },

  /**
   * 步骤4：调试和监控
   */
  debuggingAndMonitoring: {
    // 开发环境调试
    development: {
      enableDebug: true,
      debugLevel: 'debug',
      logToConsole: true
    },
    
    // 生产环境监控
    production: {
      enableDebug: false,
      enableMonitoring: true,
      performanceThreshold: 100 // 性能阈值（毫秒）
    }
  }
};

// ==================== 导出所有示例 ====================

export default {
  basicFormIntegrationExample,
  advancedFormIntegrationExample,
  performanceOptimizedIntegrationExample,
  integrationGuide
};