# PHP后端联动引擎API接口指南

> 实际项目中的联动引擎集成和API设计

## 目录

1. [API架构设计](#api架构设计)
2. [核心接口实现](#核心接口实现)
3. [数据库设计](#数据库设计)
4. [缓存策略](#缓存策略)
5. [错误处理](#错误处理)
6. [性能优化](#性能优化)
7. [部署指南](#部署指南)

## API架构设计

### 整体架构

```php
<?php
/**
 * 联动引擎API架构
 * 
 * Controller -> Service -> Repository -> Database
 *            -> Cache
 *            -> Validator
 */

// 目录结构
/*
app/
├── Controllers/
│   └── LinkageController.php
├── Services/
│   ├── LinkageService.php
│   └── LinkageEngineService.php
├── Repositories/
│   └── LinkageRepository.php
├── Models/
│   ├── LinkageRule.php
│   └── LinkageConfig.php
├── Validators/
│   └── LinkageValidator.php
├── Cache/
│   └── LinkageCache.php
└── Exceptions/
    └── LinkageException.php
*/
?>
```

### 配置文件

```php
<?php
// config/linkage.php

return [
    // 引擎配置
    'engine' => [
        'version' => '2.0',
        'debug' => env('LINKAGE_DEBUG', false),
        'cache_enabled' => env('LINKAGE_CACHE', true),
        'batch_enabled' => env('LINKAGE_BATCH', true),
        'debounce_time' => env('LINKAGE_DEBOUNCE', 300),
    ],
    
    // 缓存配置
    'cache' => [
        'driver' => env('LINKAGE_CACHE_DRIVER', 'redis'),
        'ttl' => env('LINKAGE_CACHE_TTL', 3600),
        'prefix' => 'linkage:',
    ],
    
    // 性能配置
    'performance' => [
        'max_rules_per_request' => 100,
        'max_execution_time' => 5, // 秒
        'memory_limit' => '128M',
    ],
    
    // 日志配置
    'logging' => [
        'enabled' => env('LINKAGE_LOG', true),
        'level' => env('LINKAGE_LOG_LEVEL', 'info'),
        'channel' => 'linkage',
    ],
];
?>
```

## 核心接口实现

### 1. 联动控制器

```php
<?php
// app/Controllers/LinkageController.php

namespace App\Controllers;

use App\Services\LinkageService;
use App\Validators\LinkageValidator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LinkageController extends Controller
{
    private LinkageService $linkageService;
    private LinkageValidator $validator;
    
    public function __construct(
        LinkageService $linkageService,
        LinkageValidator $validator
    ) {
        $this->linkageService = $linkageService;
        $this->validator = $validator;
    }
    
    /**
     * 获取表单联动配置
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getFormConfig(Request $request): JsonResponse
    {
        try {
            $formId = $request->get('form_id');
            $version = $request->get('version', 'latest');
            
            // 验证请求参数
            $this->validator->validateGetConfigRequest($request);
            
            // 获取配置
            $config = $this->linkageService->getFormConfig($formId, $version);
            
            return response()->json([
                'success' => true,
                'data' => $config,
                'meta' => [
                    'form_id' => $formId,
                    'version' => $version,
                    'generated_at' => now()->toISOString(),
                    'cache_hit' => $config['_cache_hit'] ?? false
                ]
            ]);
            
        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }
    
    /**
     * 保存联动规则
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function saveRules(Request $request): JsonResponse
    {
        try {
            // 验证请求数据
            $validatedData = $this->validator->validateSaveRulesRequest($request);
            
            // 保存规则
            $result = $this->linkageService->saveRules(
                $validatedData['form_id'],
                $validatedData['rules'],
                $validatedData['options'] ?? []
            );
            
            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => '联动规则保存成功'
            ]);
            
        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }
    
    /**
     * 执行联动计算
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function executeRules(Request $request): JsonResponse
    {
        try {
            $validatedData = $this->validator->validateExecuteRequest($request);
            
            // 执行联动
            $result = $this->linkageService->executeRules(
                $validatedData['form_id'],
                $validatedData['form_values'],
                $validatedData['changed_fields'] ?? [],
                $validatedData['options'] ?? []
            );
            
            return response()->json([
                'success' => true,
                'data' => $result,
                'meta' => [
                    'execution_time' => $result['_execution_time'] ?? 0,
                    'rules_executed' => $result['_rules_executed'] ?? 0,
                    'cache_hits' => $result['_cache_hits'] ?? 0
                ]
            ]);
            
        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }
    
    /**
     * 验证联动规则
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function validateRules(Request $request): JsonResponse
    {
        try {
            $rules = $request->get('rules', []);
            
            // 验证规则
        Route::post('/validate', [LinkageController::class, 'validateRules']);
        
        // 获取性能指标
        Route::get('/metrics', [LinkageController::class, 'getMetrics']);
        
        // 清除缓存
        Route::delete('/cache', [LinkageController::class, 'clearCache']);
    });
?>
```

### 部署脚本

```bash
#!/bin/bash
# deploy-linkage.sh

echo "开始部署联动引擎..."

# 1. 更新代码
git pull origin main

# 2. 安装依赖
composer install --no-dev --optimize-autoloader

# 3. 运行数据库迁移
php artisan migrate --force

# 4. 清除缓存
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# 5. 优化性能
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 6. 重启队列
php artisan queue:restart

# 7. 预热缓存
php artisan linkage:warmup-cache

echo "联动引擎部署完成！"
```

### Artisan命令

```php
<?php
// app/Console/Commands/LinkageWarmupCommand.php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\LinkageService;

class LinkageWarmupCommand extends Command
{
    protected $signature = 'linkage:warmup-cache {--forms=* : 指定表单ID}';
    protected $description = '预热联动引擎缓存';
    
    private LinkageService $linkageService;
    
    public function __construct(LinkageService $linkageService)
    {
        parent::__construct();
        $this->linkageService = $linkageService;
    }
    
    public function handle()
    {
        $this->info('开始预热联动引擎缓存...');
        
        $forms = $this->option('forms');
        if (empty($forms)) {
            // 获取所有表单
            $forms = \DB::table('linkage_form_configs')
                ->where('status', 'published')
                ->pluck('form_id')
                ->toArray();
        }
        
        $bar = $this->output->createProgressBar(count($forms));
        $bar->start();
        
        foreach ($forms as $formId) {
            try {
                $this->linkageService->getFormConfig($formId);
                $bar->advance();
            } catch (\Exception $e) {
                $this->error("预热表单 {$formId} 失败: {$e->getMessage()}");
            }
        }
        
        $bar->finish();
        $this->info("\n缓存预热完成！");
    }
}
?>
```

## 使用示例

### 前端集成示例

```javascript
// 前端JavaScript集成示例

class LinkageClient {
    constructor(baseUrl, options = {}) {
        this.baseUrl = baseUrl;
        this.options = {
            timeout: 5000,
            retries: 3,
            ...options
        };
        this.cache = new Map();
    }
    
    /**
     * 获取表单配置
     */
    async getFormConfig(formId, version = 'latest') {
        const cacheKey = `config:${formId}:${version}`;
        
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }
        
        try {
            const response = await this.request('GET', '/config', {
                form_id: formId,
                version: version
            });
            
            this.cache.set(cacheKey, response.data);
            return response.data;
            
        } catch (error) {
            console.error('获取表单配置失败:', error);
            throw error;
        }
    }
    
    /**
     * 执行联动规则
     */
    async executeRules(formId, formValues, changedFields = []) {
        try {
            const response = await this.request('POST', '/execute', {
                form_id: formId,
                form_values: formValues,
                changed_fields: changedFields
            });
            
            return response.data;
            
        } catch (error) {
            console.error('执行联动规则失败:', error);
            throw error;
        }
    }
    
    /**
     * 发送HTTP请求
     */
    async request(method, endpoint, data = null) {
        const url = `${this.baseUrl}/api/linkage${endpoint}`;
        const config = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            timeout: this.options.timeout
        };
        
        if (method === 'GET' && data) {
            const params = new URLSearchParams(data);
            url += `?${params}`;
        } else if (data) {
            config.body = JSON.stringify(data);
        }
        
        let lastError;
        for (let i = 0; i < this.options.retries; i++) {
            try {
                const response = await fetch(url, config);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
                
            } catch (error) {
                lastError = error;
                if (i < this.options.retries - 1) {
                    await this.delay(Math.pow(2, i) * 1000); // 指数退避
                }
            }
        }
        
        throw lastError;
    }
    
    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
    }
}

// 使用示例
const linkageClient = new LinkageClient('https://api.example.com');

// 初始化表单
async function initForm(formId) {
    try {
        const config = await linkageClient.getFormConfig(formId);
        console.log('表单配置:', config);
        
        // 应用初始联动
        const result = await linkageClient.executeRules(formId, {});
        applyLinkageResult(result);
        
    } catch (error) {
        console.error('表单初始化失败:', error);
    }
}

// 处理字段变更
async function handleFieldChange(formId, fieldName, newValue, allValues) {
    try {
        const result = await linkageClient.executeRules(
            formId, 
            allValues, 
            [fieldName]
        );
        
        applyLinkageResult(result);
        
    } catch (error) {
        console.error('联动执行失败:', error);
    }
}

// 应用联动结果
function applyLinkageResult(result) {
    // 应用字段状态
    Object.entries(result.fieldStates || {}).forEach(([fieldName, state]) => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!field) return;
        
        // 显示/隐藏
        if ('visible' in state) {
            field.style.display = state.visible ? 'block' : 'none';
        }
        
        // 必填/可选
        if ('required' in state) {
            field.required = state.required;
        }
        
        // 启用/禁用
        if ('disabled' in state) {
            field.disabled = state.disabled;
        }
        
        // 选项更新
        if ('options' in state && field.tagName === 'SELECT') {
            updateSelectOptions(field, state.options);
        }
    });
    
    // 应用计算值
    Object.entries(result.calculatedValues || {}).forEach(([fieldName, value]) => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.value = value;
        }
    });
}

function updateSelectOptions(select, options) {
    select.innerHTML = '';
    options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option.value;
        optionElement.textContent = option.label;
        select.appendChild(optionElement);
    });
}
```

### 完整使用流程

```php
<?php
// 完整的使用流程示例

// 1. 创建表单配置
$formId = 'user_registration';
$rules = [
    [
        'id' => 'show_company_fields',
        'type' => 'visibility',
        'target' => 'company_name',
        'conditions' => [
            [
                'field' => 'user_type',
                'operator' => 'equals',
                'value' => 'business'
            ]
        ],
        'actions' => [
            ['type' => 'show', 'value' => true]
        ]
    ],
    [
        'id' => 'calculate_age',
        'type' => 'calculation',
        'target' => 'age',
        'conditions' => [
            [
                'field' => 'birth_date',
                'operator' => 'is_not_empty'
            ]
        ],
        'actions' => [
            ['type' => 'calculate']
        ],
        'calculator' => 'calculate_age'
    ]
];

// 2. 保存规则
$linkageService = app(LinkageService::class);
$result = $linkageService->saveRules($formId, $rules);
echo "规则保存成功: " . json_encode($result) . "\n";

// 3. 获取配置
$config = $linkageService->getFormConfig($formId);
echo "表单配置: " . json_encode($config) . "\n";

// 4. 执行联动
$formValues = [
    'user_type' => 'business',
    'birth_date' => '1990-01-01'
];

$linkageResult = $linkageService->executeRules($formId, $formValues);
echo "联动结果: " . json_encode($linkageResult) . "\n";

// 5. 验证规则
$validation = $linkageService->validateRules($rules);
if (!$validation['valid']) {
    echo "规则验证失败: " . implode(', ', $validation['errors']) . "\n";
} else {
    echo "规则验证通过\n";
}

// 6. 获取性能指标
$metrics = $linkageService->getPerformanceMetrics($formId, '1h');
echo "性能指标: " . json_encode($metrics) . "\n";
?>
```

## 总结

这个PHP后端联动引擎API指南提供了：

1. **完整的架构设计** - 从控制器到服务层的完整实现
2. **数据库设计** - 包含所有必要的表结构和索引
3. **缓存策略** - Redis缓存实现和性能优化
4. **错误处理** - 自定义异常和统一错误响应
5. **性能监控** - 中间件和指标收集
6. **部署指南** - 环境配置和部署脚本
7. **使用示例** - 前端集成和完整流程

### 主要优势

- **高性能**: 通过缓存和批量处理提升性能
- **易维护**: 清晰的架构和完善的错误处理
- **可扩展**: 支持自定义操作符和计算器
- **生产就绪**: 包含监控、日志和部署脚本

### 后续优化建议

1. 添加API限流和安全验证
2. 实现规则版本管理和回滚
3. 添加更多内置计算器和操作符
4. 实现分布式缓存和负载均衡
5. 添加可视化配置界面

通过这个指南，PHP后端开发者可以快速理解和集成新的联动引擎，无需修改现有的写法，只需要按照文档配置即可。    $validation = $this->linkageService->validateRules($rules);
            
            return response()->json([
                'success' => true,
                'data' => $validation,
                'message' => $validation['valid'] ? '规则验证通过' : '规则验证失败'
            ]);
            
        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }
    
    /**
     * 获取性能指标
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getMetrics(Request $request): JsonResponse
    {
        try {
            $formId = $request->get('form_id');
            $timeRange = $request->get('time_range', '1h');
            
            $metrics = $this->linkageService->getPerformanceMetrics($formId, $timeRange);
            
            return response()->json([
                'success' => true,
                'data' => $metrics
            ]);
            
        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }
    
    /**
     * 错误处理
     */
    private function handleError(\Exception $e): JsonResponse
    {
        \Log::error('联动引擎错误', [
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'request' => request()->all()
        ]);
        
        $statusCode = method_exists($e, 'getStatusCode') ? $e->getStatusCode() : 500;
        
        return response()->json([
            'success' => false,
            'error' => [
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
                'type' => get_class($e)
            ]
        ], $statusCode);
    }
}
?>
```

### 2. 联动服务类

```php
<?php
// app/Services/LinkageService.php

namespace App\Services;

use App\Repositories\LinkageRepository;
use App\Cache\LinkageCache;
use App\Services\LinkageEngineService;
use App\Exceptions\LinkageException;

class LinkageService
{
    private LinkageRepository $repository;
    private LinkageCache $cache;
    private LinkageEngineService $engine;
    
    public function __construct(
        LinkageRepository $repository,
        LinkageCache $cache,
        LinkageEngineService $engine
    ) {
        $this->repository = $repository;
        $this->cache = $cache;
        $this->engine = $engine;
    }
    
    /**
     * 获取表单联动配置
     */
    public function getFormConfig(string $formId, string $version = 'latest'): array
    {
        $cacheKey = "form_config:{$formId}:{$version}";
        
        // 尝试从缓存获取
        if ($cached = $this->cache->get($cacheKey)) {
            $cached['_cache_hit'] = true;
            return $cached;
        }
        
        // 从数据库获取
        $rules = $this->repository->getFormRules($formId, $version);
        
        if (empty($rules)) {
            throw new LinkageException("表单 {$formId} 的联动配置不存在");
        }
        
        // 构建配置
        $config = $this->buildFormConfig($rules);
        
        // 缓存配置
        $this->cache->put($cacheKey, $config, config('linkage.cache.ttl'));
        
        $config['_cache_hit'] = false;
        return $config;
    }
    
    /**
     * 保存联动规则
     */
    public function saveRules(string $formId, array $rules, array $options = []): array
    {
        // 验证规则
        $validation = $this->validateRules($rules);
        if (!$validation['valid']) {
            throw new LinkageException('规则验证失败: ' . implode(', ', $validation['errors']));
        }
        
        // 开始事务
        \DB::beginTransaction();
        
        try {
            // 保存规则
            $savedRules = $this->repository->saveFormRules($formId, $rules, $options);
            
            // 清除相关缓存
            $this->cache->forget("form_config:{$formId}:*");
            
            // 记录操作日志
            $this->logRuleChange($formId, 'save', $rules, $options);
            
            \DB::commit();
            
            return [
                'form_id' => $formId,
                'rules_count' => count($savedRules),
                'version' => $savedRules[0]['version'] ?? 'latest',
                'saved_at' => now()->toISOString()
            ];
            
        } catch (\Exception $e) {
            \DB::rollback();
            throw $e;
        }
    }
    
    /**
     * 执行联动规则
     */
    public function executeRules(
        string $formId, 
        array $formValues, 
        array $changedFields = [],
        array $options = []
    ): array {
        $startTime = microtime(true);
        
        // 获取表单配置
        $config = $this->getFormConfig($formId);
        
        // 执行联动
        $result = $this->engine->execute($config, $formValues, $changedFields, $options);
        
        // 记录性能指标
        $executionTime = microtime(true) - $startTime;
        $this->recordMetrics($formId, $executionTime, $result);
        
        $result['_execution_time'] = $executionTime;
        $result['_rules_executed'] = count($config['rules'] ?? []);
        
        return $result;
    }
    
    /**
     * 验证联动规则
     */
    public function validateRules(array $rules): array
    {
        $errors = [];
        $warnings = [];
        
        foreach ($rules as $index => $rule) {
            // 检查必需字段
            $requiredFields = ['id', 'type', 'target'];
            foreach ($requiredFields as $field) {
                if (!isset($rule[$field]) || empty($rule[$field])) {
                    $errors[] = "规则 #{$index}: 缺少必需字段 '{$field}'";
                }
            }
            
            // 检查规则类型
            $validTypes = ['visibility', 'required', 'options', 'calculation', 'props'];
            if (isset($rule['type']) && !in_array($rule['type'], $validTypes)) {
                $errors[] = "规则 #{$index}: 无效的规则类型 '{$rule['type']}'";
            }
            
            // 检查条件格式
            if (isset($rule['conditions'])) {
                foreach ($rule['conditions'] as $condIndex => $condition) {
                    if (!isset($condition['field']) || !isset($condition['operator'])) {
                        $errors[] = "规则 #{$index}, 条件 #{$condIndex}: 条件格式不正确";
                    }
                    
                    // 检查操作符
                    $validOperators = [
                        'equals', 'not_equals', 'greater_than', 'less_than',
                        'greater_than_or_equal', 'less_than_or_equal',
                        'contains', 'not_contains', 'in', 'not_in',
                        'is_empty', 'is_not_empty', 'matches', 'not_matches'
                    ];
                    
                    if (isset($condition['operator']) && !in_array($condition['operator'], $validOperators)) {
                        $warnings[] = "规则 #{$index}, 条件 #{$condIndex}: 操作符 '{$condition['operator']}' 可能不被支持";
                    }
                }
            }
        }
        
        // 检查规则ID唯一性
        $ruleIds = array_column($rules, 'id');
        $duplicateIds = array_diff_assoc($ruleIds, array_unique($ruleIds));
        if (!empty($duplicateIds)) {
            $errors[] = '规则ID必须唯一，重复的ID: ' . implode(', ', array_unique($duplicateIds));
        }
        
        // 检查循环依赖
        $circularDeps = $this->checkCircularDependencies($rules);
        if (!empty($circularDeps)) {
            $errors[] = '检测到循环依赖: ' . implode(', ', $circularDeps);
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
            'rules_count' => count($rules)
        ];
    }
    
    /**
     * 获取性能指标
     */
    public function getPerformanceMetrics(string $formId, string $timeRange): array
    {
        return $this->repository->getPerformanceMetrics($formId, $timeRange);
    }
    
    /**
     * 构建表单配置
     */
    private function buildFormConfig(array $rules): array
    {
        return [
            'version' => '2.0',
            'rules' => $rules,
            'options' => [
                'enableCache' => config('linkage.engine.cache_enabled'),
                'enableBatch' => config('linkage.engine.batch_enabled'),
                'debounceTime' => config('linkage.engine.debounce_time'),
                'debug' => config('linkage.engine.debug')
            ],
            'meta' => [
                'rules_count' => count($rules),
                'generated_at' => now()->toISOString()
            ]
        ];
    }
    
    /**
     * 检查循环依赖
     */
    private function checkCircularDependencies(array $rules): array
    {
        // 构建依赖图
        $dependencies = [];
        foreach ($rules as $rule) {
            $target = $rule['target'];
            $triggers = [];
            
            if (isset($rule['conditions'])) {
                foreach ($rule['conditions'] as $condition) {
                    if (isset($condition['field'])) {
                        $triggers[] = $condition['field'];
                    }
                }
            }
            
            if (isset($rule['triggerFields'])) {
                $triggers = array_merge($triggers, $rule['triggerFields']);
            }
            
            $dependencies[$target] = array_unique($triggers);
        }
        
        // 检查循环依赖（简化版本）
        $circular = [];
        foreach ($dependencies as $target => $triggers) {
            foreach ($triggers as $trigger) {
                if (isset($dependencies[$trigger]) && in_array($target, $dependencies[$trigger])) {
                    $circular[] = "{$target} <-> {$trigger}";
                }
            }
        }
        
        return array_unique($circular);
    }
    
    /**
     * 记录规则变更日志
     */
    private function logRuleChange(string $formId, string $action, array $rules, array $options): void
    {
        \Log::info('联动规则变更', [
            'form_id' => $formId,
            'action' => $action,
            'rules_count' => count($rules),
            'options' => $options,
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString()
        ]);
    }
    
    /**
     * 记录性能指标
     */
    private function recordMetrics(string $formId, float $executionTime, array $result): void
    {
        $this->repository->recordMetrics([
            'form_id' => $formId,
            'execution_time' => $executionTime,
            'rules_executed' => $result['_rules_executed'] ?? 0,
            'cache_hits' => $result['_cache_hits'] ?? 0,
            'timestamp' => now()
        ]);
    }
}
?>
```

### 3. 联动引擎服务

```php
<?php
// app/Services/LinkageEngineService.php

namespace App\Services;

use App\Cache\LinkageCache;

class LinkageEngineService
{
    private LinkageCache $cache;
    private array $operators;
    private array $calculators;
    
    public function __construct(LinkageCache $cache)
    {
        $this->cache = $cache;
        $this->initializeOperators();
        $this->initializeCalculators();
    }
    
    /**
     * 执行联动规则
     */
    public function execute(
        array $config, 
        array $formValues, 
        array $changedFields = [],
        array $options = []
    ): array {
        $result = [
            'fieldStates' => [],
            'calculatedValues' => [],
            'errors' => [],
            '_cache_hits' => 0
        ];
        
        $rules = $config['rules'] ?? [];
        $enableCache = $options['enableCache'] ?? $config['options']['enableCache'] ?? true;
        
        foreach ($rules as $rule) {
            try {
                // 检查是否需要执行此规则
                if (!$this->shouldExecuteRule($rule, $changedFields)) {
                    continue;
                }
                
                // 尝试从缓存获取结果
                $cacheKey = $this->getRuleCacheKey($rule, $formValues);
                if ($enableCache && ($cached = $this->cache->get($cacheKey))) {
                    $this->mergeResult($result, $cached);
                    $result['_cache_hits']++;
                    continue;
                }
                
                // 执行规则
                $ruleResult = $this->executeRule($rule, $formValues);
                
                // 缓存结果
                if ($enableCache) {
                    $this->cache->put($cacheKey, $ruleResult, 300); // 5分钟缓存
                }
                
                // 合并结果
                $this->mergeResult($result, $ruleResult);
                
            } catch (\Exception $e) {
                $result['errors'][] = [
                    'rule_id' => $rule['id'] ?? 'unknown',
                    'message' => $e->getMessage()
                ];
            }
        }
        
        return $result;
    }
    
    /**
     * 执行单个规则
     */
    private function executeRule(array $rule, array $formValues): array
    {
        $result = [
            'fieldStates' => [],
            'calculatedValues' => []
        ];
        
        // 评估条件
        if (!$this->evaluateConditions($rule, $formValues)) {
            return $result;
        }
        
        // 执行动作
        $target = $rule['target'];
        $actions = $rule['actions'] ?? [];
        
        foreach ($actions as $action) {
            switch ($action['type']) {
                case 'show':
                case 'hide':
                    $result['fieldStates'][$target]['visible'] = $action['type'] === 'show' ? $action['value'] : !$action['value'];
                    break;
                    
                case 'required':
                case 'optional':
                    $result['fieldStates'][$target]['required'] = $action['type'] === 'required' ? $action['value'] : !$action['value'];
                    break;
                    
                case 'disabled':
                case 'enabled':
                    $result['fieldStates'][$target]['disabled'] = $action['type'] === 'disabled' ? $action['value'] : !$action['value'];
                    break;
                    
                case 'options':
                    $result['fieldStates'][$target]['options'] = $this->getOptionsForRule($rule, $formValues);
                    break;
                    
                case 'props':
                    $result['fieldStates'][$target]['props'] = $this->getPropsForRule($rule, $formValues);
                    break;
                    
                case 'calculate':
                    $result['calculatedValues'][$target] = $this->calculateValue($rule, $formValues);
                    break;
            }
        }
        
        return $result;
    }
    
    /**
     * 评估条件
     */
    private function evaluateConditions(array $rule, array $formValues): bool
    {
        $conditions = $rule['conditions'] ?? [];
        if (empty($conditions)) {
            return true;
        }
        
        $logic = $rule['logic'] ?? 'and';
        $results = [];
        
        foreach ($conditions as $condition) {
            $results[] = $this->evaluateCondition($condition, $formValues);
        }
        
        return $logic === 'and' ? !in_array(false, $results) : in_array(true, $results);
    }
    
    /**
     * 评估单个条件
     */
    private function evaluateCondition(array $condition, array $formValues): bool
    {
        $field = $condition['field'];
        $operator = $condition['operator'];
        $expectedValue = $condition['value'] ?? null;
        
        $actualValue = $this->getNestedValue($formValues, $field);
        
        if (!isset($this->operators[$operator])) {
            throw new \Exception("不支持的操作符: {$operator}");
        }
        
        return $this->operators[$operator]($actualValue, $expectedValue, $formValues);
    }
    
    /**
     * 获取选项
     */
    private function getOptionsForRule(array $rule, array $formValues): array
    {
        if (isset($rule['optionsMap'])) {
            $triggerField = $rule['trigger'] ?? '';
            $triggerValue = $this->getNestedValue($formValues, $triggerField);
            return $rule['optionsMap'][$triggerValue] ?? [];
        }
        
        if (isset($rule['optionsCallback'])) {
            return call_user_func($rule['optionsCallback'], $formValues);
        }
        
        return [];
    }
    
    /**
     * 获取属性
     */
    private function getPropsForRule(array $rule, array $formValues): array
    {
        if (isset($rule['propsMap'])) {
            $triggerField = $rule['trigger'] ?? '';
            $triggerValue = $this->getNestedValue($formValues, $triggerField);
            return $rule['propsMap'][$triggerValue] ?? [];
        }
        
        if (isset($rule['propsCallback'])) {
            return call_user_func($rule['propsCallback'], $formValues);
        }
        
        return [];
    }
    
    /**
     * 计算值
     */
    private function calculateValue(array $rule, array $formValues)
    {
        if (isset($rule['calculator'])) {
            $calculator = $rule['calculator'];
            
            if (is_string($calculator) && isset($this->calculators[$calculator])) {
                return $this->calculators[$calculator]($formValues);
            }
            
            if (is_callable($calculator)) {
                return $calculator($formValues);
            }
        }
        
        if (isset($rule['formula'])) {
            return $this->evaluateFormula($rule['formula'], $formValues);
        }
        
        return null;
    }
    
    /**
     * 评估公式
     */
    private function evaluateFormula(string $formula, array $formValues)
    {
        // 简单的公式评估（生产环境建议使用专门的表达式解析器）
        $safeFormula = $formula;
        
        // 替换字段名为实际值
        foreach ($formValues as $field => $value) {
            if (is_numeric($value)) {
                $safeFormula = str_replace($field, $value, $safeFormula);
            }
        }
        
        // 只允许安全的数学运算
        if (preg_match('/^[0-9+\-*\/.() ]+$/', $safeFormula)) {
            try {
                return eval("return {$safeFormula};");
            } catch (\Exception $e) {
                throw new \Exception("公式计算错误: {$e->getMessage()}");
            }
        }
        
        throw new \Exception("不安全的公式: {$formula}");
    }
    
    /**
     * 获取嵌套值
     */
    private function getNestedValue(array $data, string $path)
    {
        $keys = explode('.', $path);
        $value = $data;
        
        foreach ($keys as $key) {
            if (!is_array($value) || !isset($value[$key])) {
                return null;
            }
            $value = $value[$key];
        }
        
        return $value;
    }
    
    /**
     * 检查是否应该执行规则
     */
    private function shouldExecuteRule(array $rule, array $changedFields): bool
    {
        if (empty($changedFields)) {
            return true; // 初始加载时执行所有规则
        }
        
        // 检查规则的触发字段是否在变更字段中
        $triggerFields = [];
        
        if (isset($rule['conditions'])) {
            foreach ($rule['conditions'] as $condition) {
                if (isset($condition['field'])) {
                    $triggerFields[] = $condition['field'];
                }
            }
        }
        
        if (isset($rule['triggerFields'])) {
            $triggerFields = array_merge($triggerFields, $rule['triggerFields']);
        }
        
        return !empty(array_intersect($triggerFields, $changedFields));
    }
    
    /**
     * 获取规则缓存键
     */
    private function getRuleCacheKey(array $rule, array $formValues): string
    {
        $ruleId = $rule['id'] ?? 'unknown';
        $relevantValues = [];
        
        // 只包含相关字段的值
        if (isset($rule['conditions'])) {
            foreach ($rule['conditions'] as $condition) {
                if (isset($condition['field'])) {
                    $relevantValues[$condition['field']] = $this->getNestedValue($formValues, $condition['field']);
                }
            }
        }
        
        return 'rule:' . $ruleId . ':' . md5(json_encode($relevantValues));
    }
    
    /**
     * 合并结果
     */
    private function mergeResult(array &$target, array $source): void
    {
        foreach ($source['fieldStates'] ?? [] as $field => $state) {
            $target['fieldStates'][$field] = array_merge(
                $target['fieldStates'][$field] ?? [],
                $state
            );
        }
        
        foreach ($source['calculatedValues'] ?? [] as $field => $value) {
            $target['calculatedValues'][$field] = $value;
        }
    }
    
    /**
     * 初始化操作符
     */
    private function initializeOperators(): void
    {
        $this->operators = [
            'equals' => fn($actual, $expected) => $actual == $expected,
            'not_equals' => fn($actual, $expected) => $actual != $expected,
            'greater_than' => fn($actual, $expected) => is_numeric($actual) && is_numeric($expected) && $actual > $expected,
            'less_than' => fn($actual, $expected) => is_numeric($actual) && is_numeric($expected) && $actual < $expected,
            'greater_than_or_equal' => fn($actual, $expected) => is_numeric($actual) && is_numeric($expected) && $actual >= $expected,
            'less_than_or_equal' => fn($actual, $expected) => is_numeric($actual) && is_numeric($expected) && $actual <= $expected,
            'contains' => fn($actual, $expected) => is_string($actual) && is_string($expected) && strpos($actual, $expected) !== false,
            'not_contains' => fn($actual, $expected) => is_string($actual) && is_string($expected) && strpos($actual, $expected) === false,
            'in' => fn($actual, $expected) => is_array($expected) && in_array($actual, $expected),
            'not_in' => fn($actual, $expected) => is_array($expected) && !in_array($actual, $expected),
            'is_empty' => fn($actual) => empty($actual),
            'is_not_empty' => fn($actual) => !empty($actual),
            'matches' => fn($actual, $expected) => is_string($actual) && is_string($expected) && preg_match($expected, $actual),
            'not_matches' => fn($actual, $expected) => is_string($actual) && is_string($expected) && !preg_match($expected, $actual),
        ];
    }
    
    /**
     * 初始化计算器
     */
    private function initializeCalculators(): void
    {
        $this->calculators = [
            'calculate_age' => function($values) {
                $birthDate = $values['birth_date'] ?? null;
                if (!$birthDate) return null;
                
                $birth = new \DateTime($birthDate);
                $now = new \DateTime();
                return $now->diff($birth)->y;
            },
            
            'calculate_bmi' => function($values) {
                $height = $values['height'] ?? 0;
                $weight = $values['weight'] ?? 0;
                
                if ($height <= 0 || $weight <= 0) return 0;
                
                $heightInMeters = $height / 100;
                return round($weight / ($heightInMeters * $heightInMeters), 2);
            },
            
            'calculate_total_price' => function($values) {
                $unitPrice = $values['unit_price'] ?? 0;
                $quantity = $values['quantity'] ?? 0;
                $discount = $values['discount'] ?? 0;
                
                return $unitPrice * $quantity * (1 - $discount);
            }
        ];
    }
}
?>
```

## 数据库设计

### 数据库表结构

```sql
-- 联动规则表
CREATE TABLE linkage_rules (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    form_id VARCHAR(100) NOT NULL COMMENT '表单ID',
    rule_id VARCHAR(100) NOT NULL COMMENT '规则ID',
    rule_name VARCHAR(200) COMMENT '规则名称',
    rule_type ENUM('visibility', 'required', 'options', 'calculation', 'props') NOT NULL COMMENT '规则类型',
    target_field VARCHAR(100) NOT NULL COMMENT '目标字段',
    conditions JSON COMMENT '触发条件',
    actions JSON COMMENT '执行动作',
    priority INT DEFAULT 0 COMMENT '优先级',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    version VARCHAR(20) DEFAULT 'latest' COMMENT '版本号',
    description TEXT COMMENT '规则描述',
    created_by BIGINT UNSIGNED COMMENT '创建人',
    updated_by BIGINT UNSIGNED COMMENT '更新人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_form_id (form_id),
    INDEX idx_rule_id (rule_id),
    INDEX idx_form_version (form_id, version),
    INDEX idx_target_field (target_field),
    UNIQUE KEY uk_form_rule_version (form_id, rule_id, version)
) COMMENT='联动规则配置表';

-- 表单配置表
CREATE TABLE linkage_form_configs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    form_id VARCHAR(100) NOT NULL COMMENT '表单ID',
    form_name VARCHAR(200) COMMENT '表单名称',
    version VARCHAR(20) DEFAULT 'latest' COMMENT '版本号',
    config JSON COMMENT '表单配置',
    options JSON COMMENT '选项配置',
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '状态',
    created_by BIGINT UNSIGNED COMMENT '创建人',
    updated_by BIGINT UNSIGNED COMMENT '更新人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_form_id (form_id),
    INDEX idx_form_version (form_id, version),
    INDEX idx_status (status),
    UNIQUE KEY uk_form_version (form_id, version)
) COMMENT='表单配置表';

-- 性能指标表
CREATE TABLE linkage_performance_metrics (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    form_id VARCHAR(100) NOT NULL COMMENT '表单ID',
    execution_time DECIMAL(10,4) COMMENT '执行时间(秒)',
    rules_executed INT COMMENT '执行的规则数量',
    cache_hits INT DEFAULT 0 COMMENT '缓存命中次数',
    memory_usage INT COMMENT '内存使用(KB)',
    error_count INT DEFAULT 0 COMMENT '错误次数',
    request_data JSON COMMENT '请求数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_form_id (form_id),
    INDEX idx_created_at (created_at),
    INDEX idx_execution_time (execution_time)
) COMMENT='性能指标表';

-- 操作日志表
CREATE TABLE linkage_operation_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    form_id VARCHAR(100) NOT NULL COMMENT '表单ID',
    operation_type ENUM('create', 'update', 'delete', 'execute') NOT NULL COMMENT '操作类型',
    rule_id VARCHAR(100) COMMENT '规则ID',
    operation_data JSON COMMENT '操作数据',
    user_id BIGINT UNSIGNED COMMENT '操作用户',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_form_id (form_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) COMMENT='操作日志表';
```

### Repository实现

```php
<?php
// app/Repositories/LinkageRepository.php

namespace App\Repositories;

use Illuminate\Support\Facades\DB;
use App\Models\LinkageRule;
use App\Models\LinkageFormConfig;
use App\Models\LinkagePerformanceMetric;

class LinkageRepository
{
    /**
     * 获取表单规则
     */
    public function getFormRules(string $formId, string $version = 'latest'): array
    {
        return LinkageRule::where('form_id', $formId)
            ->where('version', $version)
            ->where('enabled', true)
            ->orderBy('priority', 'desc')
            ->orderBy('created_at', 'asc')
            ->get()
            ->toArray();
    }
    
    /**
     * 保存表单规则
     */
    public function saveFormRules(string $formId, array $rules, array $options = []): array
    {
        $version = $options['version'] ?? 'latest';
        $savedRules = [];
        
        // 删除现有规则（如果是覆盖模式）
        if ($options['overwrite'] ?? true) {
            LinkageRule::where('form_id', $formId)
                ->where('version', $version)
                ->delete();
        }
        
        foreach ($rules as $rule) {
            $savedRule = LinkageRule::create([
                'form_id' => $formId,
                'rule_id' => $rule['id'],
                'rule_name' => $rule['name'] ?? $rule['description'] ?? '',
                'rule_type' => $rule['type'],
                'target_field' => $rule['target'],
                'conditions' => json_encode($rule['conditions'] ?? []),
                'actions' => json_encode($rule['actions'] ?? []),
                'priority' => $rule['priority'] ?? 0,
                'enabled' => $rule['enabled'] ?? true,
                'version' => $version,
                'description' => $rule['description'] ?? '',
                'created_by' => auth()->id(),
                'updated_by' => auth()->id()
            ]);
            
            $savedRules[] = $savedRule->toArray();
        }
        
        return $savedRules;
    }
    
    /**
     * 记录性能指标
     */
    public function recordMetrics(array $metrics): void
    {
        LinkagePerformanceMetric::create($metrics);
    }
    
    /**
     * 获取性能指标
     */
    public function getPerformanceMetrics(string $formId, string $timeRange): array
    {
        $query = LinkagePerformanceMetric::where('form_id', $formId);
        
        // 时间范围过滤
        switch ($timeRange) {
            case '1h':
                $query->where('created_at', '>=', now()->subHour());
                break;
            case '24h':
                $query->where('created_at', '>=', now()->subDay());
                break;
            case '7d':
                $query->where('created_at', '>=', now()->subWeek());
                break;
            case '30d':
                $query->where('created_at', '>=', now()->subMonth());
                break;
        }
        
        $metrics = $query->get();
        
        return [
            'total_executions' => $metrics->count(),
            'avg_execution_time' => $metrics->avg('execution_time'),
            'max_execution_time' => $metrics->max('execution_time'),
            'min_execution_time' => $metrics->min('execution_time'),
            'total_cache_hits' => $metrics->sum('cache_hits'),
            'cache_hit_rate' => $metrics->count() > 0 ? $metrics->sum('cache_hits') / $metrics->count() : 0,
            'error_rate' => $metrics->count() > 0 ? $metrics->sum('error_count') / $metrics->count() : 0,
            'avg_memory_usage' => $metrics->avg('memory_usage'),
            'time_range' => $timeRange,
            'generated_at' => now()->toISOString()
        ];
    }
}
?>
```

## 缓存策略

### 缓存实现

```php
<?php
// app/Cache/LinkageCache.php

namespace App\Cache;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

class LinkageCache
{
    private string $prefix;
    private int $defaultTtl;
    private string $driver;
    
    public function __construct()
    {
        $this->prefix = config('linkage.cache.prefix', 'linkage:');
        $this->defaultTtl = config('linkage.cache.ttl', 3600);
        $this->driver = config('linkage.cache.driver', 'redis');
    }
    
    /**
     * 获取缓存
     */
    public function get(string $key, $default = null)
    {
        $fullKey = $this->prefix . $key;
        
        try {
            return Cache::store($this->driver)->get($fullKey, $default);
        } catch (\Exception $e) {
            \Log::warning('缓存获取失败', ['key' => $fullKey, 'error' => $e->getMessage()]);
            return $default;
        }
    }
    
    /**
     * 设置缓存
     */
    public function put(string $key, $value, ?int $ttl = null): bool
    {
        $fullKey = $this->prefix . $key;
        $ttl = $ttl ?? $this->defaultTtl;
        
        try {
            return Cache::store($this->driver)->put($fullKey, $value, $ttl);
        } catch (\Exception $e) {
            \Log::warning('缓存设置失败', ['key' => $fullKey, 'error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 删除缓存
     */
    public function forget(string $key): bool
    {
        $fullKey = $this->prefix . $key;
        
        try {
            // 支持通配符删除
            if (strpos($key, '*') !== false) {
                return $this->forgetPattern($key);
            }
            
            return Cache::store($this->driver)->forget($fullKey);
        } catch (\Exception $e) {
            \Log::warning('缓存删除失败', ['key' => $fullKey, 'error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 批量获取
     */
    public function many(array $keys): array
    {
        $fullKeys = array_map(fn($key) => $this->prefix . $key, $keys);
        
        try {
            $results = Cache::store($this->driver)->many($fullKeys);
            
            // 移除前缀
            $cleanResults = [];
            foreach ($results as $fullKey => $value) {
                $originalKey = str_replace($this->prefix, '', $fullKey);
                $cleanResults[$originalKey] = $value;
            }
            
            return $cleanResults;
        } catch (\Exception $e) {
            \Log::warning('批量缓存获取失败', ['keys' => $keys, 'error' => $e->getMessage()]);
            return array_fill_keys($keys, null);
        }
    }
    
    /**
     * 批量设置
     */
    public function putMany(array $values, ?int $ttl = null): bool
    {
        $ttl = $ttl ?? $this->defaultTtl;
        $fullValues = [];
        
        foreach ($values as $key => $value) {
            $fullValues[$this->prefix . $key] = $value;
        }
        
        try {
            return Cache::store($this->driver)->putMany($fullValues, $ttl);
        } catch (\Exception $e) {
            \Log::warning('批量缓存设置失败', ['keys' => array_keys($values), 'error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 通配符删除
     */
    private function forgetPattern(string $pattern): bool
    {
        if ($this->driver !== 'redis') {
            \Log::warning('通配符删除仅支持Redis驱动');
            return false;
        }
        
        try {
            $fullPattern = $this->prefix . $pattern;
            $keys = Redis::keys($fullPattern);
            
            if (!empty($keys)) {
                Redis::del($keys);
            }
            
            return true;
        } catch (\Exception $e) {
            \Log::warning('通配符缓存删除失败', ['pattern' => $pattern, 'error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 清空所有联动缓存
     */
    public function flush(): bool
    {
        try {
            return $this->forgetPattern('*');
        } catch (\Exception $e) {
            \Log::warning('缓存清空失败', ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public function getStats(): array
    {
        if ($this->driver !== 'redis') {
            return ['error' => '统计信息仅支持Redis驱动'];
        }
        
        try {
            $info = Redis::info();
            $keys = Redis::keys($this->prefix . '*');
            
            return [
                'total_keys' => count($keys),
                'memory_usage' => $info['used_memory_human'] ?? 'unknown',
                'hit_rate' => $info['keyspace_hits'] ?? 0,
                'miss_rate' => $info['keyspace_misses'] ?? 0,
                'driver' => $this->driver,
                'prefix' => $this->prefix
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}
?>
```

## 错误处理

### 自定义异常

```php
<?php
// app/Exceptions/LinkageException.php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;

class LinkageException extends Exception
{
    protected array $context;
    protected string $errorCode;
    
    public function __construct(
        string $message = '',
        string $errorCode = 'LINKAGE_ERROR',
        array $context = [],
        int $code = 0,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->errorCode = $errorCode;
        $this->context = $context;
    }
    
    /**
     * 渲染异常响应
     */
    public function render(): JsonResponse
    {
        $statusCode = $this->getHttpStatusCode();
        
        return response()->json([
            'success' => false,
            'error' => [
                'code' => $this->errorCode,
                'message' => $this->getMessage(),
                'context' => $this->context,
                'trace_id' => request()->header('X-Trace-ID', uniqid())
            ]
        ], $statusCode);
    }
    
    /**
     * 获取HTTP状态码
     */
    private function getHttpStatusCode(): int
    {
        $errorCodeMap = [
            'LINKAGE_VALIDATION_ERROR' => 422,
            'LINKAGE_NOT_FOUND' => 404,
            'LINKAGE_PERMISSION_DENIED' => 403,
            'LINKAGE_RATE_LIMIT' => 429,
            'LINKAGE_TIMEOUT' => 408,
            'LINKAGE_CONFIG_ERROR' => 500,
        ];
        
        return $errorCodeMap[$this->errorCode] ?? 500;
    }
    
    /**
     * 获取错误上下文
     */
    public function getContext(): array
    {
        return $this->context;
    }
    
    /**
     * 获取错误代码
     */
    public function getErrorCode(): string
    {
        return $this->errorCode;
    }
}

// 具体异常类
class LinkageValidationException extends LinkageException
{
    public function __construct(string $message, array $errors = [])
    {
        parent::__construct($message, 'LINKAGE_VALIDATION_ERROR', ['errors' => $errors]);
    }
}

class LinkageNotFoundException extends LinkageException
{
    public function __construct(string $resource, string $id)
    {
        parent::__construct(
            "资源不存在: {$resource}#{$id}",
            'LINKAGE_NOT_FOUND',
            ['resource' => $resource, 'id' => $id]
        );
    }
}

class LinkageTimeoutException extends LinkageException
{
    public function __construct(float $timeout, float $actual)
    {
        parent::__construct(
            "执行超时: 期望{$timeout}秒，实际{$actual}秒",
            'LINKAGE_TIMEOUT',
            ['timeout' => $timeout, 'actual' => $actual]
        );
    }
}
?>
```

## 性能优化

### 性能监控中间件

```php
<?php
// app/Http/Middleware/LinkagePerformanceMiddleware.php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\LinkageService;

class LinkagePerformanceMiddleware
{
    private LinkageService $linkageService;
    
    public function __construct(LinkageService $linkageService)
    {
        $this->linkageService = $linkageService;
    }
    
    public function handle(Request $request, Closure $next)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        $response = $next($request);
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        // 记录性能指标
        if ($this->isLinkageRequest($request)) {
            $this->recordPerformanceMetrics($request, [
                'execution_time' => $endTime - $startTime,
                'memory_usage' => ($endMemory - $startMemory) / 1024, // KB
                'status_code' => $response->getStatusCode()
            ]);
        }
        
        // 添加性能头信息
        $response->headers->set('X-Execution-Time', round(($endTime - $startTime) * 1000, 2) . 'ms');
        $response->headers->set('X-Memory-Usage', round(($endMemory - $startMemory) / 1024, 2) . 'KB');
        
        return $response;
    }
    
    private function isLinkageRequest(Request $request): bool
    {
        return str_starts_with($request->path(), 'api/linkage/');
    }
    
    private function recordPerformanceMetrics(Request $request, array $metrics): void
    {
        try {
            $formId = $request->get('form_id') ?? 'unknown';
            
            $this->linkageService->recordMetrics([
                'form_id' => $formId,
                'execution_time' => $metrics['execution_time'],
                'memory_usage' => $metrics['memory_usage'],
                'error_count' => $metrics['status_code'] >= 400 ? 1 : 0,
                'request_data' => [
                    'method' => $request->method(),
                    'path' => $request->path(),
                    'user_agent' => $request->userAgent(),
                    'ip' => $request->ip()
                ]
            ]);
        } catch (\Exception $e) {
            \Log::warning('性能指标记录失败', ['error' => $e->getMessage()]);
        }
    }
}
?>
```

## 部署指南

### 环境配置

```bash
# .env 配置示例

# 联动引擎配置
LINKAGE_DEBUG=false
LINKAGE_CACHE=true
LINKAGE_BATCH=true
LINKAGE_DEBOUNCE=300

# 缓存配置
LINKAGE_CACHE_DRIVER=redis
LINKAGE_CACHE_TTL=3600

# 日志配置
LINKAGE_LOG=true
LINKAGE_LOG_LEVEL=info

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0
```

### 路由配置

```php
<?php
// routes/api.php

use App\Http\Controllers\LinkageController;
use App\Http\Middleware\LinkagePerformanceMiddleware;

Route::prefix('linkage')
    ->middleware(['api', LinkagePerformanceMiddleware::class])
    ->group(function () {
        
        // 获取表单配置
        Route::get('/config', [LinkageController::class, 'getFormConfig']);
        
        // 保存规则
        Route::post('/rules', [LinkageController::class, 'saveRules']);
        
        // 执行联动
        Route::post('/execute', [LinkageController::class, 'executeRules']);
        
        // 验证规则
        