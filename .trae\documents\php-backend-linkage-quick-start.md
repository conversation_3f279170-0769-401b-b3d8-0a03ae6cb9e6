# PHP后端联动引擎快速入门指南

> 专为学生和初学者设计的简化教程

## 📚 目录

1. [什么是联动引擎](#什么是联动引擎)
2. [环境准备](#环境准备)
3. [基础配置](#基础配置)
4. [常用场景](#常用场景)
5. [实战练习](#实战练习)
6. [常见问题](#常见问题)

## 什么是联动引擎

联动引擎就像表单的"智能助手"，它可以：
- 根据用户选择自动显示/隐藏字段
- 自动计算数值（如总价、年龄等）
- 动态更新下拉选项
- 设置字段为必填或可选

### 举个例子
```
用户选择"企业用户" → 自动显示"公司名称"字段
用户输入生日 → 自动计算并显示年龄
用户选择省份 → 城市下拉框自动更新对应城市
```

## 环境准备

### 1. 基础要求
```bash
# PHP版本
PHP >= 8.0

# 数据库
MySQL >= 5.7 或 PostgreSQL >= 10

# 缓存（可选）
Redis >= 5.0
```

### 2. Laravel项目设置
```bash
# 安装Laravel（如果还没有）
composer create-project laravel/laravel linkage-demo
cd linkage-demo

# 配置数据库
cp .env.example .env
# 编辑.env文件，设置数据库连接
```

### 3. 创建必要的文件
```bash
# 创建控制器
php artisan make:controller LinkageController

# 创建服务类
php artisan make:service LinkageService

# 创建数据库迁移
php artisan make:migration create_linkage_rules_table
```

## 基础配置

### 1. 数据库表结构（简化版）

```sql
-- 创建联动规则表
CREATE TABLE linkage_rules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    form_id VARCHAR(100) NOT NULL COMMENT '表单ID',
    rule_name VARCHAR(200) COMMENT '规则名称',
    rule_type VARCHAR(50) NOT NULL COMMENT '规则类型',
    target_field VARCHAR(100) NOT NULL COMMENT '目标字段',
    trigger_field VARCHAR(100) NOT NULL COMMENT '触发字段',
    trigger_value TEXT COMMENT '触发值',
    action_type VARCHAR(50) NOT NULL COMMENT '动作类型',
    action_value TEXT COMMENT '动作值',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入示例数据
INSERT INTO linkage_rules (form_id, rule_name, rule_type, target_field, trigger_field, trigger_value, action_type, action_value) VALUES
('user_form', '显示公司字段', 'visibility', 'company_name', 'user_type', 'business', 'show', 'true'),
('user_form', '计算年龄', 'calculation', 'age', 'birth_date', '', 'calculate_age', ''),
('order_form', '计算总价', 'calculation', 'total_price', 'quantity,unit_price', '', 'calculate_total', '');
```

### 2. 控制器（简化版）

```php
<?php
// app/Http/Controllers/LinkageController.php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class LinkageController extends Controller
{
    /**
     * 获取表单联动配置
     * 用法：GET /api/linkage/config?form_id=user_form
     */
    public function getConfig(Request $request)
    {
        $formId = $request->get('form_id');
        
        if (!$formId) {
            return response()->json([
                'success' => false,
                'message' => '请提供form_id参数'
            ], 400);
        }
        
        try {
            // 从数据库获取规则
            $rules = DB::table('linkage_rules')
                ->where('form_id', $formId)
                ->where('enabled', true)
                ->get();
            
            // 转换为前端需要的格式
            $config = [
                'form_id' => $formId,
                'rules' => $rules->map(function($rule) {
                    return [
                        'id' => $rule->id,
                        'name' => $rule->rule_name,
                        'type' => $rule->rule_type,
                        'target' => $rule->target_field,
                        'trigger' => $rule->trigger_field,
                        'trigger_value' => $rule->trigger_value,
                        'action' => $rule->action_type,
                        'action_value' => $rule->action_value
                    ];
                })->toArray()
            ];
            
            return response()->json([
                'success' => true,
                'data' => $config
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取配置失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 执行联动规则
     * 用法：POST /api/linkage/execute
     * 参数：{"form_id": "user_form", "form_data": {"user_type": "business"}}
     */
    public function execute(Request $request)
    {
        $formId = $request->get('form_id');
        $formData = $request->get('form_data', []);
        
        if (!$formId) {
            return response()->json([
                'success' => false,
                'message' => '请提供form_id参数'
            ], 400);
        }
        
        try {
            // 获取所有规则
            $rules = DB::table('linkage_rules')
                ->where('form_id', $formId)
                ->where('enabled', true)
                ->get();
            
            $result = [
                'field_states' => [],
                'calculated_values' => []
            ];
            
            // 执行每个规则
            foreach ($rules as $rule) {
                $executed = $this->executeRule($rule, $formData);
                if ($executed) {
                    $result = array_merge_recursive($result, $executed);
                }
            }
            
            return response()->json([
                'success' => true,
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '执行联动失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 执行单个规则
     */
    private function executeRule($rule, $formData)
    {
        $triggerField = $rule->trigger_field;
        $triggerValue = $rule->trigger_value;
        $targetField = $rule->target_field;
        $actionType = $rule->action_type;
        $actionValue = $rule->action_value;
        
        // 检查触发条件
        if ($triggerField && isset($formData[$triggerField])) {
            $currentValue = $formData[$triggerField];
            
            // 简单的条件判断
            $conditionMet = false;
            if (empty($triggerValue)) {
                // 如果没有指定触发值，只要字段有值就触发
                $conditionMet = !empty($currentValue);
            } else {
                // 检查值是否匹配
                $conditionMet = ($currentValue == $triggerValue);
            }
            
            if (!$conditionMet) {
                return null;
            }
        }
        
        // 执行动作
        $result = [];
        
        switch ($rule->rule_type) {
            case 'visibility':
                $result['field_states'][$targetField]['visible'] = ($actionType === 'show');
                break;
                
            case 'required':
                $result['field_states'][$targetField]['required'] = ($actionType === 'required');
                break;
                
            case 'calculation':
                $calculatedValue = $this->calculateValue($actionType, $formData);
                if ($calculatedValue !== null) {
                    $result['calculated_values'][$targetField] = $calculatedValue;
                }
                break;
        }
        
        return $result;
    }
    
    /**
     * 计算值
     */
    private function calculateValue($calculationType, $formData)
    {
        switch ($calculationType) {
            case 'calculate_age':
                if (isset($formData['birth_date']) && !empty($formData['birth_date'])) {
                    $birthDate = new \DateTime($formData['birth_date']);
                    $today = new \DateTime();
                    return $today->diff($birthDate)->y;
                }
                break;
                
            case 'calculate_total':
                $quantity = $formData['quantity'] ?? 0;
                $unitPrice = $formData['unit_price'] ?? 0;
                return $quantity * $unitPrice;
                
            case 'calculate_bmi':
                $height = $formData['height'] ?? 0;
                $weight = $formData['weight'] ?? 0;
                if ($height > 0 && $weight > 0) {
                    $heightInMeters = $height / 100;
                    return round($weight / ($heightInMeters * $heightInMeters), 2);
                }
                break;
        }
        
        return null;
    }
}
?>
```

### 3. 路由配置

```php
<?php
// routes/api.php

use App\Http\Controllers\LinkageController;

// 联动引擎路由
Route::prefix('linkage')->group(function () {
    Route::get('/config', [LinkageController::class, 'getConfig']);
    Route::post('/execute', [LinkageController::class, 'execute']);
});
?>
```

## 常用场景

### 场景1：显示/隐藏字段

**需求**：用户选择"企业用户"时显示"公司名称"字段

```sql
-- 插入规则
INSERT INTO linkage_rules (
    form_id, rule_name, rule_type, target_field, 
    trigger_field, trigger_value, action_type, action_value
) VALUES (
    'user_form', '显示公司字段', 'visibility', 'company_name',
    'user_type', 'business', 'show', 'true'
);
```

**前端使用**：
```javascript
// 当用户类型改变时
function onUserTypeChange(value) {
    const formData = {
        user_type: value
    };
    
    fetch('/api/linkage/execute', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            form_id: 'user_form',
            form_data: formData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 应用字段状态
            const states = data.data.field_states;
            if (states.company_name) {
                const companyField = document.getElementById('company_name');
                companyField.style.display = states.company_name.visible ? 'block' : 'none';
            }
        }
    });
}
```

### 场景2：自动计算年龄

**需求**：用户输入生日后自动计算年龄

```sql
-- 插入规则
INSERT INTO linkage_rules (
    form_id, rule_name, rule_type, target_field, 
    trigger_field, trigger_value, action_type, action_value
) VALUES (
    'user_form', '计算年龄', 'calculation', 'age',
    'birth_date', '', 'calculate_age', ''
);
```

**前端使用**：
```javascript
// 当生日改变时
function onBirthDateChange(value) {
    const formData = {
        birth_date: value
    };
    
    fetch('/api/linkage/execute', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            form_id: 'user_form',
            form_data: formData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 应用计算值
            const values = data.data.calculated_values;
            if (values.age !== undefined) {
                document.getElementById('age').value = values.age;
            }
        }
    });
}
```

### 场景3：计算订单总价

**需求**：数量×单价=总价

```sql
-- 插入规则
INSERT INTO linkage_rules (
    form_id, rule_name, rule_type, target_field, 
    trigger_field, trigger_value, action_type, action_value
) VALUES (
    'order_form', '计算总价', 'calculation', 'total_price',
    'quantity,unit_price', '', 'calculate_total', ''
);
```

**前端使用**：
```javascript
// 当数量或单价改变时
function onPriceFieldChange() {
    const formData = {
        quantity: document.getElementById('quantity').value,
        unit_price: document.getElementById('unit_price').value
    };
    
    fetch('/api/linkage/execute', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            form_id: 'order_form',
            form_data: formData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const values = data.data.calculated_values;
            if (values.total_price !== undefined) {
                document.getElementById('total_price').value = values.total_price;
            }
        }
    });
}
```

## 实战练习

### 练习1：用户注册表单

创建一个用户注册表单，包含以下联动功能：

1. 用户类型选择"企业"时显示公司相关字段
2. 输入生日自动计算年龄
3. 选择不同省份时更新城市列表

**步骤1：创建HTML表单**
```html
<!DOCTYPE html>
<html>
<head>
    <title>用户注册</title>
</head>
<body>
    <form id="userForm">
        <div>
            <label>用户类型：</label>
            <select id="user_type" onchange="onUserTypeChange(this.value)">
                <option value="">请选择</option>
                <option value="personal">个人用户</option>
                <option value="business">企业用户</option>
            </select>
        </div>
        
        <div id="company_fields" style="display: none;">
            <label>公司名称：</label>
            <input type="text" id="company_name">
        </div>
        
        <div>
            <label>生日：</label>
            <input type="date" id="birth_date" onchange="onBirthDateChange(this.value)">
        </div>
        
        <div>
            <label>年龄：</label>
            <input type="number" id="age" readonly>
        </div>
        
        <div>
            <label>省份：</label>
            <select id="province" onchange="onProvinceChange(this.value)">
                <option value="">请选择省份</option>
                <option value="beijing">北京</option>
                <option value="shanghai">上海</option>
                <option value="guangdong">广东</option>
            </select>
        </div>
        
        <div>
            <label>城市：</label>
            <select id="city">
                <option value="">请先选择省份</option>
            </select>
        </div>
    </form>
    
    <script>
        // 联动处理函数
        function onUserTypeChange(value) {
            const companyFields = document.getElementById('company_fields');
            companyFields.style.display = (value === 'business') ? 'block' : 'none';
        }
        
        function onBirthDateChange(value) {
            if (value) {
                const birthDate = new Date(value);
                const today = new Date();
                const age = today.getFullYear() - birthDate.getFullYear();
                document.getElementById('age').value = age;
            }
        }
        
        function onProvinceChange(value) {
            const citySelect = document.getElementById('city');
            citySelect.innerHTML = '<option value="">请选择城市</option>';
            
            const cityOptions = {
                'beijing': [{'value': 'dongcheng', 'text': '东城区'}, {'value': 'xicheng', 'text': '西城区'}],
                'shanghai': [{'value': 'huangpu', 'text': '黄浦区'}, {'value': 'xuhui', 'text': '徐汇区'}],
                'guangdong': [{'value': 'guangzhou', 'text': '广州市'}, {'value': 'shenzhen', 'text': '深圳市'}]
            };
            
            if (cityOptions[value]) {
                cityOptions[value].forEach(city => {
                    const option = document.createElement('option');
                    option.value = city.value;
                    option.textContent = city.text;
                    citySelect.appendChild(option);
                });
            }
        }
    </script>
</body>
</html>
```

**步骤2：添加数据库规则**
```sql
-- 用户注册表单的联动规则
INSERT INTO linkage_rules (form_id, rule_name, rule_type, target_field, trigger_field, trigger_value, action_type, action_value) VALUES
('user_register', '显示企业字段', 'visibility', 'company_name', 'user_type', 'business', 'show', 'true'),
('user_register', '计算年龄', 'calculation', 'age', 'birth_date', '', 'calculate_age', '');
```

### 练习2：订单计算表单

创建一个订单表单，实现：
1. 自动计算小计（数量×单价）
2. 自动计算总价（小计-折扣）
3. 根据总价自动计算运费

```html
<form id="orderForm">
    <div>
        <label>商品单价：</label>
        <input type="number" id="unit_price" onchange="calculateOrder()">
    </div>
    
    <div>
        <label>购买数量：</label>
        <input type="number" id="quantity" onchange="calculateOrder()">
    </div>
    
    <div>
        <label>小计：</label>
        <input type="number" id="subtotal" readonly>
    </div>
    
    <div>
        <label>折扣金额：</label>
        <input type="number" id="discount" onchange="calculateOrder()">
    </div>
    
    <div>
        <label>运费：</label>
        <input type="number" id="shipping_fee" readonly>
    </div>
    
    <div>
        <label>总价：</label>
        <input type="number" id="total_price" readonly>
    </div>
</form>

<script>
function calculateOrder() {
    const unitPrice = parseFloat(document.getElementById('unit_price').value) || 0;
    const quantity = parseFloat(document.getElementById('quantity').value) || 0;
    const discount = parseFloat(document.getElementById('discount').value) || 0;
    
    // 计算小计
    const subtotal = unitPrice * quantity;
    document.getElementById('subtotal').value = subtotal;
    
    // 计算运费（满100免运费，否则10元）
    const shippingFee = subtotal >= 100 ? 0 : 10;
    document.getElementById('shipping_fee').value = shippingFee;
    
    // 计算总价
    const totalPrice = subtotal - discount + shippingFee;
    document.getElementById('total_price').value = totalPrice;
}
</script>
```

## 常见问题

### Q1：为什么联动不生效？

**检查清单：**
1. 数据库中是否有对应的规则记录
2. 规则的`enabled`字段是否为`true`
3. `form_id`是否匹配
4. 触发字段名和值是否正确
5. 前端是否正确调用API

**调试方法：**
```php
// 在控制器中添加调试信息
public function execute(Request $request)
{
    // 记录请求数据
    \Log::info('联动请求', $request->all());
    
    // ... 原有代码 ...
    
    // 记录执行结果
    \Log::info('联动结果', $result);
    
    return response()->json([
        'success' => true,
        'data' => $result,
        'debug' => [
            'rules_count' => $rules->count(),
            'form_data' => $formData
        ]
    ]);
}
```

### Q2：如何添加新的计算类型？

在`calculateValue`方法中添加新的case：

```php
private function calculateValue($calculationType, $formData)
{
    switch ($calculationType) {
        // 现有的计算类型...
        
        case 'calculate_discount':
            $originalPrice = $formData['original_price'] ?? 0;
            $discountRate = $formData['discount_rate'] ?? 0;
            return $originalPrice * (1 - $discountRate / 100);
            
        case 'calculate_tax':
            $amount = $formData['amount'] ?? 0;
            $taxRate = 0.13; // 13%税率
            return $amount * $taxRate;
    }
    
    return null;
}
```

### Q3：如何处理复杂的条件判断？

可以扩展`executeRule`方法：

```php
private function checkCondition($rule, $formData)
{
    $triggerField = $rule->trigger_field;
    $triggerValue = $rule->trigger_value;
    
    if (!isset($formData[$triggerField])) {
        return false;
    }
    
    $currentValue = $formData[$triggerField];
    
    // 支持多种条件类型
    if (strpos($triggerValue, '>') === 0) {
        // 大于条件：">100"
        $compareValue = (float)substr($triggerValue, 1);
        return (float)$currentValue > $compareValue;
    }
    
    if (strpos($triggerValue, '<') === 0) {
        // 小于条件："<50"
        $compareValue = (float)substr($triggerValue, 1);
        return (float)$currentValue < $compareValue;
    }
    
    if (strpos($triggerValue, 'in:') === 0) {
        // 包含条件："in:value1,value2,value3"
        $allowedValues = explode(',', substr($triggerValue, 3));
        return in_array($currentValue, $allowedValues);
    }
    
    // 默认相等判断
    return $currentValue == $triggerValue;
}
```

### Q4：如何提高性能？

1. **添加缓存**：
```php
use Illuminate\Support\Facades\Cache;

public function getConfig(Request $request)
{
    $formId = $request->get('form_id');
    $cacheKey = "linkage_config_{$formId}";
    
    return Cache::remember($cacheKey, 3600, function() use ($formId) {
        // 原有的数据库查询逻辑
        return DB::table('linkage_rules')
            ->where('form_id', $formId)
            ->where('enabled', true)
            ->get();
    });
}
```

2. **批量处理**：
```php
// 一次性获取所有需要的规则，而不是逐个查询
$rules = DB::table('linkage_rules')
    ->whereIn('form_id', $formIds)
    ->where('enabled', true)
    ->get()
    ->groupBy('form_id');
```

3. **添加索引**：
```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_form_enabled ON linkage_rules(form_id, enabled);
CREATE INDEX idx_trigger_field ON linkage_rules(trigger_field);
```

## 总结

通过这个快速入门指南，你应该能够：

✅ 理解联动引擎的基本概念
✅ 搭建基础的PHP后端环境
✅ 实现常见的联动场景
✅ 解决常见问题

**下一步学习建议：**
1. 学习更复杂的条件判断
2. 了解缓存和性能优化
3. 学习前端框架（Vue/React）集成
4. 探索高级功能（规则引擎、可视化配置等）

**记住：**
- 从简单开始，逐步增加复杂度
- 多写注释，方便后续维护
- 及时测试，确保功能正常
- 关注性能，避免过度查询数据库

祝你学习愉快！🎉