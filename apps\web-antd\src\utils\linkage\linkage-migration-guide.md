# 联动引擎V2迁移指南

本文档提供了从旧版本联动系统迁移到联动引擎V2的完整指南，包括API对比、性能优化最佳实践和常见问题解决方案。

## 目录

1. [为什么要升级到V2](#为什么要升级到v2)
2. [核心改进](#核心改进)
3. [API对比](#api对比)
4. [迁移步骤](#迁移步骤)
5. [性能优化最佳实践](#性能优化最佳实践)
6. [常见问题解决方案](#常见问题解决方案)
7. [后端配置示例](#后端配置示例)
8. [调试技巧](#调试技巧)

## 为什么要升级到V2

### 旧版本的问题

1. **性能问题**
   - 每次字段变化都会重新计算所有规则
   - 缺乏缓存机制，重复计算相同条件
   - 没有批量更新支持，频繁触发重渲染

2. **配置复杂性**
   - 规则配置冗长，难以理解和维护
   - 缺乏类型安全，容易出错
   - 没有配置验证机制

3. **调试困难**
   - 缺乏调试工具，问题定位困难
   - 没有执行日志，无法追踪规则执行过程
   - 错误信息不够详细

4. **功能限制**
   - 复杂联动逻辑难以实现
   - 缺乏动态计算支持
   - 无法处理异步操作

### V2版本的优势

1. **性能提升**
   - 智能依赖分析，只计算受影响的规则
   - 多级缓存机制，避免重复计算
   - 批量更新支持，减少重渲染
   - 防抖机制，优化高频更新

2. **配置简化**
   - 简洁的规则配置格式
   - 类型安全的配置构建器
   - 链式API，提高开发效率

3. **调试友好**
   - 完整的调试工具集
   - 详细的执行日志
   - 性能监控和优化建议

4. **功能强大**
   - 支持复杂联动逻辑
   - 动态计算和异步操作
   - 灵活的扩展机制

## 核心改进

### 1. 架构改进

```typescript
// 旧版本：基于转换器的架构
const converter = new LinkageRuleConverter();
const dependencies = converter.convertToDependencies(linkageConfig);

// 新版本：事件驱动的引擎架构
const engine = new LinkageEngineV2();
engine.setRules(simplifiedRules);
engine.updateValues(formValues);
```

### 2. 性能优化

```typescript
// 旧版本：每次都重新计算所有规则
formValues.forEach((field) => {
  allRules.forEach((rule) => {
    evaluateRule(rule, formValues);
  });
});

// 新版本：智能依赖分析，只计算受影响的规则
const affectedRules = engine.getAffectedRules(changedFields);
affectedRules.forEach((rule) => {
  engine.processRule(rule, formValues);
});
```

### 3. 配置简化

```typescript
// 旧版本：复杂的配置结构
const oldConfig = {
  displayRules: [
    {
      showWhen: [
        {
          field: 'paymentMethod',
          operator: 'equals',
          value: 'creditCard',
          logicalOperator: 'AND'
        }
      ],
      fields: ['cardNumber', 'expiryDate', 'cvv']
    }
  ],
  requiredRules: [...],
  calculationRules: [...]
};

// 新版本：简化的规则配置
const newRule = new RuleBuilder()
  .id('show-card-fields')
  .when('paymentMethod').equals('creditCard')
  .then('cardNumber').setVisible(true)
  .and('expiryDate').setVisible(true)
  .and('cvv').setVisible(true)
  .build();
```

## API对比

### 基本使用

#### 旧版本

```typescript
import { LinkageRuleConverter } from './linkage-converter';

// 创建转换器
const converter = new LinkageRuleConverter();

// 设置联动配置
const linkageConfig = {
  displayRules: [...],
  requiredRules: [...],
  calculationRules: [...]
};

// 转换为Vben表单依赖
const dependencies = converter.convertToDependencies(linkageConfig);

// 在表单中使用
const formSchema = {
  field: 'example',
  component: 'Input',
  dependencies: dependencies['example']
};
```

#### 新版本

```typescript
import { LinkageEngineV2 } from './linkage-engine-v2';
import { RuleBuilder } from './linkage-config-builder';

// 创建引擎
const engine = new LinkageEngineV2();

// 设置规则
const rules = [
  new RuleBuilder()
    .id('example-rule')
    .when('trigger')
    .equals('value')
    .then('target')
    .setVisible(true)
    .build(),
];

engine.setRules(rules);

// 设置表单值
engine.setValues(formValues);

// 监听状态变化
engine.onFieldStateChange('target', (state) => {
  // 更新UI
});
```

### 条件配置

#### 旧版本

```typescript
// 复杂的条件配置
const condition = {
  field: 'age',
  operator: 'greater_than',
  value: 18,
  logicalOperator: 'AND',
  customCondition: {
    name: 'isWorkday',
    params: [],
  },
};
```

#### 新版本

```typescript
// 简化的条件配置
const condition: SimpleCondition = {
  field: 'age',
  operator: 'greaterThan',
  value: 18,
};

// 或使用构建器
const rule = new RuleBuilder()
  .when('age')
  .greaterThan(18)
  .and('status')
  .equals('active')
  .then('discount')
  .setVisible(true)
  .build();
```

### 动作配置

#### 旧版本

```typescript
// 分散的动作配置
const displayRule = {
  showWhen: [...],
  fields: ['field1', 'field2']
};

const requiredRule = {
  requiredWhen: [...],
  fields: ['field1']
};

const calculationRule = {
  type: 'sum',
  targetField: 'total',
  sourceFields: ['price', 'tax']
};
```

#### 新版本

```typescript
// 统一的动作配置
const actions: LinkageAction[] = [
  { field: 'field1', property: 'visible', value: true },
  { field: 'field1', property: 'required', value: true },
  { field: 'field2', property: 'visible', value: true },
  {
    field: 'total',
    property: 'value',
    valueFunction: (values) => {
      return (values.price || 0) + (values.tax || 0);
    },
  },
];
```

## 迁移步骤

### 步骤1: 分析现有配置

首先分析现有的联动配置，识别所有的规则类型：

```typescript
// 1. 收集所有显示规则
const displayRules = linkageConfig.displayRules || [];

// 2. 收集所有必填规则
const requiredRules = linkageConfig.requiredRules || [];

// 3. 收集所有计算规则
const calculationRules = linkageConfig.calculationRules || [];

// 4. 收集所有选项规则
const optionsRules = linkageConfig.optionsRules || [];
```

### 步骤2: 转换规则格式

使用转换函数将旧格式转换为新格式：

```typescript
function convertDisplayRules(oldRules: any[]): SimpleLinkageRule[] {
  const newRules: SimpleLinkageRule[] = [];

  oldRules.forEach((rule, index) => {
    rule.fields.forEach((field) => {
      newRules.push({
        id: `display-rule-${index}-${field}`,
        triggerFields: rule.showWhen.map((condition) => condition.field),
        conditions: rule.showWhen.map((condition) => ({
          field: condition.field,
          operator: convertOperator(condition.operator),
          value: condition.value,
        })),
        actions: [{ field, property: 'visible', value: true }],
      });
    });
  });

  return newRules;
}

function convertOperator(oldOperator: string): string {
  const operatorMap = {
    equals: 'equals',
    not_equals: 'notEquals',
    greater_than: 'greaterThan',
    less_than: 'lessThan',
    contains: 'contains',
    is_empty: 'isEmpty',
    is_not_empty: 'isNotEmpty',
  };

  return operatorMap[oldOperator] || oldOperator;
}
```

### 步骤3: 创建新引擎实例

```typescript
// 创建新的联动引擎
const engine = new LinkageEngineV2();

// 转换并设置规则
const allRules = [
  ...convertDisplayRules(displayRules),
  ...convertRequiredRules(requiredRules),
  ...convertCalculationRules(calculationRules),
  ...convertOptionsRules(optionsRules),
];

engine.setRules(allRules);
```

### 步骤4: 集成到现有表单

```typescript
// 在表单组件中集成新引擎
const FormComponent = () => {
  const [formValues, setFormValues] = useState({});
  const [fieldStates, setFieldStates] = useState({});

  useEffect(() => {
    // 设置初始值
    engine.setValues(formValues);

    // 监听所有字段状态变化
    const unsubscribe = engine.onStateChange((states) => {
      setFieldStates(states);
    });

    return unsubscribe;
  }, []);

  const handleValueChange = (field: string, value: any) => {
    const newValues = { ...formValues, [field]: value };
    setFormValues(newValues);
    engine.updateValues({ [field]: value });
  };

  return (
    // 表单渲染逻辑
  );
};
```

### 步骤5: 测试和验证

```typescript
// 创建测试用例验证迁移结果
function testMigration() {
  const engine = new LinkageEngineV2();
  engine.setRules(migratedRules);

  // 测试各种场景
  const testCases = [
    {
      input: { paymentMethod: 'creditCard' },
      expected: { cardNumber: { visible: true } },
    },
    {
      input: { age: 25 },
      expected: { seniorDiscount: { visible: false } },
    },
  ];

  testCases.forEach((testCase, index) => {
    engine.setValues(testCase.input);
    const result = engine.getAllFieldStates();

    console.log(`测试用例 ${index + 1}:`, {
      input: testCase.input,
      expected: testCase.expected,
      actual: result,
    });
  });
}
```

## 性能优化最佳实践

### 1. 规则设计优化

#### 减少触发字段数量

```typescript
// ❌ 不好的做法：过多的触发字段
const badRule = {
  triggerFields: ['field1', 'field2', 'field3', 'field4', 'field5'],
  // ...
};

// ✅ 好的做法：只包含必要的触发字段
const goodRule = {
  triggerFields: ['field1', 'field2'],
  // ...
};
```

#### 使用具体的条件

```typescript
// ❌ 不好的做法：模糊的条件
const badCondition = {
  field: 'text',
  operator: 'contains',
  value: '',
};

// ✅ 好的做法：具体的条件
const goodCondition = {
  field: 'status',
  operator: 'equals',
  value: 'active',
};
```

### 2. 批量更新

```typescript
// ❌ 不好的做法：逐个更新字段
engine.updateValues({ field1: 'value1' });
engine.updateValues({ field2: 'value2' });
engine.updateValues({ field3: 'value3' });

// ✅ 好的做法：批量更新
engine.updateValues({
  field1: 'value1',
  field2: 'value2',
  field3: 'value3',
});
```

### 3. 缓存策略

```typescript
// 启用性能监控
engine.enablePerformanceMonitoring();

// 定期检查缓存效率
setInterval(() => {
  const metrics = engine.getPerformanceMetrics();
  if (metrics.cacheHitRate < 0.8) {
    console.warn('缓存命中率较低，考虑优化规则设计');
  }
}, 10000);
```

### 4. 防抖配置

```typescript
// 为高频更新的字段配置防抖
const engine = new LinkageEngineV2({
  debounceDelay: 300, // 300ms防抖
  batchSize: 10, // 批量处理大小
});
```

### 5. 内存管理

```typescript
// 定期清理缓存
setInterval(() => {
  engine.clearCache();
}, 300000); // 每5分钟清理一次

// 组件卸载时清理资源
useEffect(() => {
  return () => {
    engine.destroy();
  };
}, []);
```

## 常见问题解决方案

### 问题1: 规则不生效

**症状**: 设置了联动规则，但字段状态没有变化

**可能原因**:

1. 触发字段名称错误
2. 条件配置错误
3. 字段状态监听器未正确设置

**解决方案**:

```typescript
// 1. 检查触发字段名称
const rule = {
  triggerFields: ['paymentMethod'], // 确保字段名正确
  conditions: [
    { field: 'paymentMethod', operator: 'equals', value: 'creditCard' },
  ],
  actions: [{ field: 'cardNumber', property: 'visible', value: true }],
};

// 2. 启用调试模式
engine.enableDebugging({ level: 'verbose' });

// 3. 检查规则执行
engine.updateValues({ paymentMethod: 'creditCard' });
const debugInfo = engine.getDebugInfo();
console.log('调试信息:', debugInfo);
```

### 问题2: 性能问题

**症状**: 表单响应缓慢，特别是在大表单中

**解决方案**:

```typescript
// 1. 启用性能监控
engine.enablePerformanceMonitoring();

// 2. 检查性能指标
const metrics = engine.getPerformanceMetrics();
console.log('性能指标:', metrics);

// 3. 获取优化建议
const suggestions = engine.getOptimizationSuggestions();
suggestions.forEach((suggestion) => {
  console.log(`${suggestion.severity}: ${suggestion.title}`);
  console.log(`解决方案: ${suggestion.solution}`);
});

// 4. 优化规则设计
// 减少不必要的触发字段
// 使用更具体的条件
// 合并相似的规则
```

### 问题3: 复杂计算错误

**症状**: 计算结果不正确或抛出异常

**解决方案**:

```typescript
// 1. 添加错误处理
const calculationAction = {
  field: 'total',
  property: 'value',
  valueFunction: (values) => {
    try {
      const price = parseFloat(values.price) || 0;
      const tax = parseFloat(values.tax) || 0;
      const discount = parseFloat(values.discount) || 0;

      return price + tax - discount;
    } catch (error) {
      console.error('计算错误:', error);
      return 0;
    }
  },
};

// 2. 添加数据验证
const validatedAction = {
  field: 'total',
  property: 'value',
  valueFunction: (values) => {
    // 验证输入数据
    if (!values.price || !values.tax) {
      return 0;
    }

    const price = Number(values.price);
    const tax = Number(values.tax);

    if (isNaN(price) || isNaN(tax)) {
      return 0;
    }

    return price + tax;
  },
};
```

### 问题4: 内存泄漏

**症状**: 长时间使用后内存占用持续增长

**解决方案**:

```typescript
// 1. 正确清理监听器
const FormComponent = () => {
  useEffect(() => {
    const unsubscribe = engine.onFieldStateChange('field', callback);

    return () => {
      unsubscribe(); // 重要：清理监听器
    };
  }, []);
};

// 2. 定期清理缓存
useEffect(() => {
  const interval = setInterval(() => {
    engine.clearCache();
  }, 300000); // 每5分钟清理一次

  return () => {
    clearInterval(interval);
  };
}, []);

// 3. 组件卸载时销毁引擎
useEffect(() => {
  return () => {
    engine.destroy();
  };
}, []);
```

## 后端配置示例

### 简单配置

```json
{
  "linkageRules": [
    {
      "id": "show-card-fields",
      "triggerFields": ["paymentMethod"],
      "conditions": [
        {
          "field": "paymentMethod",
          "operator": "equals",
          "value": "creditCard"
        }
      ],
      "actions": [
        {
          "field": "cardNumber",
          "property": "visible",
          "value": true
        },
        {
          "field": "expiryDate",
          "property": "visible",
          "value": true
        }
      ]
    }
  ]
}
```

### 复杂配置

```json
{
  "linkageRules": [
    {
      "id": "dynamic-pricing",
      "triggerFields": ["productType", "quantity", "customerLevel"],
      "conditions": [
        {
          "field": "productType",
          "operator": "equals",
          "value": "premium"
        },
        {
          "field": "quantity",
          "operator": "greaterThan",
          "value": 10
        }
      ],
      "actions": [
        {
          "field": "discount",
          "property": "value",
          "valueExpression": "customerLevel === 'vip' ? 0.2 : 0.1"
        },
        {
          "field": "totalPrice",
          "property": "value",
          "valueExpression": "unitPrice * quantity * (1 - discount)"
        }
      ]
    }
  ]
}
```

### 后端API设计

```typescript
// 获取联动配置的API
GET /api/forms/{formId}/linkage-config

// 响应格式
{
  "success": true,
  "data": {
    "formId": "user-registration",
    "version": "1.0.0",
    "linkageRules": [...],
    "metadata": {
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "author": "admin"
    }
  }
}

// 验证联动配置的API
POST /api/forms/linkage-config/validate

// 请求体
{
  "linkageRules": [...]
}

// 响应格式
{
  "success": true,
  "data": {
    "isValid": true,
    "errors": [],
    "warnings": [
      {
        "ruleId": "rule-1",
        "message": "规则可能导致性能问题",
        "suggestion": "考虑减少触发字段数量"
      }
    ]
  }
}
```

## 调试技巧

### 1. 启用详细日志

```typescript
// 启用详细调试模式
engine.enableDebugging({
  level: 'verbose',
  logToConsole: true,
  includeStackTrace: true,
});

// 监听调试事件
engine.onDebugEvent((event) => {
  console.log(`[${event.level}] ${event.message}`, event.data);
});
```

### 2. 规则执行追踪

```typescript
// 追踪特定规则的执行
engine.traceRule('rule-id', (context) => {
  console.log('规则执行上下文:', {
    ruleId: context.ruleId,
    triggerFields: context.triggerFields,
    conditions: context.conditions,
    result: context.result,
    executionTime: context.executionTime,
  });
});
```

### 3. 字段状态快照

```typescript
// 捕获字段状态快照
const snapshot = engine.captureFieldStateSnapshot();
console.log('字段状态快照:', snapshot);

// 比较两个快照的差异
const diff = engine.compareSnapshots(snapshot1, snapshot2);
console.log('状态变化:', diff);
```

### 4. 性能分析

```typescript
// 分析性能瓶颈
const metrics = engine.getPerformanceMetrics();
console.log('性能指标:', {
  totalExecutionTime: metrics.totalExecutionTime,
  averageResponseTime: metrics.averageResponseTime,
  cacheHitRate: metrics.cacheHitRate,
  slowestRules: metrics.slowestRules,
});

// 获取优化建议
const suggestions = engine.getOptimizationSuggestions();
suggestions.forEach((suggestion) => {
  console.log(`优化建议: ${suggestion.title}`);
  console.log(`描述: ${suggestion.description}`);
  console.log(`解决方案: ${suggestion.solution}`);
});
```

### 5. 错误处理

```typescript
// 全局错误处理
engine.onError((error) => {
  console.error('联动引擎错误:', {
    message: error.message,
    ruleId: error.ruleId,
    field: error.field,
    stack: error.stack,
  });

  // 发送错误报告到后端
  sendErrorReport(error);
});

// 规则级别的错误处理
const ruleWithErrorHandling = {
  id: 'safe-calculation',
  triggerFields: ['price', 'quantity'],
  conditions: [],
  actions: [
    {
      field: 'total',
      property: 'value',
      valueFunction: (values) => {
        try {
          return values.price * values.quantity;
        } catch (error) {
          console.error('计算错误:', error);
          return 0;
        }
      },
      onError: (error, context) => {
        console.error('动作执行错误:', error, context);
        // 返回默认值或执行恢复逻辑
        return { field: 'total', property: 'value', value: 0 };
      },
    },
  ],
};
```

## 总结

联动引擎V2通过以下方式解决了旧版本的问题：

1. **性能提升**: 智能依赖分析、多级缓存、批量更新
2. **配置简化**: 简洁的API、类型安全、链式构建器
3. **调试友好**: 完整的调试工具、详细日志、性能监控
4. **功能强大**: 支持复杂联动、动态计算、异步操作

迁移到V2版本将显著提升开发效率和用户体验，建议按照本指南逐步进行迁移。
