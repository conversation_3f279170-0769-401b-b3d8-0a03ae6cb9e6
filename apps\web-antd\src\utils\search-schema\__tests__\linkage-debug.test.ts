/**
 * 联动引擎调试工具单元测试
 * 
 * 测试调试器、错误处理和日志记录功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  createDebugger,
  LinkageErrorHandler,
  createErrorHandler,
  withDebugging,
  type DebugEvent,
  type DebugConfig,
  type LinkageError
} from '../linkage-debug';

// ==================== 调试器基础功能测试 ====================

describe('LinkageDebugger - 基础功能', () => {
  let linkageDebugger: ReturnType<typeof createDebugger>;

  beforeEach(() => {
    linkageDebugger = createDebugger({ enabled: true });
  });

  afterEach(() => {
    linkageDebugger.destroy();
  });

  it('应该正确初始化调试器', () => {
    expect(linkageDebugger).toBeDefined();
    expect(typeof linkageDebugger.logConditionEvaluation).toBe('function');
    expect(typeof linkageDebugger.logActionExecution).toBe('function');
    expect(typeof linkageDebugger.getEvents).toBe('function');
  });

  it('应该正确记录条件评估事件', () => {
    const condition = {
      field: 'category',
      operator: 'eq' as const,
      value: 'electronics'
    };
    
    const values = { category: 'electronics', price: 100 };
    const result = true;
    
    linkageDebugger.logConditionEvaluation('rule1', condition, values, result);
    
    const events = linkageDebugger.getEvents();
    expect(events).toHaveLength(1);
    
    const event = events[0];
    expect(event.type).toBe('condition_evaluation');
    expect(event.ruleId).toBe('rule1');
    expect(event.data.condition).toEqual(condition);
    expect(event.data.values).toEqual(values);
    expect(event.data.result).toBe(true);
    expect(event.timestamp).toBeDefined();
  });

  it('应该正确记录动作执行事件', () => {
    const action = {
      type: 'show' as const
    };
    
    const result = {
      success: true,
      target: 'discount_field',
      changes: { visible: true }
    };
    
    linkageDebugger.logActionExecution('rule1', 'discount_field', action, result);
    
    const events = linkageDebugger.getEvents();
    expect(events).toHaveLength(1);
    
    const event = events[0];
    expect(event.type).toBe('action_execution');
    expect(event.ruleId).toBe('rule1');
    expect(event.data.target).toBe('discount_field');
    expect(event.data.action).toEqual(action);
    expect(event.data.result).toEqual(result);
  });

  it('应该正确记录规则执行开始和结束', () => {
    linkageDebugger.startRuleExecution('rule1', { category: 'electronics' });
    
    // 模拟一些执行时间
    setTimeout(() => {
      linkageDebugger.endRuleExecution('rule1', { success: true, executionTime: 50 });
    }, 10);
    
    const events = linkageDebugger.getEvents();
    expect(events).toHaveLength(2);
    
    const startEvent = events[0];
    expect(startEvent.type).toBe('rule_execution_start');
    expect(startEvent.ruleId).toBe('rule1');
    
    const endEvent = events[1];
    expect(endEvent.type).toBe('rule_execution_end');
    expect(endEvent.ruleId).toBe('rule1');
  });

  it('应该正确捕获字段状态快照', () => {
    const fieldStates = {
      category: { visible: true, disabled: false, required: false },
      price: { visible: true, disabled: false, required: true },
      discount: { visible: false, disabled: false, required: false }
    };
    
    linkageDebugger.captureFieldSnapshot(fieldStates, 'after_rule_execution');
    
    const events = linkageDebugger.getEvents();
    expect(events).toHaveLength(1);
    
    const event = events[0];
    expect(event.type).toBe('field_snapshot');
    expect(event.data.fieldStates).toEqual(fieldStates);
    expect(event.data.reason).toBe('after_rule_execution');
  });
});

// ==================== 调试配置测试 ====================

describe('LinkageDebugger - 配置选项', () => {
  it('应该支持禁用调试', () => {
    const linkageDebugger = createDebugger({ enabled: false });
    
    linkageDebugger.logConditionEvaluation('rule1', 
      { field: 'test', operator: 'eq', value: 'test' },
      { test: 'test' },
      true
    );
    
    const events = linkageDebugger.getEvents();
    expect(events).toHaveLength(0);
    
    linkageDebugger.destroy();
  });

  it('应该支持设置最大事件数量', () => {
    const linkageDebugger = createDebugger({ 
      enabled: true, 
      maxEvents: 3 
    });
    
    // 添加5个事件
    for (let i = 0; i < 5; i++) {
      linkageDebugger.logConditionEvaluation(`rule${i}`,
        { field: 'test', operator: 'eq', value: i },
        { test: i },
        true
      );
    }
    
    const events = linkageDebugger.getEvents();
    expect(events).toHaveLength(3); // 应该只保留最新的3个
    expect(events[0].ruleId).toBe('rule2'); // 最旧的应该是rule2
    expect(events[2].ruleId).toBe('rule4'); // 最新的应该是rule4
    
    linkageDebugger.destroy();
  });

  it('应该支持事件过滤', () => {
    const linkageDebugger = createDebugger({ 
      enabled: true,
      filter: (event) => event.type === 'condition_evaluation'
    });
    
    linkageDebugger.logConditionEvaluation('rule1',
      { field: 'test', operator: 'eq', value: 'test' },
      { test: 'test' },
      true
    );
    
    linkageDebugger.logActionExecution('rule1', 'target',
      { type: 'show' },
      { success: true }
    );
    
    const events = linkageDebugger.getEvents();
    expect(events).toHaveLength(1); // 只有条件评估事件被记录
    expect(events[0].type).toBe('condition_evaluation');
    
    linkageDebugger.destroy();
  });

  it('应该支持自定义日志级别', () => {
    const mockConsole = vi.spyOn(console, 'debug').mockImplementation(() => {});
    
    const linkageDebugger = createDebugger({ 
      enabled: true,
      logLevel: 'debug',
      logToConsole: true
    });
    
    linkageDebugger.logConditionEvaluation('rule1',
      { field: 'test', operator: 'eq', value: 'test' },
      { test: 'test' },
      true
    );
    
    expect(mockConsole).toHaveBeenCalled();
    
    mockConsole.mockRestore();
    linkageDebugger.destroy();
  });
});

// ==================== 调试报告测试 ====================

describe('LinkageDebugger - 调试报告', () => {
  let linkageDebugger: ReturnType<typeof createDebugger>;

  beforeEach(() => {
    linkageDebugger = createDebugger({ enabled: true });
    
    // 添加一些测试数据
    linkageDebugger.logConditionEvaluation('rule1',
      { field: 'category', operator: 'eq', value: 'electronics' },
      { category: 'electronics' },
      true
    );
    
    linkageDebugger.logActionExecution('rule1', 'subcategory',
      { type: 'updateOptions', options: [] },
      { success: true }
    );
    
    linkageDebugger.logConditionEvaluation('rule2',
      { field: 'price', operator: 'gt', value: 100 },
      { price: 150 },
      true
    );
    
    linkageDebugger.logActionExecution('rule2', 'discount',
      { type: 'show' },
      { success: true }
    );
    
    linkageDebugger.logConditionEvaluation('rule1',
      { field: 'category', operator: 'eq', value: 'clothing' },
      { category: 'electronics' },
      false
    );
  });

  afterEach(() => {
    linkageDebugger.destroy();
  });

  it('应该生成正确的调试报告', () => {
    const report = linkageDebugger.generateReport();
    
    expect(report.summary).toBeDefined();
    expect(report.summary.totalEvents).toBe(5);
    expect(report.summary.totalRulesExecuted).toBe(2);
    expect(report.summary.successfulConditions).toBe(2);
    expect(report.summary.failedConditions).toBe(1);
    expect(report.summary.successfulActions).toBe(2);
    
    expect(report.ruleStats).toBeDefined();
    expect(report.ruleStats['rule1']).toBeDefined();
    expect(report.ruleStats['rule1'].conditionEvaluations).toBe(2);
    expect(report.ruleStats['rule1'].actionExecutions).toBe(1);
    expect(report.ruleStats['rule1'].successRate).toBe(0.5); // 1成功/2总数
    
    expect(report.ruleStats['rule2']).toBeDefined();
    expect(report.ruleStats['rule2'].conditionEvaluations).toBe(1);
    expect(report.ruleStats['rule2'].actionExecutions).toBe(1);
    expect(report.ruleStats['rule2'].successRate).toBe(1); // 1成功/1总数
  });

  it('应该正确识别错误和问题', () => {
    // 添加一些错误事件
    linkageDebugger.logActionExecution('rule3', 'invalid_field',
      { type: 'show' },
      { success: false, error: 'Field not found' }
    );
    
    const report = linkageDebugger.generateReport();
    
    expect(report.errors).toBeDefined();
    expect(report.errors.length).toBeGreaterThan(0);
    
    const actionError = report.errors.find(error => 
      error.type === 'action_execution_error'
    );
    expect(actionError).toBeDefined();
    expect(actionError?.ruleId).toBe('rule3');
  });

  it('应该提供性能分析', () => {
    // 模拟一些执行时间数据
    linkageDebugger.startRuleExecution('rule1', {});
    linkageDebugger.endRuleExecution('rule1', { success: true, executionTime: 150 });
    
    linkageDebugger.startRuleExecution('rule2', {});
    linkageDebugger.endRuleExecution('rule2', { success: true, executionTime: 50 });
    
    const report = linkageDebugger.generateReport();
    
    expect(report.performance).toBeDefined();
    expect(report.performance.averageExecutionTime).toBe(100);
    expect(report.performance.slowestRule).toBe('rule1');
    expect(report.performance.fastestRule).toBe('rule2');
  });
});

// ==================== 错误处理器测试 ====================

describe('LinkageErrorHandler - 错误处理', () => {
  let errorHandler: LinkageErrorHandler;
  let mockCallback: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockCallback = vi.fn();
    errorHandler = createErrorHandler({
      onError: mockCallback,
      enableStackTrace: true
    });
  });

  it('应该正确处理联动错误', () => {
    const linkageError: LinkageError = {
      type: 'condition_evaluation_error',
      message: '条件评估失败',
      ruleId: 'rule1',
      fieldName: 'category',
      originalError: new Error('原始错误'),
      context: {
        condition: { field: 'category', operator: 'eq', value: 'test' },
        values: { category: 'electronics' }
      }
    };
    
    errorHandler.handleError(linkageError);
    
    expect(mockCallback).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'condition_evaluation_error',
        message: '条件评估失败',
        ruleId: 'rule1',
        fieldName: 'category'
      })
    );
  });

  it('应该正确处理通用错误', () => {
    const genericError = new Error('通用错误');
    
    errorHandler.handleError(genericError);
    
    expect(mockCallback).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'unknown_error',
        message: '通用错误',
        originalError: genericError
      })
    );
  });

  it('应该提供错误解决建议', () => {
    const conditionError: LinkageError = {
      type: 'condition_evaluation_error',
      message: '字段不存在',
      ruleId: 'rule1',
      fieldName: 'nonexistent_field'
    };
    
    const suggestions = errorHandler.getSuggestions(conditionError);
    
    expect(suggestions).toBeDefined();
    expect(suggestions.length).toBeGreaterThan(0);
    expect(suggestions[0]).toContain('检查字段名');
  });

  it('应该正确分类错误严重程度', () => {
    const criticalError: LinkageError = {
      type: 'circular_dependency_error',
      message: '检测到循环依赖',
      ruleId: 'rule1'
    };
    
    const warningError: LinkageError = {
      type: 'performance_warning',
      message: '执行时间过长',
      ruleId: 'rule2'
    };
    
    expect(errorHandler.getErrorSeverity(criticalError)).toBe('critical');
    expect(errorHandler.getErrorSeverity(warningError)).toBe('warning');
  });

  it('应该支持错误恢复策略', () => {
    const recoverableError: LinkageError = {
      type: 'action_execution_error',
      message: '动作执行失败',
      ruleId: 'rule1',
      fieldName: 'target_field'
    };
    
    const recoveryStrategy = errorHandler.getRecoveryStrategy(recoverableError);
    
    expect(recoveryStrategy).toBeDefined();
    expect(recoveryStrategy.type).toBe('retry');
    expect(recoveryStrategy.maxRetries).toBeGreaterThan(0);
  });
});

// ==================== 数据导出导入测试 ====================

describe('LinkageDebugger - 数据导出导入', () => {
  let linkageDebugger: ReturnType<typeof createDebugger>;

  beforeEach(() => {
    linkageDebugger = createDebugger({ enabled: true });
    
    // 添加一些测试数据
    linkageDebugger.logConditionEvaluation('rule1',
      { field: 'test', operator: 'eq', value: 'value' },
      { test: 'value' },
      true
    );
    
    linkageDebugger.logActionExecution('rule1', 'target',
      { type: 'show' },
      { success: true }
    );
  });

  afterEach(() => {
    linkageDebugger.destroy();
  });

  it('应该正确导出调试数据', () => {
    const exportedData = linkageDebugger.exportData();
    
    expect(exportedData).toBeDefined();
    expect(exportedData.events).toHaveLength(2);
    expect(exportedData.config).toBeDefined();
    expect(exportedData.timestamp).toBeDefined();
    expect(exportedData.version).toBeDefined();
  });

  it('应该正确导入调试数据', () => {
    const exportedData = linkageDebugger.exportData();
    
    const newLinkageDebugger = createDebugger({ enabled: true });
    newLinkageDebugger.importData(exportedData);
    
    const importedEvents = newLinkageDebugger.getEvents();
    expect(importedEvents).toHaveLength(2);
    expect(importedEvents[0].type).toBe('condition_evaluation');
    expect(importedEvents[1].type).toBe('action_execution');
    
    newLinkageDebugger.destroy();
  });

  it('应该支持数据合并导入', () => {
    // 在新调试器中添加一些数据
    const newLinkageDebugger = createDebugger({ enabled: true });
    newLinkageDebugger.logConditionEvaluation('rule2',
      { field: 'test2', operator: 'ne', value: 'value2' },
      { test2: 'other' },
      true
    );
    
    // 导入原有数据
    const exportedData = linkageDebugger.exportData();
    newLinkageDebugger.importData(exportedData, { merge: true });
    
    const mergedEvents = newLinkageDebugger.getEvents();
    expect(mergedEvents).toHaveLength(3); // 1个新的 + 2个导入的
    
    newLinkageDebugger.destroy();
  });
});

// ==================== 调试装饰器测试 ====================

describe('withDebugging - 调试装饰器', () => {
  it('应该正确包装函数并记录调试信息', () => {
    const linkageDebugger = createDebugger({ enabled: true });
    
    const originalFunction = (x: number, y: number) => x + y;
    const wrappedFunction = withDebugging(
      originalFunction,
      'test_function',
      linkageDebugger
    );
    
    const result = wrappedFunction(2, 3);
    
    expect(result).toBe(5);
    
    const events = linkageDebugger.getEvents();
    expect(events.length).toBeGreaterThan(0);
    
    const functionCallEvent = events.find(e => e.type === 'function_call');
    expect(functionCallEvent).toBeDefined();
    
    linkageDebugger.destroy();
  });

  it('应该正确处理异步函数', async () => {
    const linkageDebugger = createDebugger({ enabled: true });
    
    const asyncFunction = async (delay: number) => {
      await new Promise(resolve => setTimeout(resolve, delay));
      return 'completed';
    };
    
    const wrappedFunction = withDebugging(
      asyncFunction,
      'async_test_function',
      linkageDebugger
    );
    
    const result = await wrappedFunction(10);
    
    expect(result).toBe('completed');
    
    const events = linkageDebugger.getEvents();
    const functionCallEvent = events.find(e => e.type === 'function_call');
    expect(functionCallEvent).toBeDefined();
    expect(functionCallEvent?.data.executionTime).toBeGreaterThan(0);
    
    linkageDebugger.destroy();
  });

  it('应该正确处理函数执行中的错误', () => {
    const linkageDebugger = createDebugger({ enabled: true });
    
    const errorFunction = () => {
      throw new Error('测试错误');
    };
    
    const wrappedFunction = withDebugging(
      errorFunction,
      'error_test_function',
      linkageDebugger
    );
    
    expect(() => wrappedFunction()).toThrow('测试错误');
    
    const events = linkageDebugger.getEvents();
    const errorEvent = events.find(e => e.type === 'function_error');
    expect(errorEvent).toBeDefined();
    expect(errorEvent?.data.error).toBeDefined();
    
    linkageDebugger.destroy();
  });
});

// ==================== 本地存储测试 ====================

describe('LinkageDebugger - 本地存储', () => {
  // 模拟localStorage
  const mockLocalStorage = (() => {
    let store: Record<string, string> = {};
    return {
      getItem: (key: string) => store[key] || null,
      setItem: (key: string, value: string) => { store[key] = value; },
      removeItem: (key: string) => { delete store[key]; },
      clear: () => { store = {}; }
    };
  })();

  beforeEach(() => {
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true
    });
    mockLocalStorage.clear();
  });

  it('应该支持保存调试数据到本地存储', () => {
    const linkageDebugger = createDebugger({ 
      enabled: true,
      persistToLocalStorage: true,
      localStorageKey: 'test_linkage_debug'
    });
    
    linkageDebugger.logConditionEvaluation('rule1',
      { field: 'test', operator: 'eq', value: 'value' },
      { test: 'value' },
      true
    );
    
    // 手动触发保存
    linkageDebugger.saveToLocalStorage();
    
    const savedData = mockLocalStorage.getItem('test_linkage_debug');
    expect(savedData).toBeDefined();
    
    const parsedData = JSON.parse(savedData!);
    expect(parsedData.events).toHaveLength(1);
    
    linkageDebugger.destroy();
  });

  it('应该支持从本地存储加载调试数据', () => {
    const testData = {
      events: [
        {
          id: 'test-event',
          type: 'condition_evaluation',
          ruleId: 'rule1',
          timestamp: Date.now(),
          data: {
            condition: { field: 'test', operator: 'eq', value: 'value' },
            values: { test: 'value' },
            result: true
          }
        }
      ],
      config: { enabled: true },
      timestamp: Date.now(),
      version: '1.0.0'
    };
    
    mockLocalStorage.setItem('test_linkage_debug', JSON.stringify(testData));
    
    const linkageDebugger = createDebugger({ 
      enabled: true,
      persistToLocalStorage: true,
      localStorageKey: 'test_linkage_debug',
      loadFromLocalStorage: true
    });
    
    const events = linkageDebugger.getEvents();
    expect(events).toHaveLength(1);
    expect(events[0].ruleId).toBe('rule1');
    
    linkageDebugger.destroy();
  });
});

// ==================== 集成测试 ====================

describe('LinkageDebugger - 集成测试', () => {
  it('应该在完整的联动场景中正确工作', () => {
    const mockErrorHandler = vi.fn();
    
    const linkageDebugger = createDebugger({ 
      enabled: true,
      logLevel: 'debug',
      maxEvents: 100
    });
    
    const errorHandler = createErrorHandler({
      onError: mockErrorHandler
    });
    
    // 模拟完整的联动执行流程
    
    // 1. 开始规则执行
    linkageDebugger.startRuleExecution('user_type_validation', {
      userType: 'vip',
      orderAmount: 1500
    });
    
    // 2. 条件评估
    linkageDebugger.logConditionEvaluation('user_type_validation',
      { field: 'userType', operator: 'eq', value: 'vip' },
      { userType: 'vip', orderAmount: 1500 },
      true
    );
    
    linkageDebugger.logConditionEvaluation('user_type_validation',
      { field: 'orderAmount', operator: 'gt', value: 1000 },
      { userType: 'vip', orderAmount: 1500 },
      true
    );
    
    // 3. 动作执行
    linkageDebugger.logActionExecution('user_type_validation', 'vip_discount',
      { type: 'show' },
      { success: true, changes: { visible: true } }
    );
    
    linkageDebugger.logActionExecution('user_type_validation', 'discount_rate',
      { type: 'setValue', value: 0.15 },
      { success: true, changes: { value: 0.15 } }
    );
    
    // 4. 字段状态快照
    linkageDebugger.captureFieldSnapshot({
      userType: { visible: true, disabled: false, required: true },
      orderAmount: { visible: true, disabled: false, required: true },
      vip_discount: { visible: true, disabled: false, required: false },
      discount_rate: { visible: true, disabled: true, required: false }
    }, 'after_vip_validation');
    
    // 5. 结束规则执行
    linkageDebugger.endRuleExecution('user_type_validation', {
      success: true,
      executionTime: 45
    });
    
    // 6. 模拟一个错误场景
    const error: LinkageError = {
      type: 'action_execution_error',
      message: '无法更新不存在的字段',
      ruleId: 'invalid_rule',
      fieldName: 'nonexistent_field'
    };
    
    errorHandler.handleError(error);
    
    // 验证调试数据
    const events = linkageDebugger.getEvents();
    expect(events.length).toBeGreaterThan(5);
    
    const report = linkageDebugger.generateReport();
    expect(report.summary.totalRulesExecuted).toBe(1);
    expect(report.summary.successfulConditions).toBe(2);
    expect(report.summary.successfulActions).toBe(2);
    
    expect(report.ruleStats['user_type_validation']).toBeDefined();
    expect(report.ruleStats['user_type_validation'].successRate).toBe(1);
    
    // 验证错误处理
    expect(mockErrorHandler).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'action_execution_error',
        ruleId: 'invalid_rule'
      })
    );
    
    linkageDebugger.destroy();
  });
});