# ================================
# 代理配置示例
# ================================

# 基础代理配置
# 代理目标地址（必填）
VITE_PROXY_TARGET=http://127.0.0.1:8000

# 代理路径前缀（默认：/api）
VITE_PROXY_PATH_PREFIX=/api

# 路径重写规则
# 本地环境：将 /api 重写为 /apiv2
VITE_PROXY_REWRITE_FROM=^/api
VITE_PROXY_REWRITE_TO=/apiv2

# 是否改变源地址（默认：true）
VITE_PROXY_CHANGE_ORIGIN=true

# 是否支持 WebSocket（默认：true）
VITE_PROXY_WS=true

# ================================
# 两种环境配置
# ================================

# 本地开发环境 - 连接本地后端
# VITE_PROXY_TARGET=http://127.0.0.1:8000
# VITE_PROXY_REWRITE_TO=/apiv2

# 远程环境 - 连接测试/正式服务器
# VITE_PROXY_TARGET=https://erp.gbuilderchina.com/testapiv2/
# VITE_PROXY_REWRITE_TO=

# ================================
# 多个代理配置示例
# ================================

# 额外代理1：文件上传服务
# VITE_PROXY_ADDITIONAL_1_TARGET=http://localhost:3001
# VITE_PROXY_ADDITIONAL_1_PREFIX=/upload
# VITE_PROXY_ADDITIONAL_1_REWRITE_FROM=^/upload
# VITE_PROXY_ADDITIONAL_1_REWRITE_TO=/files

# 额外代理2：WebSocket 服务
# VITE_PROXY_ADDITIONAL_2_TARGET=ws://localhost:3002
# VITE_PROXY_ADDITIONAL_2_PREFIX=/ws
# VITE_PROXY_ADDITIONAL_2_REWRITE_FROM=^/ws
# VITE_PROXY_ADDITIONAL_2_REWRITE_TO=/socket

# 额外代理3：第三方 API
# VITE_PROXY_ADDITIONAL_3_TARGET=https://api.third-party.com
# VITE_PROXY_ADDITIONAL_3_PREFIX=/external
# VITE_PROXY_ADDITIONAL_3_REWRITE_FROM=^/external
# VITE_PROXY_ADDITIONAL_3_REWRITE_TO=/v1
# VITE_PROXY_ADDITIONAL_3_CHANGE_ORIGIN=true
# VITE_PROXY_ADDITIONAL_3_WS=false

# ================================
# 其他 Vite 配置
# ================================

# 应用标题
VITE_APP_TITLE=ERP系统

# API 基础路径
VITE_API_BASE_URL=/api

# 是否启用 Mock
VITE_ENABLE_MOCK=false
