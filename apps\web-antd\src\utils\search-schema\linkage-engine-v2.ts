/**
 * 联动引擎 V2 - 简化高效的表单联动解决方案
 * 
 * 核心特性：
 * 1. 事件驱动架构 - 减少不必要的计算
 * 2. 智能缓存机制 - 避免重复计算
 * 3. 批量更新策略 - 提升性能
 * 4. 简化配置接口 - 降低使用复杂度
 * 5. 完善的调试支持 - 便于问题排查
 */

// ==================== 类型定义 ====================

/** 简化的联动条件 */
export interface SimpleCondition {
  /** 字段名 */
  field: string;
  /** 操作符 */
  op: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains' | 'startsWith' | 'endsWith' | 'isEmpty' | 'isNotEmpty' | 'between' | 'custom' | '=' | '!=' | '>' | '<' | '>=' | '<=' | 'not_in' | 'empty' | 'not_empty';
  /** 比较值 */
  value?: any;
}

/** 联动动作类型 */
export type LinkageAction = 
  | { type: 'show' | 'hide' }
  | { type: 'enable' | 'disable' }
  | { type: 'required' | 'optional' }
  | { type: 'options'; options: Array<{ label: string; value: any }> }
  | { type: 'props'; props: Record<string, any> }
  | { type: 'calculate'; formula: string | ((values: Record<string, any>) => any) };

/** 简化的联动规则 */
export interface SimpleLinkageRule {
  /** 规则ID */
  id: string;
  /** 目标字段 */
  target: string;
  /** 触发条件 */
  when: SimpleCondition | SimpleCondition[];
  /** 执行动作 */
  action: LinkageAction;
  /** 优先级（数字越大优先级越高） */
  priority?: number;
  /** 是否启用 */
  enabled?: boolean;
}

/** 联动配置 */
export interface LinkageConfig {
  /** 联动规则列表 */
  rules: SimpleLinkageRule[];
  /** 全局配置 */
  options?: {
    /** 防抖延迟（毫秒） */
    debounceMs?: number;
    /** 是否启用缓存 */
    enableCache?: boolean;
    /** 是否启用调试模式 */
    debug?: boolean;
  };
}

/** 字段状态 */
interface FieldState {
  visible: boolean;
  enabled: boolean;
  required: boolean;
  options?: Array<{ label: string; value: any }>;
  props?: Record<string, any>;
  value?: any;
}

/** 缓存项 */
interface CacheItem {
  result: any;
  timestamp: number;
  dependencies: string[];
}

// ==================== 联动引擎核心类 ====================

export class LinkageEngineV2 {
  private rules: SimpleLinkageRule[] = [];
  private fieldStates = new Map<string, FieldState>();
  private cache = new Map<string, CacheItem>();
  private debounceTimers = new Map<string, NodeJS.Timeout>();
  private listeners = new Map<string, Set<(state: FieldState) => void>>();
  private options: Required<NonNullable<LinkageConfig['options']>>;
  private formValues: Record<string, any> = {};
  
  constructor(config?: LinkageConfig) {
    this.options = {
      debounceMs: 100,
      enableCache: true,
      debug: false,
      ...config?.options
    };
    
    if (config?.rules) {
      this.setRules(config.rules);
    }
  }

  // ==================== 公共API ====================

  /**
   * 设置联动规则
   */
  setRules(rules: SimpleLinkageRule[]): void {
    this.rules = rules
      .filter(rule => rule.enabled !== false)
      .sort((a, b) => (b.priority || 0) - (a.priority || 0));
    
    this.clearCache();
    this.log('设置联动规则', { count: this.rules.length });
  }

  /**
   * 更新表单值并触发联动
   */
  updateValues(values: Record<string, any>): void {
    const changedFields = this.getChangedFields(values);
    this.formValues = { ...values };
    
    if (changedFields.length === 0) return;
    
    this.log('表单值更新', { changedFields });
    
    // 批量处理联动
    this.processBatchLinkage(changedFields);
  }

  /**
   * 获取字段状态
   */
  getFieldState(fieldName: string): FieldState {
    return this.fieldStates.get(fieldName) || this.getDefaultFieldState();
  }

  /**
   * 监听字段状态变化
   */
  onFieldStateChange(fieldName: string, callback: (state: FieldState) => void): () => void {
    if (!this.listeners.has(fieldName)) {
      this.listeners.set(fieldName, new Set());
    }
    
    this.listeners.get(fieldName)!.add(callback);
    
    // 返回取消监听函数
    return () => {
      this.listeners.get(fieldName)?.delete(callback);
    };
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
    this.log('缓存已清除');
  }

  /**
   * 获取调试信息
   */
  getDebugInfo(): any {
    return {
      rulesCount: this.rules.length,
      fieldStatesCount: this.fieldStates.size,
      cacheSize: this.cache.size,
      activeTimers: this.debounceTimers.size,
      listeners: Array.from(this.listeners.entries()).map(([field, callbacks]) => ({
        field,
        callbackCount: callbacks.size
      }))
    };
  }

  // ==================== 私有方法 ====================

  /**
   * 获取变化的字段
   */
  private getChangedFields(newValues: Record<string, any>): string[] {
    const changed: string[] = [];
    
    // 检查新增或修改的字段
    for (const [field, value] of Object.entries(newValues)) {
      if (this.formValues[field] !== value) {
        changed.push(field);
      }
    }
    
    // 检查删除的字段
    for (const field of Object.keys(this.formValues)) {
      if (!(field in newValues)) {
        changed.push(field);
      }
    }
    
    return changed;
  }

  /**
   * 批量处理联动
   */
  private processBatchLinkage(changedFields: string[]): void {
    // 找出受影响的规则
    const affectedRules = this.getAffectedRules(changedFields);
    
    if (affectedRules.length === 0) return;
    
    this.log('批量处理联动', { 
      changedFields, 
      affectedRulesCount: affectedRules.length 
    });
    
    // 按目标字段分组，避免重复处理
    const rulesByTarget = new Map<string, SimpleLinkageRule[]>();
    
    for (const rule of affectedRules) {
      if (!rulesByTarget.has(rule.target)) {
        rulesByTarget.set(rule.target, []);
      }
      rulesByTarget.get(rule.target)!.push(rule);
    }
    
    // 防抖处理每个目标字段
    for (const [target, rules] of rulesByTarget) {
      this.debounceProcess(target, () => {
        this.processFieldLinkage(target, rules);
      });
    }
  }

  /**
   * 获取受影响的规则
   */
  private getAffectedRules(changedFields: string[]): SimpleLinkageRule[] {
    return this.rules.filter(rule => {
      const conditions = Array.isArray(rule.when) ? rule.when : [rule.when];
      return conditions.some(condition => 
        changedFields.includes(condition.field)
      );
    });
  }

  /**
   * 防抖处理
   */
  private debounceProcess(key: string, callback: () => void): void {
    // 清除之前的定时器
    if (this.debounceTimers.has(key)) {
      clearTimeout(this.debounceTimers.get(key)!);
    }
    
    // 设置新的定时器
    const timer = setTimeout(() => {
      callback();
      this.debounceTimers.delete(key);
    }, this.options.debounceMs);
    
    this.debounceTimers.set(key, timer);
  }

  /**
   * 处理字段联动
   */
  private processFieldLinkage(fieldName: string, rules: SimpleLinkageRule[]): void {
    const cacheKey = this.getCacheKey(fieldName, rules);
    
    // 检查缓存
    if (this.options.enableCache && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!;
      if (this.isCacheValid(cached)) {
        this.applyFieldState(fieldName, cached.result);
        this.log('使用缓存结果', { fieldName, cacheKey });
        return;
      }
    }
    
    // 计算新状态
    const currentState = this.getFieldState(fieldName);
    const newState = { ...currentState };
    
    for (const rule of rules) {
      if (this.evaluateConditions(rule.when)) {
        this.applyAction(newState, rule.action);
        this.log('应用联动规则', { 
          ruleId: rule.id, 
          fieldName, 
          action: rule.action.type 
        });
      }
    }
    
    // 缓存结果
    if (this.options.enableCache) {
      this.cache.set(cacheKey, {
        result: newState,
        timestamp: Date.now(),
        dependencies: this.getRuleDependencies(rules)
      });
    }
    
    // 应用状态
    this.applyFieldState(fieldName, newState);
  }

  /**
   * 评估条件
   */
  private evaluateConditions(when: SimpleCondition | SimpleCondition[]): boolean {
    const conditions = Array.isArray(when) ? when : [when];
    
    // 所有条件都必须满足（AND逻辑）
    return conditions.every(condition => this.evaluateCondition(condition));
  }

  /**
   * 评估单个条件
   */
  private evaluateCondition(condition: SimpleCondition): boolean {
    const fieldValue = this.formValues[condition.field];
    const { op, value } = condition;
    
    switch (op) {
      case 'eq':
      case '=':
        return fieldValue === value;
      case 'ne':
      case '!=':
        return fieldValue !== value;
      case 'gt':
      case '>':
        return Number(fieldValue) > Number(value);
      case 'lt':
      case '<':
        return Number(fieldValue) < Number(value);
      case 'gte':
      case '>=':
        return Number(fieldValue) >= Number(value);
      case 'lte':
      case '<=':
        return Number(fieldValue) <= Number(value);
      case 'in':
        return Array.isArray(value) && value.includes(fieldValue);
      case 'nin':
      case 'not_in':
        return Array.isArray(value) && !value.includes(fieldValue);
      case 'isEmpty':
      case 'empty':
        return !fieldValue || fieldValue === '' || 
               (Array.isArray(fieldValue) && fieldValue.length === 0);
      case 'isNotEmpty':
      case 'not_empty':
        return fieldValue && fieldValue !== '' && 
               (!Array.isArray(fieldValue) || fieldValue.length > 0);
      case 'contains':
        return String(fieldValue).includes(String(value));
      case 'startsWith':
        return String(fieldValue).startsWith(String(value));
      case 'endsWith':
        return String(fieldValue).endsWith(String(value));
      case 'between':
        if (Array.isArray(value) && value.length === 2) {
          const numValue = Number(fieldValue);
          return numValue >= Number(value[0]) && numValue <= Number(value[1]);
        }
        return false;
      case 'custom':
        if (typeof value === 'function') {
          return value(fieldValue, this.formValues);
        }
        return false;
      default:
        return false;
    }
  }

  /**
   * 应用动作到字段状态
   */
  private applyAction(state: FieldState, action: LinkageAction): void {
    switch (action.type) {
      case 'show':
        state.visible = true;
        break;
      case 'hide':
        state.visible = false;
        break;
      case 'enable':
        state.enabled = true;
        break;
      case 'disable':
        state.enabled = false;
        break;
      case 'required':
        state.required = true;
        break;
      case 'optional':
        state.required = false;
        break;
      case 'options':
        state.options = action.options;
        break;
      case 'props':
        state.props = { ...state.props, ...action.props };
        break;
      case 'calculate':
        if (typeof action.formula === 'function') {
          state.value = action.formula(this.formValues);
        } else {
          // 简单的公式计算（可扩展）
          state.value = this.evaluateFormula(action.formula);
        }
        break;
    }
  }

  /**
   * 应用字段状态
   */
  private applyFieldState(fieldName: string, state: FieldState): void {
    const oldState = this.fieldStates.get(fieldName);
    
    // 检查状态是否有变化
    if (oldState && this.isStateEqual(oldState, state)) {
      return;
    }
    
    this.fieldStates.set(fieldName, state);
    
    // 通知监听器
    const listeners = this.listeners.get(fieldName);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(state);
        } catch (error) {
          console.error('联动监听器执行错误:', error);
        }
      });
    }
    
    this.log('字段状态更新', { fieldName, state });
  }

  /**
   * 获取默认字段状态
   */
  private getDefaultFieldState(): FieldState {
    return {
      visible: true,
      enabled: true,
      required: false
    };
  }

  /**
   * 生成缓存键
   */
  private getCacheKey(fieldName: string, rules: SimpleLinkageRule[]): string {
    const ruleIds = rules.map(r => r.id).sort().join(',');
    const valueHash = this.getValuesHash(this.getRuleDependencies(rules));
    return `${fieldName}:${ruleIds}:${valueHash}`;
  }

  /**
   * 获取规则依赖的字段
   */
  private getRuleDependencies(rules: SimpleLinkageRule[]): string[] {
    const deps = new Set<string>();
    
    for (const rule of rules) {
      const conditions = Array.isArray(rule.when) ? rule.when : [rule.when];
      conditions.forEach(condition => deps.add(condition.field));
    }
    
    return Array.from(deps);
  }

  /**
   * 获取值的哈希
   */
  private getValuesHash(fields: string[]): string {
    const values = fields.map(field => `${field}:${this.formValues[field]}`).join('|');
    return btoa(values).slice(0, 8); // 简单哈希
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cached: CacheItem): boolean {
    // 检查依赖字段是否有变化
    const currentHash = this.getValuesHash(cached.dependencies);
    const cachedHash = this.getValuesHash(cached.dependencies);
    
    return currentHash === cachedHash;
  }

  /**
   * 检查状态是否相等
   */
  private isStateEqual(state1: FieldState, state2: FieldState): boolean {
    return JSON.stringify(state1) === JSON.stringify(state2);
  }

  /**
   * 简单公式计算
   */
  private evaluateFormula(formula: string): any {
    try {
      // 替换字段引用
      let expression = formula;
      for (const [field, value] of Object.entries(this.formValues)) {
        const regex = new RegExp(`\\b${field}\\b`, 'g');
        expression = expression.replace(regex, String(value || 0));
      }
      
      // 安全的数学表达式计算（仅支持基本运算）
      if (/^[\d+\-*/().\s]+$/.test(expression)) {
        return Function(`"use strict"; return (${expression})`)();
      }
      
      return 0;
    } catch {
      return 0;
    }
  }

  /**
   * 调试日志
   */
  private log(message: string, data?: any): void {
    if (this.options.debug) {
      console.log(`[LinkageEngineV2] ${message}`, data || '');
    }
  }

  /**
   * 销毁引擎
   */
  destroy(): void {
    // 清除所有定时器
    this.debounceTimers.forEach(timer => clearTimeout(timer));
    this.debounceTimers.clear();
    
    // 清除所有状态
    this.fieldStates.clear();
    this.cache.clear();
    this.listeners.clear();
    
    this.log('联动引擎已销毁');
  }
}

// ==================== 工具函数 ====================

/**
 * 创建联动引擎实例
 */
export function createLinkageEngine(config?: LinkageConfig): LinkageEngineV2 {
  return new LinkageEngineV2(config);
}

/**
 * 简化的规则构建器
 */
export const LinkageBuilder = {
  /**
   * 创建显示/隐藏规则
   */
  visibility(target: string, when: SimpleCondition | SimpleCondition[], show: boolean = true): SimpleLinkageRule {
    return {
      id: `visibility_${target}_${Date.now()}`,
      target,
      when,
      action: { type: show ? 'show' : 'hide' }
    };
  },

  /**
   * 创建启用/禁用规则
   */
  enabled(target: string, when: SimpleCondition | SimpleCondition[], enabled: boolean = true): SimpleLinkageRule {
    return {
      id: `enabled_${target}_${Date.now()}`,
      target,
      when,
      action: { type: enabled ? 'enable' : 'disable' }
    };
  },

  /**
   * 创建必填规则
   */
  required(target: string, when: SimpleCondition | SimpleCondition[], required: boolean = true): SimpleLinkageRule {
    return {
      id: `required_${target}_${Date.now()}`,
      target,
      when,
      action: { type: required ? 'required' : 'optional' }
    };
  },

  /**
   * 创建选项规则
   */
  options(target: string, when: SimpleCondition | SimpleCondition[], options: Array<{ label: string; value: any }>): SimpleLinkageRule {
    return {
      id: `options_${target}_${Date.now()}`,
      target,
      when,
      action: { type: 'options', options }
    };
  },

  /**
   * 创建计算规则
   */
  calculate(target: string, when: SimpleCondition | SimpleCondition[], formula: string | ((values: Record<string, any>) => any)): SimpleLinkageRule {
    return {
      id: `calculate_${target}_${Date.now()}`,
      target,
      when,
      action: { type: 'calculate', formula }
    };
  }
};

/**
 * 从旧版联动配置迁移到新版
 */
export function migrateLinkageConfig(oldConfig: any): LinkageConfig {
  // TODO: 实现迁移逻辑
  console.warn('联动配置迁移功能待实现');
  return { rules: [] };
}