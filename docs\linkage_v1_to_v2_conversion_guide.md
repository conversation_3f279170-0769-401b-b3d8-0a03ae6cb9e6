# 联动配置从V1到V2的转换指南

## 概述

本文档详细说明了如何将现有的联动配置（V1版本）转换为联动引擎V2格式，以及两个版本之间的主要差异。

## 原始配置（V1版本）分析

### 1. 显示/隐藏联动（V1格式）

```typescript
// 原始V1配置
{
  field: 'project_number',
  linkage: {
    show: {
      conditions: [
        {
          field: 'type',
          operator: 'in',
          value: ['3', '27']
        }
      ]
    }
  }
}
```

### 2. 启用/禁用联动（V1格式）

```typescript
// 原始V1配置
{
  field: 'client_id',
  linkage: {
    disabled: {
      conditions: [
        {
          field: 'type',
          operator: 'eq',
          value: '2'
        }
      ]
    }
  }
}
```

### 3. 字段赋值（V1格式）

```typescript
// 原始V1配置
{
  field: 'project_number',
  linkageAssignment: {
    'project_name': 'title',
    'client_id': 'client_id'
  }
}
```

### 4. 计算字段（V1格式）

```typescript
// 原始V1配置
{
  field: 'exchange_rate',
  calculation: {
    type: 'product',
    sourceFields: ['receivable_left', 'exchange_rate'],
    targetField: 'calculated_amount'
  }
}
```

## V2版本转换结果

### 1. 统一的联动规则结构

```php
// V2格式：统一的规则结构
'linkage' => [
    'rules' => [
        [
            'id' => 'order-type-project-visibility',
            'name' => '订单类型控制项目字段显示',
            'conditions' => [
                [
                    'field' => 'type',
                    'operator' => 'in',
                    'value' => ['3', '27'],
                ],
            ],
            'logic' => 'and',
            'actions' => [
                [
                    'type' => 'show',
                    'target' => 'project_number',
                ],
                [
                    'type' => 'show',
                    'target' => 'project_name',
                ],
            ],
        ],
    ],
]
```

### 2. 独立的计算配置

```php
// V2格式：独立的计算配置
'calculations' => [
    [
        'id' => 'receivable-amount-calculation',
        'targetField' => 'receivable_left',
        'type' => 'sum',
        'config' => [
            'tableFields' => ['total_amount'],
            'triggerFields' => ['item_request'],
            'precision' => 2,
            'defaultValue' => 0,
            'realtime' => true,
        ],
    ],
]
```

## 主要差异对比

### 1. 配置结构差异

| 方面 | V1版本 | V2版本 |
|------|--------|--------|
| 配置位置 | 分散在各个字段的 `linkage` 属性中 | 统一在顶级 `linkage` 配置中 |
| 规则组织 | 每个字段独立配置 | 按规则ID统一管理 |
| 条件逻辑 | 简单的条件匹配 | 支持复杂的逻辑组合（and/or） |
| 动作类型 | 固定的几种类型（show/hide/disabled） | 丰富的动作类型和参数 |

### 2. 功能增强对比

#### V1版本限制：
- 联动规则分散，难以维护
- 不支持复杂的条件逻辑
- 计算功能有限
- 性能优化不足
- 调试困难

#### V2版本优势：
- **统一管理**：所有联动规则集中配置
- **复杂逻辑**：支持 and/or 逻辑组合
- **丰富动作**：支持更多动作类型
- **性能优化**：内置缓存和对象池
- **调试友好**：提供详细的调试信息
- **类型安全**：完整的TypeScript类型定义

### 3. 性能提升

```php
// V2版本性能配置
'performance' => [
    'cache' => [
        'enabled' => true,
        'maxSize' => 1000,
        'ttl' => 300000, // 5分钟
    ],
    'objectPool' => [
        'enabled' => true,
        'maxSize' => 100,
    ],
    'batch' => [
        'enabled' => true,
        'delay' => 10, // 10ms
    ],
],
```

## 转换步骤

### 步骤1：分析现有联动配置

1. 识别所有使用 `linkage` 属性的字段
2. 收集所有 `linkageAssignment` 配置
3. 整理所有 `calculation` 配置
4. 分析字段间的依赖关系

### 步骤2：创建V2规则

1. **显示/隐藏规则**：
   ```php
   // 将 linkage.show/hide 转换为 rules
   [
       'id' => 'unique-rule-id',
       'conditions' => [...],
       'actions' => [
           ['type' => 'show', 'target' => 'field_name']
       ]
   ]
   ```

2. **启用/禁用规则**：
   ```php
   // 将 linkage.disabled 转换为 rules
   [
       'id' => 'unique-rule-id',
       'conditions' => [...],
       'actions' => [
           ['type' => 'disable', 'target' => 'field_name']
       ]
   ]
   ```

3. **字段赋值规则**：
   ```php
   // 将 linkageAssignment 转换为 setValue 动作
   [
       'id' => 'unique-rule-id',
       'conditions' => [...],
       'actions' => [
           [
               'type' => 'setValue',
               'target' => 'target_field',
               'value' => [
                   'type' => 'fieldMapping',
                   'sourceField' => 'source_field',
                   'mapping' => 'property_name'
               ]
           ]
       ]
   ]
   ```

4. **计算配置**：
   ```php
   // 将 calculation 转换为独立的 calculations 配置
   [
       'id' => 'unique-calculation-id',
       'targetField' => 'target_field',
       'type' => 'formula|sum|product|average',
       'config' => [...]
   ]
   ```

### 步骤3：测试和验证

1. **功能测试**：确保所有联动功能正常工作
2. **性能测试**：验证性能提升效果
3. **兼容性测试**：确保与现有代码兼容
4. **用户体验测试**：确保用户操作流畅

## 迁移建议

### 1. 渐进式迁移

- 优先迁移复杂的联动场景
- 保持V1和V2版本并存
- 逐步替换旧配置

### 2. 性能监控

```php
// 启用性能监控
'debug' => [
    'enabled' => true,
    'logLevel' => 'info',
    'showPerformanceMetrics' => true,
],
```

### 3. 团队培训

- 学习V2配置语法
- 理解新的调试工具
- 掌握性能优化技巧

## 常见问题

### Q1：V1和V2可以同时使用吗？

**A1**：可以。系统会自动检测配置格式，V1配置会被转换为V2格式执行。

### Q2：迁移后性能提升有多大？

**A2**：根据测试，复杂联动场景下性能提升可达50-80%。

### Q3：如何调试V2联动规则？

**A3**：启用调试模式，系统会提供详细的执行日志和性能指标。

### Q4：V2版本支持哪些新功能？

**A4**：
- 复杂条件逻辑（and/or组合）
- 更丰富的动作类型
- 实时计算
- 性能优化
- 调试工具

## 总结

联动引擎V2版本提供了更强大、更灵活、更高性能的联动配置方式。通过统一的配置结构、丰富的功能特性和完善的性能优化，V2版本能够满足更复杂的业务需求，同时提供更好的开发体验。

建议团队采用渐进式迁移策略，优先处理复杂场景，逐步完成整体升级。