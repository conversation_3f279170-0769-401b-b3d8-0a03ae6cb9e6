# 联动引擎迁移实战指南

## 1. 迁移前准备

### 1.1 环境检查

在开始迁移之前，请确保您的项目满足以下要求：

- React 版本 >= 16.8（支持 Hooks）
- TypeScript 版本 >= 4.0
- Ant Design 版本 >= 4.0

### 1.2 备份现有代码

```bash
# 创建备份分支
git checkout -b backup-old-linkage
git add .
git commit -m "备份旧版本联动代码"

# 切换到开发分支
git checkout develop
git checkout -b feature/linkage-v2-migration
```

### 1.3 依赖安装

确保项目中已安装联动引擎V2相关依赖：

```bash
npm install @your-org/linkage-engine-v2
# 或
yarn add @your-org/linkage-engine-v2
```

## 2. 迁移策略

### 2.1 渐进式迁移

我们推荐采用渐进式迁移策略，按以下顺序进行：

1. **简单联动**：显示/隐藏、启用/禁用
2. **条件联动**：必填验证、组件属性设置
3. **复杂联动**：多条件组合、动态选项
4. **高级功能**：表格联动、自定义函数

### 2.2 迁移检查清单

- [ ] 识别所有使用 `dependencies` 的字段
- [ ] 分析现有联动逻辑的复杂度
- [ ] 准备测试用例
- [ ] 制定回滚计划

## 3. 实战迁移示例

### 3.1 简单显示/隐藏联动

#### 迁移前（旧版本）

```javascript
// 用户注册表单 - 旧版本
const userFormSchema = [
  {
    field: 'userType',
    label: '用户类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '个人用户', value: 'personal' },
        { label: '企业用户', value: 'enterprise' }
      ]
    }
  },
  {
    field: 'companyName',
    label: '公司名称',
    component: 'Input',
    dependencies: {
      trigger: ['userType'],
      componentProps: (values) => {
        return {
          style: {
            display: values.userType === 'enterprise' ? 'block' : 'none'
          }
        };
      }
    }
  },
  {
    field: 'companyAddress',
    label: '公司地址',
    component: 'Input',
    dependencies: {
      trigger: ['userType'],
      componentProps: (values) => {
        return {
          style: {
            display: values.userType === 'enterprise' ? 'block' : 'none'
          }
        };
      }
    }
  }
];
```

#### 迁移后（V2版本）

```javascript
// 用户注册表单 - V2版本
const userFormSchema = [
  {
    field: 'userType',
    label: '用户类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '个人用户', value: 'personal' },
        { label: '企业用户', value: 'enterprise' }
      ]
    }
  },
  {
    field: 'companyName',
    label: '公司名称',
    component: 'Input'
  },
  {
    field: 'companyAddress',
    label: '公司地址',
    component: 'Input'
  }
];

// 联动配置 - V2版本
const linkageConfig = {
  rules: [
    {
      id: 'show-company-fields',
      name: '显示企业用户字段',
      conditions: [
        { field: 'userType', operator: 'eq', value: 'enterprise' }
      ],
      actions: [
        { type: 'show', target: 'companyName' },
        { type: 'show', target: 'companyAddress' },
        { type: 'setRequired', target: 'companyName', required: true },
        { type: 'setRequired', target: 'companyAddress', required: true }
      ]
    },
    {
      id: 'hide-company-fields',
      name: '隐藏企业用户字段',
      conditions: [
        { field: 'userType', operator: 'ne', value: 'enterprise' }
      ],
      actions: [
        { type: 'hide', target: 'companyName' },
        { type: 'hide', target: 'companyAddress' },
        { type: 'setOptional', target: 'companyName' },
        { type: 'setOptional', target: 'companyAddress' }
      ]
    }
  ]
};
```

### 3.2 复杂条件联动

#### 迁移前（旧版本）

```javascript
// 订单折扣计算 - 旧版本
{
  field: 'discount',
  label: '折扣金额',
  component: 'InputNumber',
  dependencies: {
    trigger: ['userType', 'orderAmount', 'memberLevel'],
    componentProps: (values) => {
      const { userType, orderAmount, memberLevel } = values;
      let maxDiscount = 0;
      let disabled = true;
      
      // 复杂的业务逻辑
      if (userType === 'vip') {
        if (orderAmount >= 1000) {
          if (memberLevel === 'gold') {
            maxDiscount = Math.min(orderAmount * 0.2, 200);
            disabled = false;
          } else if (memberLevel === 'silver') {
            maxDiscount = Math.min(orderAmount * 0.15, 150);
            disabled = false;
          } else if (memberLevel === 'bronze') {
            maxDiscount = Math.min(orderAmount * 0.1, 100);
            disabled = false;
          }
        } else if (orderAmount >= 500) {
          maxDiscount = Math.min(orderAmount * 0.05, 50);
          disabled = false;
        }
      } else if (userType === 'regular' && orderAmount >= 500) {
        maxDiscount = Math.min(orderAmount * 0.05, 25);
        disabled = false;
      }
      
      return {
        max: maxDiscount,
        disabled: disabled,
        placeholder: disabled ? '不满足折扣条件' : `最大折扣: ${maxDiscount}元`
      };
    }
  }
}
```

#### 迁移后（V2版本）

```javascript
// 订单折扣计算 - V2版本
{
  field: 'discount',
  label: '折扣金额',
  component: 'InputNumber'
}

// 联动配置 - V2版本
const discountLinkageConfig = {
  rules: [
    // VIP用户 - 金卡会员
    {
      id: 'vip-gold-discount',
      conditions: [
        { field: 'userType', operator: 'eq', value: 'vip' },
        { field: 'orderAmount', operator: 'gte', value: 1000 },
        { field: 'memberLevel', operator: 'eq', value: 'gold' }
      ],
      logic: 'and',
      actions: [
        { 
          type: 'setComponentProps', 
          target: 'discount', 
          props: { 
            disabled: false,
            placeholder: '最大折扣: 200元'
          }
        },
        {
          type: 'setValue',
          target: 'maxDiscount',
          value: { type: 'function', name: 'calculateDiscount', params: ['orderAmount', 0.2, 200] }
        }
      ]
    },
    // VIP用户 - 银卡会员
    {
      id: 'vip-silver-discount',
      conditions: [
        { field: 'userType', operator: 'eq', value: 'vip' },
        { field: 'orderAmount', operator: 'gte', value: 1000 },
        { field: 'memberLevel', operator: 'eq', value: 'silver' }
      ],
      logic: 'and',
      actions: [
        { 
          type: 'setComponentProps', 
          target: 'discount', 
          props: { 
            disabled: false,
            placeholder: '最大折扣: 150元'
          }
        },
        {
          type: 'setValue',
          target: 'maxDiscount',
          value: { type: 'function', name: 'calculateDiscount', params: ['orderAmount', 0.15, 150] }
        }
      ]
    },
    // VIP用户 - 铜卡会员
    {
      id: 'vip-bronze-discount',
      conditions: [
        { field: 'userType', operator: 'eq', value: 'vip' },
        { field: 'orderAmount', operator: 'gte', value: 1000 },
        { field: 'memberLevel', operator: 'eq', value: 'bronze' }
      ],
      logic: 'and',
      actions: [
        { 
          type: 'setComponentProps', 
          target: 'discount', 
          props: { 
            disabled: false,
            placeholder: '最大折扣: 100元'
          }
        },
        {
          type: 'setValue',
          target: 'maxDiscount',
          value: { type: 'function', name: 'calculateDiscount', params: ['orderAmount', 0.1, 100] }
        }
      ]
    },
    // VIP用户 - 中等订单
    {
      id: 'vip-medium-order',
      conditions: [
        { field: 'userType', operator: 'eq', value: 'vip' },
        { field: 'orderAmount', operator: 'gte', value: 500 },
        { field: 'orderAmount', operator: 'lt', value: 1000 }
      ],
      logic: 'and',
      actions: [
        { 
          type: 'setComponentProps', 
          target: 'discount', 
          props: { 
            disabled: false,
            placeholder: '最大折扣: 50元'
          }
        },
        {
          type: 'setValue',
          target: 'maxDiscount',
          value: { type: 'function', name: 'calculateDiscount', params: ['orderAmount', 0.05, 50] }
        }
      ]
    },
    // 普通用户
    {
      id: 'regular-user-discount',
      conditions: [
        { field: 'userType', operator: 'eq', value: 'regular' },
        { field: 'orderAmount', operator: 'gte', value: 500 }
      ],
      logic: 'and',
      actions: [
        { 
          type: 'setComponentProps', 
          target: 'discount', 
          props: { 
            disabled: false,
            placeholder: '最大折扣: 25元'
          }
        },
        {
          type: 'setValue',
          target: 'maxDiscount',
          value: { type: 'function', name: 'calculateDiscount', params: ['orderAmount', 0.05, 25] }
        }
      ]
    },
    // 默认状态 - 不满足条件
    {
      id: 'no-discount',
      conditions: [
        {
          logic: 'or',
          conditions: [
            { field: 'userType', operator: 'eq', value: 'regular', and: { field: 'orderAmount', operator: 'lt', value: 500 } },
            { field: 'userType', operator: 'eq', value: 'vip', and: { field: 'orderAmount', operator: 'lt', value: 500 } }
          ]
        }
      ],
      actions: [
        { 
          type: 'setComponentProps', 
          target: 'discount', 
          props: { 
            disabled: true,
            placeholder: '不满足折扣条件'
          }
        },
        {
          type: 'setValue',
          target: 'discount',
          value: 0
        }
      ]
    }
  ],
  // 自定义函数
  customFunctions: {
    calculateDiscount: (orderAmount, rate, maxAmount) => {
      return Math.min(orderAmount * rate, maxAmount);
    }
  }
};
```

### 3.3 动态选项联动

#### 迁移前（旧版本）

```javascript
// 地区选择联动 - 旧版本
{
  field: 'city',
  label: '城市',
  component: 'Select',
  dependencies: {
    trigger: ['province'],
    componentProps: (values) => {
      const { province } = values;
      let options = [];
      
      if (province === 'guangdong') {
        options = [
          { label: '广州市', value: 'guangzhou' },
          { label: '深圳市', value: 'shenzhen' },
          { label: '珠海市', value: 'zhuhai' }
        ];
      } else if (province === 'beijing') {
        options = [
          { label: '东城区', value: 'dongcheng' },
          { label: '西城区', value: 'xicheng' },
          { label: '朝阳区', value: 'chaoyang' }
        ];
      }
      
      return { options };
    }
  }
}
```

#### 迁移后（V2版本）

```javascript
// 地区选择联动 - V2版本
{
  field: 'city',
  label: '城市',
  component: 'Select'
}

// 联动配置 - V2版本
const regionLinkageConfig = {
  rules: [
    {
      id: 'guangdong-cities',
      conditions: [
        { field: 'province', operator: 'eq', value: 'guangdong' }
      ],
      actions: [
        {
          type: 'setOptions',
          target: 'city',
          options: [
            { label: '广州市', value: 'guangzhou' },
            { label: '深圳市', value: 'shenzhen' },
            { label: '珠海市', value: 'zhuhai' },
            { label: '佛山市', value: 'foshan' },
            { label: '东莞市', value: 'dongguan' }
          ]
        },
        { type: 'setValue', target: 'city', value: null } // 清空之前的选择
      ]
    },
    {
      id: 'beijing-districts',
      conditions: [
        { field: 'province', operator: 'eq', value: 'beijing' }
      ],
      actions: [
        {
          type: 'setOptions',
          target: 'city',
          options: [
            { label: '东城区', value: 'dongcheng' },
            { label: '西城区', value: 'xicheng' },
            { label: '朝阳区', value: 'chaoyang' },
            { label: '海淀区', value: 'haidian' },
            { label: '丰台区', value: 'fengtai' }
          ]
        },
        { type: 'setValue', target: 'city', value: null }
      ]
    },
    {
      id: 'shanghai-districts',
      conditions: [
        { field: 'province', operator: 'eq', value: 'shanghai' }
      ],
      actions: [
        {
          type: 'setOptions',
          target: 'city',
          options: [
            { label: '黄浦区', value: 'huangpu' },
            { label: '徐汇区', value: 'xuhui' },
            { label: '长宁区', value: 'changning' },
            { label: '静安区', value: 'jingan' },
            { label: '普陀区', value: 'putuo' }
          ]
        },
        { type: 'setValue', target: 'city', value: null }
      ]
    }
  ]
};
```

## 4. 组件集成

### 4.1 React组件集成

```typescript
// FormWithLinkage.tsx
import React, { useEffect, useRef } from 'react';
import { Form } from 'antd';
import { LinkageEngineV2 } from '@your-org/linkage-engine-v2';

interface FormWithLinkageProps {
  schema: any[];
  linkageConfig: any;
  onValuesChange?: (changedValues: any, allValues: any) => void;
}

const FormWithLinkage: React.FC<FormWithLinkageProps> = ({
  schema,
  linkageConfig,
  onValuesChange
}) => {
  const [form] = Form.useForm();
  const linkageEngineRef = useRef<LinkageEngineV2>();
  
  useEffect(() => {
    // 初始化联动引擎
    linkageEngineRef.current = new LinkageEngineV2({
      ...linkageConfig,
      debug: process.env.NODE_ENV === 'development'
    });
    
    return () => {
      // 清理资源
      linkageEngineRef.current?.dispose();
    };
  }, [linkageConfig]);
  
  const handleValuesChange = (changedValues: any, allValues: any) => {
    // 执行联动逻辑
    Object.keys(changedValues).forEach(fieldName => {
      linkageEngineRef.current?.execute(
        fieldName, 
        changedValues[fieldName], 
        allValues
      );
    });
    
    // 调用外部回调
    onValuesChange?.(changedValues, allValues);
  };
  
  return (
    <Form
      form={form}
      onValuesChange={handleValuesChange}
      layout="vertical"
    >
      {/* 渲染表单字段 */}
      {schema.map(field => (
        <FormField key={field.field} {...field} />
      ))}
    </Form>
  );
};

export default FormWithLinkage;
```

### 4.2 使用示例

```typescript
// App.tsx
import React from 'react';
import FormWithLinkage from './components/FormWithLinkage';
import { userFormSchema, linkageConfig } from './config/form-config';

const App: React.FC = () => {
  const handleFormChange = (changedValues: any, allValues: any) => {
    console.log('表单值变化:', changedValues, allValues);
  };
  
  return (
    <div className="app">
      <h1>用户注册表单</h1>
      <FormWithLinkage
        schema={userFormSchema}
        linkageConfig={linkageConfig}
        onValuesChange={handleFormChange}
      />
    </div>
  );
};

export default App;
```

## 5. 测试策略

### 5.1 单元测试

```typescript
// linkage.test.ts
import { LinkageEngineV2 } from '@your-org/linkage-engine-v2';

describe('联动引擎V2测试', () => {
  let linkageEngine: LinkageEngineV2;
  
  beforeEach(() => {
    linkageEngine = new LinkageEngineV2({
      rules: [
        {
          id: 'test-rule',
          conditions: [{ field: 'userType', operator: 'eq', value: 'enterprise' }],
          actions: [{ type: 'show', target: 'companyName' }]
        }
      ]
    });
  });
  
  afterEach(() => {
    linkageEngine.dispose();
  });
  
  test('应该正确执行显示联动', () => {
    const mockShowAction = jest.fn();
    linkageEngine.onAction('show', mockShowAction);
    
    linkageEngine.execute('userType', 'enterprise', { userType: 'enterprise' });
    
    expect(mockShowAction).toHaveBeenCalledWith('companyName');
  });
  
  test('应该正确处理条件不匹配的情况', () => {
    const mockShowAction = jest.fn();
    linkageEngine.onAction('show', mockShowAction);
    
    linkageEngine.execute('userType', 'personal', { userType: 'personal' });
    
    expect(mockShowAction).not.toHaveBeenCalled();
  });
});
```

### 5.2 集成测试

```typescript
// form-integration.test.tsx
import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react';
import FormWithLinkage from '../components/FormWithLinkage';

const testSchema = [
  {
    field: 'userType',
    label: '用户类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '个人用户', value: 'personal' },
        { label: '企业用户', value: 'enterprise' }
      ]
    }
  },
  {
    field: 'companyName',
    label: '公司名称',
    component: 'Input'
  }
];

const testLinkageConfig = {
  rules: [
    {
      id: 'show-company-name',
      conditions: [{ field: 'userType', operator: 'eq', value: 'enterprise' }],
      actions: [{ type: 'show', target: 'companyName' }]
    }
  ]
};

describe('表单联动集成测试', () => {
  test('选择企业用户时应该显示公司名称字段', async () => {
    const { getByLabelText, getByText } = render(
      <FormWithLinkage
        schema={testSchema}
        linkageConfig={testLinkageConfig}
      />
    );
    
    // 选择企业用户
    const userTypeSelect = getByLabelText('用户类型');
    fireEvent.change(userTypeSelect, { target: { value: 'enterprise' } });
    
    // 等待联动执行
    await waitFor(() => {
      expect(getByLabelText('公司名称')).toBeVisible();
    });
  });
});
```

## 6. 性能优化

### 6.1 启用缓存

```typescript
// 为频繁执行的联动启用缓存
const optimizedLinkageConfig = {
  rules: [...rules],
  performance: {
    cache: {
      enabled: true,
      maxSize: 1000,
      ttl: 300000 // 5分钟
    },
    objectPool: {
      enabled: true,
      maxSize: 100
    },
    batch: {
      enabled: true,
      delay: 10 // 10ms批处理延迟
    }
  }
};
```

### 6.2 监控性能

```typescript
// 性能监控
const linkageEngine = new LinkageEngineV2(config);

// 定期检查性能指标
setInterval(() => {
  const metrics = linkageEngine.getPerformanceMetrics();
  
  if (metrics.averageExecutionTime > 50) {
    console.warn('联动执行时间过长:', metrics.averageExecutionTime);
  }
  
  if (metrics.cacheHitRate < 0.8) {
    console.warn('缓存命中率过低:', metrics.cacheHitRate);
  }
}, 60000); // 每分钟检查一次
```

## 7. 常见问题和解决方案

### 7.1 迁移过程中的常见问题

#### 问题1：复杂的条件逻辑难以转换

**解决方案**：将复杂逻辑拆分为多个简单规则

```typescript
// 错误的做法：试图在一个规则中处理所有逻辑
{
  conditions: [
    // 非常复杂的条件组合
  ],
  actions: [
    // 大量的动作
  ]
}

// 正确的做法：拆分为多个简单规则
[
  {
    id: 'rule-1',
    conditions: [{ field: 'type', operator: 'eq', value: 'A' }],
    actions: [{ type: 'show', target: 'fieldA' }]
  },
  {
    id: 'rule-2',
    conditions: [{ field: 'type', operator: 'eq', value: 'B' }],
    actions: [{ type: 'show', target: 'fieldB' }]
  }
]
```

#### 问题2：性能下降

**解决方案**：启用性能优化选项

```typescript
// 启用所有性能优化
const config = {
  rules: [...],
  performance: {
    cache: { enabled: true, maxSize: 1000, ttl: 300000 },
    objectPool: { enabled: true, maxSize: 100 },
    batch: { enabled: true, delay: 10 },
    debug: { enabled: false } // 生产环境关闭调试
  }
};
```

#### 问题3：调试困难

**解决方案**：使用内置调试工具

```typescript
// 开发环境启用调试
const config = {
  rules: [...],
  debug: process.env.NODE_ENV === 'development',
  performance: {
    debug: {
      enabled: true,
      logLevel: 'debug',
      showPerformanceMetrics: true
    }
  }
};

// 查看调试信息
const debugInfo = linkageEngine.exportDebugInfo();
console.log('调试信息:', debugInfo);
```

### 7.2 最佳实践

1. **渐进式迁移**：不要一次性迁移所有联动，按模块逐步进行
2. **充分测试**：为每个联动规则编写测试用例
3. **性能监控**：持续监控联动性能，及时优化
4. **文档更新**：及时更新相关文档和注释
5. **团队培训**：确保团队成员了解新的联动配置方式

## 8. 迁移检查清单

### 8.1 迁移前检查

- [ ] 备份现有代码
- [ ] 安装V2依赖
- [ ] 准备测试环境
- [ ] 制定迁移计划

### 8.2 迁移过程检查

- [ ] 识别所有dependencies使用
- [ ] 转换为V2配置格式
- [ ] 更新组件集成代码
- [ ] 编写测试用例
- [ ] 性能测试

### 8.3 迁移后检查

- [ ] 功能测试通过
- [ ] 性能指标正常
- [ ] 无控制台错误
- [ ] 用户体验良好
- [ ] 文档已更新

## 9. 总结

通过本迁移指南，您应该能够：

1. 理解旧版本和V2版本的差异
2. 掌握渐进式迁移策略
3. 学会转换各种类型的联动逻辑
4. 了解性能优化和调试技巧
5. 避免常见的迁移陷阱

联动引擎V2不仅提供了更强大的功能，还大大简化了配置和维护工作。通过合理的迁移策略和充分的测试，您可以顺利完成升级，享受V2版本带来的所有优势。

如果在迁移过程中遇到问题，请参考技术文档或联系技术支持团队。