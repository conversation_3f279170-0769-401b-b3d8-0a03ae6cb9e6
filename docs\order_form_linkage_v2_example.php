<?php
/**
 * 订单表单联动引擎V2配置示例
 * 将现有的联动配置转换为联动引擎V2格式
 * 
 * 原始配置来源：测试.ts
 * 转换日期：2024年
 */

return [
    // 基本字段配置
    'fields' => [
        [
            'field' => 'type',
            'type' => 'radio',
            'title' => '订单类型',
            'required' => true,
            'config' => [
                'options' => [
                    '2' => '备货单',
                    '3' => '销售单',
                    '27' => '费用单',
                ],
                'optionType' => 'button',
                'buttonStyle' => 'solid',
                'editPermission' => 'add-only',
            ],
            'default' => '3',
        ],
        [
            'field' => 'project_number',
            'type' => 'apiSelect',
            'title' => '项目ID',
            'required' => true,
            'config' => [
                'url' => '/erp/pro/getProjectByCrm',
                'labelField' => 'id',
                'valueField' => 'id',
                'editPermission' => 'add-only',
            ],
        ],
        [
            'field' => 'project_name',
            'type' => 'text',
            'title' => '项目名称',
            'required' => true,
            'config' => [
                'editPermission' => 'add-only',
            ],
        ],
        [
            'field' => 'client_id',
            'type' => 'apiSelect',
            'title' => '客户',
            'config' => [
                'url' => '/erp/customermanage/getPageList',
                'labelField' => 'name',
                'valueField' => 'id',
                'placeholder' => '请选择人员',
                'allowClear' => true,
                'showSearch' => true,
                'editPermission' => 'add-only',
            ],
        ],
        [
            'field' => 'currency',
            'type' => 'text',
            'title' => '货币',
            'disabled' => true,
        ],
        [
            'field' => 'exchange_rate',
            'type' => 'apiSelect',
            'title' => '汇率',
            'required' => true,
            'config' => [
                'url' => '/erp/getRmbquot',
                'labelField' => 'name',
                'valueField' => 'fBuyPri',
                'placeholder' => '请选择',
                'allowClear' => true,
                'showSearch' => true,
                'editPermission' => 'add-only',
                'readonly' => true,
            ],
            'default' => '1.000000',
        ],
        [
            'field' => 'receivable_left',
            'type' => 'calculated',
            'title' => '应收金额',
            'required' => true,
            'disabled' => true,
            'config' => [
                'readonly' => true,
                'prefix' => '¥',
            ],
        ],
        [
            'field' => 'foreign_currency_amount',
            'type' => 'calculated',
            'title' => '外汇总金额',
            'required' => true,
            'disabled' => true,
            'config' => [
                'readonly' => true,
                'prefix' => '¥',
            ],
        ],
    ],
    
    // 联动引擎V2配置
    'linkage' => [
        'rules' => [
            // 规则1：根据订单类型控制项目相关字段的显示/隐藏
            [
                'id' => 'order-type-project-visibility',
                'name' => '订单类型控制项目字段显示',
                'conditions' => [
                    [
                        'field' => 'type',
                        'operator' => 'in',
                        'value' => ['3', '27'], // 销售单或费用单
                    ],
                ],
                'logic' => 'and',
                'actions' => [
                    [
                        'type' => 'show',
                        'target' => 'project_number',
                    ],
                    [
                        'type' => 'show',
                        'target' => 'project_name',
                    ],
                    [
                        'type' => 'setRequired',
                        'target' => 'project_number',
                        'required' => true,
                    ],
                    [
                        'type' => 'setRequired',
                        'target' => 'project_name',
                        'required' => true,
                    ],
                ],
            ],
            
            // 规则2：备货单时隐藏项目相关字段
            [
                'id' => 'stock-order-hide-project',
                'name' => '备货单隐藏项目字段',
                'conditions' => [
                    [
                        'field' => 'type',
                        'operator' => 'eq',
                        'value' => '2', // 备货单
                    ],
                ],
                'logic' => 'and',
                'actions' => [
                    [
                        'type' => 'hide',
                        'target' => 'project_number',
                    ],
                    [
                        'type' => 'hide',
                        'target' => 'project_name',
                    ],
                    [
                        'type' => 'setOptional',
                        'target' => 'project_number',
                    ],
                    [
                        'type' => 'setOptional',
                        'target' => 'project_name',
                    ],
                ],
            ],
            
            // 规则3：根据订单类型控制客户字段的启用/禁用
            [
                'id' => 'order-type-client-disabled',
                'name' => '订单类型控制客户字段状态',
                'conditions' => [
                    [
                        'field' => 'type',
                        'operator' => 'eq',
                        'value' => '2', // 备货单
                    ],
                ],
                'logic' => 'and',
                'actions' => [
                    [
                        'type' => 'disable',
                        'target' => 'client_id',
                    ],
                ],
            ],
            
            // 规则4：销售单和费用单时启用客户字段
            [
                'id' => 'order-type-client-enabled',
                'name' => '销售单费用单启用客户字段',
                'conditions' => [
                    [
                        'field' => 'type',
                        'operator' => 'in',
                        'value' => ['3', '27'], // 销售单或费用单
                    ],
                ],
                'logic' => 'and',
                'actions' => [
                    [
                        'type' => 'enable',
                        'target' => 'client_id',
                    ],
                ],
            ],
            
            // 规则5：项目选择时自动赋值相关字段
            [
                'id' => 'project-auto-assignment',
                'name' => '项目选择自动赋值',
                'conditions' => [
                    [
                        'field' => 'project_number',
                        'operator' => 'isNotEmpty',
                    ],
                ],
                'logic' => 'and',
                'actions' => [
                    [
                        'type' => 'setValue',
                        'target' => 'project_name',
                        'value' => [
                            'type' => 'fieldMapping',
                            'sourceField' => 'project_number',
                            'mapping' => 'title',
                        ],
                    ],
                    [
                        'type' => 'setValue',
                        'target' => 'client_id',
                        'value' => [
                            'type' => 'fieldMapping',
                            'sourceField' => 'project_number',
                            'mapping' => 'client_id',
                        ],
                    ],
                ],
            ],
            
            // 规则6：汇率选择时自动赋值货币字段
            [
                'id' => 'exchange-rate-currency-assignment',
                'name' => '汇率选择自动赋值货币',
                'conditions' => [
                    [
                        'field' => 'exchange_rate',
                        'operator' => 'isNotEmpty',
                    ],
                ],
                'logic' => 'and',
                'actions' => [
                    [
                        'type' => 'setValue',
                        'target' => 'currency',
                        'value' => [
                            'type' => 'fieldMapping',
                            'sourceField' => 'exchange_rate',
                            'mapping' => 'name',
                        ],
                    ],
                ],
            ],
        ],
        
        // 计算字段配置
        'calculations' => [
            // 应收金额计算
            [
                'id' => 'receivable-amount-calculation',
                'targetField' => 'receivable_left',
                'type' => 'sum',
                'config' => [
                    'tableFields' => ['total_amount'],
                    'triggerFields' => ['item_request'],
                    'precision' => 2,
                    'defaultValue' => 0,
                    'realtime' => true,
                ],
            ],
            
            // 外汇总金额计算
            [
                'id' => 'foreign-currency-calculation',
                'targetField' => 'foreign_currency_amount',
                'type' => 'formula',
                'config' => [
                    'formula' => '(foreign_currency_unit_pirce * 100) * qty_request_actual',
                    'tableFields' => ['foreign_currency_unit_pirce', 'qty_request_actual'],
                    'triggerFields' => ['item_request'],
                    'precision' => 2,
                    'defaultValue' => 0,
                    'realtime' => true,
                ],
            ],
            
            // 汇率相关计算（如果需要）
            [
                'id' => 'exchange-rate-calculation',
                'targetField' => 'calculated_exchange_amount',
                'type' => 'product',
                'config' => [
                    'sourceFields' => ['receivable_left', 'exchange_rate'],
                    'triggerFields' => ['receivable_left', 'exchange_rate'],
                    'precision' => 2,
                    'defaultValue' => 0,
                ],
            ],
        ],
        
        // 性能配置
        'performance' => [
            'cache' => [
                'enabled' => true,
                'maxSize' => 1000,
                'ttl' => 300000, // 5分钟
            ],
            'objectPool' => [
                'enabled' => true,
                'maxSize' => 100,
            ],
            'batch' => [
                'enabled' => true,
                'delay' => 10, // 10ms
            ],
        ],
        
        // 调试配置
        'debug' => [
            'enabled' => false, // 生产环境关闭
            'logLevel' => 'info',
            'showPerformanceMetrics' => false,
        ],
    ],
    
    // 表格字段联动配置
    'tableFields' => [
        'item_request' => [
            'linkage' => [
                'rules' => [
                    // 表格内计算规则
                    [
                        'id' => 'table-total-amount-calculation',
                        'name' => '表格总价计算',
                        'conditions' => [
                            [
                                'field' => 'unit_price',
                                'operator' => 'isNotEmpty',
                            ],
                            [
                                'field' => 'qty_request_actual',
                                'operator' => 'isNotEmpty',
                            ],
                        ],
                        'logic' => 'and',
                        'actions' => [
                            [
                                'type' => 'setValue',
                                'target' => 'total_amount',
                                'value' => [
                                    'type' => 'calculation',
                                    'formula' => 'unit_price * qty_request_actual',
                                    'precision' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
                
                'calculations' => [
                    // 表格行总价计算
                    [
                        'id' => 'row-total-calculation',
                        'targetField' => 'total_amount',
                        'type' => 'product',
                        'config' => [
                            'sourceFields' => ['unit_price', 'qty_request_actual'],
                            'triggerFields' => ['unit_price', 'qty_request_actual'],
                            'precision' => 2,
                            'defaultValue' => 0,
                        ],
                    ],
                ],
            ],
        ],
    ],
];

?>