<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>表单联动测试</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      .form-group {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .form-group label {
        min-width: 120px;
        font-weight: bold;
      }
      .form-group input,
      .form-group select {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        min-width: 200px;
      }
      .hidden {
        display: none !important;
      }
      .disabled {
        opacity: 0.6;
        pointer-events: none;
      }
      .calculated {
        background-color: #f0f0f0;
        color: #666;
      }
      .test-section {
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
      }
      .test-section h3 {
        margin-top: 0;
        color: #333;
        border-bottom: 2px solid #007bff;
        padding-bottom: 5px;
      }
      .table-container {
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
      }
      .table-row {
        display: flex;
        border-bottom: 1px solid #eee;
        padding: 10px;
        align-items: center;
        gap: 10px;
      }
      .table-row:last-child {
        border-bottom: none;
      }
      .table-header {
        background-color: #f8f9fa;
        font-weight: bold;
      }
      .table-cell {
        flex: 1;
        min-width: 100px;
      }
      .table-cell input {
        width: 100%;
        padding: 5px;
        border: 1px solid #ddd;
        border-radius: 3px;
      }
      .btn {
        padding: 8px 16px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      }
      .btn:hover {
        background-color: #0056b3;
      }
      .btn-success {
        background-color: #28a745;
      }
      .btn-danger {
        background-color: #dc3545;
      }
      .log {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 10px;
        margin-top: 10px;
        max-height: 200px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
      }
      .log-entry {
        margin-bottom: 5px;
        padding: 2px 5px;
        border-radius: 3px;
      }
      .log-linkage {
        background-color: #d4edda;
        color: #155724;
      }
      .log-calculation {
        background-color: #d1ecf1;
        color: #0c5460;
      }
      .log-error {
        background-color: #f8d7da;
        color: #721c24;
      }
    </style>
  </head>
  <body>
    <h1>表单联动测试 - 订单管理系统</h1>

    <!-- 主表单测试 -->
    <div class="container">
      <div class="test-section">
        <h3>🔧 主表单联动测试</h3>

        <div class="form-group">
          <label>订单类型:</label>
          <select id="type" onchange="handleTypeChange()">
            <option value="">请选择</option>
            <option value="2">采购订单</option>
            <option value="3">销售订单</option>
            <option value="27">服务订单</option>
          </select>
        </div>

        <div class="form-group hidden" id="project_number_group">
          <label>项目ID:</label>
          <select id="project_number" onchange="handleProjectChange()">
            <option value="">请选择项目</option>
            <option
              value="proj_001"
              data-title="ERP系统开发"
              data-client="client_001"
            >
              ERP系统开发项目
            </option>
            <option
              value="proj_002"
              data-title="移动应用开发"
              data-client="client_002"
            >
              移动应用开发项目
            </option>
          </select>
        </div>

        <div class="form-group hidden" id="project_name_group">
          <label>项目名称:</label>
          <input type="text" id="project_name" readonly />
        </div>

        <div class="form-group" id="client_id_group">
          <label>客户:</label>
          <select id="client_id">
            <option value="">请选择客户</option>
            <option value="client_001">阿里巴巴</option>
            <option value="client_002">腾讯科技</option>
          </select>
        </div>

        <div class="form-group">
          <label>货币:</label>
          <select id="currency" onchange="handleCurrencyChange()">
            <option value="人民币">人民币</option>
            <option value="美元">美元</option>
            <option value="欧元">欧元</option>
          </select>
        </div>

        <div class="form-group">
          <label>汇率:</label>
          <select id="exchange_rate" onchange="handleExchangeRateChange()">
            <option value="1.000000" data-name="人民币">人民币-1.000000</option>
            <option value="7.200000" data-name="美元">美元-7.200000</option>
            <option value="7.800000" data-name="欧元">欧元-7.800000</option>
          </select>
        </div>

        <div class="form-group">
          <label>应收金额:</label>
          <input
            type="text"
            id="receivable_left"
            class="calculated"
            readonly
            value="0.00"
          />
        </div>

        <div class="form-group">
          <label>外汇总金额:</label>
          <input
            type="text"
            id="foreign_currency_amount"
            class="calculated"
            readonly
            value="0.00"
          />
        </div>
      </div>
    </div>

    <!-- 产品信息表格测试 -->
    <div class="container">
      <div class="test-section">
        <h3>📋 产品信息表格联动测试</h3>

        <div class="table-container">
          <div class="table-header table-row">
            <div class="table-cell">产品名称</div>
            <div class="table-cell">单位</div>
            <div class="table-cell" id="unit_price_header">单价</div>
            <div class="table-cell hidden" id="foreign_price_header">
              外汇单价
            </div>
            <div class="table-cell">数量</div>
            <div class="table-cell" id="total_amount_header">总价</div>
            <div class="table-cell">操作</div>
          </div>

          <div class="table-row" id="product_row_1">
            <div class="table-cell">
              <input
                type="text"
                value="笔记本电脑"
                onchange="calculateRowTotal(1)"
              />
            </div>
            <div class="table-cell">
              <input type="text" value="台" onchange="calculateRowTotal(1)" />
            </div>
            <div class="table-cell" id="unit_price_cell_1">
              <input
                type="number"
                id="unit_price_1"
                value="5000"
                onchange="calculateRowTotal(1)"
              />
            </div>
            <div class="table-cell hidden" id="foreign_price_cell_1">
              <input
                type="number"
                id="foreign_price_1"
                value="0"
                onchange="calculateRowTotal(1)"
              />
            </div>
            <div class="table-cell">
              <input
                type="number"
                id="quantity_1"
                value="2"
                onchange="calculateRowTotal(1)"
              />
            </div>
            <div class="table-cell" id="total_amount_cell_1">
              <input
                type="text"
                id="total_amount_1"
                class="calculated"
                readonly
                value="10000.00"
              />
            </div>
            <div class="table-cell">
              <button class="btn btn-success" onclick="openEditModal(1)">
                编辑
              </button>
              <button class="btn btn-danger" onclick="deleteRow(1)">
                删除
              </button>
            </div>
          </div>
        </div>

        <button class="btn" onclick="addNewRow()">新增产品</button>
      </div>
    </div>

    <!-- 弹窗表单测试 -->
    <div class="container">
      <div class="test-section">
        <h3>🔧 弹窗表单联动测试</h3>
        <p>模拟编辑产品时的弹窗表单联动效果</p>

        <div class="form-group">
          <label>产品名称:</label>
          <input type="text" id="modal_name" value="笔记本电脑" />
        </div>

        <div class="form-group">
          <label>单位:</label>
          <input type="text" id="modal_unit" value="台" />
        </div>

        <div class="form-group" id="modal_unit_price_group">
          <label>单价:</label>
          <input
            type="number"
            id="modal_unit_price"
            value="5000"
            onchange="calculateModalTotal()"
          />
        </div>

        <div class="form-group hidden" id="modal_foreign_price_group">
          <label>外汇单价:</label>
          <input
            type="number"
            id="modal_foreign_price"
            value="0"
            onchange="calculateModalTotal()"
          />
        </div>

        <div class="form-group">
          <label>数量:</label>
          <input
            type="number"
            id="modal_quantity"
            value="2"
            onchange="calculateModalTotal()"
          />
        </div>

        <div class="form-group" id="modal_total_amount_group">
          <label>总价:</label>
          <input
            type="text"
            id="modal_total_amount"
            class="calculated"
            readonly
            value="10000.00"
          />
        </div>

        <button class="btn btn-success" onclick="saveModalData()">保存</button>
        <button class="btn" onclick="closeModal()">取消</button>
      </div>
    </div>

    <!-- 测试控制面板 -->
    <div class="container">
      <div class="test-section">
        <h3>🧪 测试控制面板</h3>

        <div style="margin-bottom: 15px">
          <h4>基础测试</h4>
          <button class="btn" onclick="runAllTests()">快速测试</button>
          <button class="btn btn-success" onclick="runComprehensiveTest()">
            全面测试
          </button>
          <button class="btn" onclick="resetForm()">重置表单</button>
          <button class="btn" onclick="clearLog()">清空日志</button>
        </div>

        <div style="margin-bottom: 15px">
          <h4>专项测试</h4>
          <button class="btn" onclick="testLinkageAssignment()">
            联动赋值测试
          </button>
          <button class="btn" onclick="testCalculationAccuracy()">
            计算精度测试
          </button>
          <button class="btn" onclick="testModalLinkage()">弹窗联动测试</button>
          <button class="btn" onclick="testComplexScenarios()">
            复杂场景测试
          </button>
        </div>

        <div style="margin-bottom: 15px">
          <h4>测试说明</h4>
          <p style="font-size: 14px; color: #666; margin: 5px 0">
            • <strong>快速测试</strong>: 基本的联动和计算功能测试<br />
            • <strong>全面测试</strong>: 包含所有测试场景的完整测试<br />
            • <strong>联动赋值测试</strong>: 测试选择字段时的自动填充功能<br />
            • <strong>计算精度测试</strong>: 测试各种计算场景的准确性<br />
            • <strong>弹窗联动测试</strong>: 测试弹窗表单与主表单的联动<br />
            • <strong>复杂场景测试</strong>: 测试多个联动规则组合的复杂场景
          </p>
        </div>

        <div class="log" id="testLog">
          <div class="log-entry">测试日志将在这里显示...</div>
        </div>
      </div>
    </div>

    <script>
      // 模拟联动引擎V2
      class MockLinkageEngine {
        constructor() {
          this.rules = [];
          this.formValues = {};
        }

        addRule(rule) {
          this.rules.push(rule);
          this.log(`添加联动规则: ${rule.id} - ${rule.description}`, 'linkage');
        }

        updateValue(field, value) {
          this.formValues[field] = value;
          this.processLinkage(field);
        }

        processLinkage(changedField) {
          this.rules.forEach((rule) => {
            if (this.shouldTriggerRule(rule, changedField)) {
              this.executeAction(rule);
            }
          });
        }

        shouldTriggerRule(rule, changedField) {
          // 检查是否是触发字段
          const conditions = Array.isArray(rule.when) ? rule.when : [rule.when];
          return conditions.some((condition) => {
            if (condition.field === changedField) {
              return this.evaluateCondition(condition);
            }
            return false;
          });
        }

        evaluateCondition(condition) {
          const fieldValue = this.formValues[condition.field];
          const { op, value } = condition;

          switch (op) {
            case 'eq':
              return fieldValue === value;
            case 'ne':
              return fieldValue !== value;
            case 'in':
              return Array.isArray(value) && value.includes(fieldValue);
            default:
              return false;
          }
        }

        executeAction(rule) {
          const { target, action } = rule;
          const element =
            document.getElementById(target) ||
            document.getElementById(target + '_group');

          if (!element) {
            this.log(`目标元素未找到: ${target}`, 'error');
            return;
          }

          switch (action.type) {
            case 'show':
              element.classList.remove('hidden');
              this.log(`显示字段: ${target}`, 'linkage');
              break;
            case 'hide':
              element.classList.add('hidden');
              this.log(`隐藏字段: ${target}`, 'linkage');
              break;
            case 'enable':
              element.classList.remove('disabled');
              this.log(`启用字段: ${target}`, 'linkage');
              break;
            case 'disable':
              element.classList.add('disabled');
              this.log(`禁用字段: ${target}`, 'linkage');
              break;
          }
        }

        log(message, type = 'info') {
          const logElement = document.getElementById('testLog');
          const entry = document.createElement('div');
          entry.className = `log-entry log-${type}`;
          entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
          logElement.appendChild(entry);
          logElement.scrollTop = logElement.scrollHeight;
        }
      }

      // 初始化联动引擎
      const linkageEngine = new MockLinkageEngine();

      // 添加联动规则
      function initializeLinkageRules() {
        // 主表单联动规则
        linkageEngine.addRule({
          id: 'project_visibility_rule',
          target: 'project_number',
          when: [{ field: 'type', op: 'in', value: ['3', '27'] }],
          action: { type: 'show' },
          description: '销售订单和服务订单显示项目字段',
        });

        linkageEngine.addRule({
          id: 'project_name_visibility_rule',
          target: 'project_name',
          when: [{ field: 'type', op: 'in', value: ['3', '27'] }],
          action: { type: 'show' },
          description: '销售订单和服务订单显示项目名称字段',
        });

        linkageEngine.addRule({
          id: 'client_disable_rule',
          target: 'client_id',
          when: [{ field: 'type', op: 'eq', value: '2' }],
          action: { type: 'disable' },
          description: '采购订单禁用客户字段',
        });

        // 表格联动规则
        linkageEngine.addRule({
          id: 'table_foreign_price_visibility',
          target: 'foreign_price_header',
          when: [{ field: 'currency', op: 'ne', value: '人民币' }],
          action: { type: 'show' },
          description: '非人民币时显示外汇单价列',
        });

        // 弹窗表单联动规则
        linkageEngine.addRule({
          id: 'modal_foreign_price_visibility',
          target: 'modal_foreign_price_group',
          when: [{ field: 'currency', op: 'ne', value: '人民币' }],
          action: { type: 'show' },
          description: '非人民币时显示外汇单价字段',
        });
      }

      // 事件处理函数
      function handleTypeChange() {
        const type = document.getElementById('type').value;
        linkageEngine.updateValue('type', type);

        // 重置相关字段
        if (type !== '3' && type !== '27') {
          document.getElementById('project_number').value = '';
          document.getElementById('project_name').value = '';
        }
      }

      function handleProjectChange() {
        const select = document.getElementById('project_number');
        const option = select.options[select.selectedIndex];

        if (option && option.dataset.title) {
          document.getElementById('project_name').value = option.dataset.title;
          document.getElementById('client_id').value =
            option.dataset.client || '';
          linkageEngine.log(
            '联动赋值: 项目选择触发名称和客户自动填充',
            'linkage',
          );
        }
      }

      function handleCurrencyChange() {
        const currency = document.getElementById('currency').value;
        linkageEngine.updateValue('currency', currency);

        // 更新表格列显示
        updateTableColumns();
        // 更新弹窗表单显示
        updateModalFields();
        // 重新计算所有金额
        calculateAllTotals();
      }

      function handleExchangeRateChange() {
        const select = document.getElementById('exchange_rate');
        const option = select.options[select.selectedIndex];

        if (option && option.dataset.name) {
          document.getElementById('currency').value = option.dataset.name;
          linkageEngine.updateValue('currency', option.dataset.name);
          linkageEngine.log('联动赋值: 汇率选择触发货币自动填充', 'linkage');

          // 触发货币变化的联动
          handleCurrencyChange();
        }
      }

      function updateTableColumns() {
        const currency = document.getElementById('currency').value;
        const isRMB = currency === '人民币';

        // 更新表头
        document
          .getElementById('unit_price_header')
          .classList.toggle('hidden', !isRMB);
        document
          .getElementById('foreign_price_header')
          .classList.toggle('hidden', isRMB);
        // 总价列始终显示，只是内容根据货币类型变化

        // 更新数据行
        document
          .getElementById('unit_price_cell_1')
          .classList.toggle('hidden', !isRMB);
        document
          .getElementById('foreign_price_cell_1')
          .classList.toggle('hidden', isRMB);
        // 总价单元格始终显示

        linkageEngine.log(
          `表格列联动: ${isRMB ? '显示人民币' : '显示外汇'}相关列`,
          'linkage',
        );
      }

      function updateModalFields() {
        const currency = document.getElementById('currency').value;
        const isRMB = currency === '人民币';

        document
          .getElementById('modal_unit_price_group')
          .classList.toggle('hidden', !isRMB);
        document
          .getElementById('modal_foreign_price_group')
          .classList.toggle('hidden', isRMB);
        // 总价字段始终显示，只是内容根据货币类型变化

        linkageEngine.log(
          `弹窗表单联动: ${isRMB ? '显示人民币' : '显示外汇'}相关字段`,
          'linkage',
        );
      }

      function calculateRowTotal(rowId) {
        const currency = document.getElementById('currency').value;
        const quantity =
          parseFloat(document.getElementById(`quantity_${rowId}`).value) || 0;

        if (currency === '人民币') {
          const unitPrice =
            parseFloat(document.getElementById(`unit_price_${rowId}`).value) ||
            0;
          const total = unitPrice * quantity;
          document.getElementById(`total_amount_${rowId}`).value =
            total.toFixed(2);
          linkageEngine.log(
            `计算人民币总价: ${unitPrice} × ${quantity} = ${total.toFixed(2)}`,
            'calculation',
          );
        } else {
          const foreignPrice =
            parseFloat(
              document.getElementById(`foreign_price_${rowId}`).value,
            ) || 0;
          const total = foreignPrice * quantity;
          document.getElementById(`total_amount_${rowId}`).value =
            total.toFixed(2);
          linkageEngine.log(
            `计算外汇总价: ${foreignPrice} × ${quantity} = ${total.toFixed(2)}`,
            'calculation',
          );
        }

        // 更新主表单汇总
        updateMainFormTotals();
      }

      function calculateModalTotal() {
        const currency = document.getElementById('currency').value;
        const quantity =
          parseFloat(document.getElementById('modal_quantity').value) || 0;

        let total = 0;
        if (currency === '人民币') {
          const unitPrice =
            parseFloat(document.getElementById('modal_unit_price').value) || 0;
          total = unitPrice * quantity;
          linkageEngine.log(
            `弹窗计算人民币总价: ${unitPrice} × ${quantity} = ${total.toFixed(2)}`,
            'calculation',
          );
        } else {
          const foreignPrice =
            parseFloat(document.getElementById('modal_foreign_price').value) ||
            0;
          total = foreignPrice * quantity;
          linkageEngine.log(
            `弹窗计算外汇总价: ${foreignPrice} × ${quantity} = ${total.toFixed(2)}`,
            'calculation',
          );
        }

        // 统一更新总价字段
        document.getElementById('modal_total_amount').value = total.toFixed(2);
      }

      function calculateAllTotals() {
        calculateRowTotal(1);
        calculateModalTotal();
        updateMainFormTotals();
      }

      function updateMainFormTotals() {
        const currency = document.getElementById('currency').value;
        const exchangeRate =
          parseFloat(document.getElementById('exchange_rate').value) || 1;

        const totalAmount =
          parseFloat(document.getElementById('total_amount_1').value) || 0;

        if (currency === '人民币') {
          document.getElementById('receivable_left').value =
            totalAmount.toFixed(2);
          document.getElementById('foreign_currency_amount').value = '0.00';
          linkageEngine.log(
            `主表单汇总(人民币): 应收金额 = ${totalAmount.toFixed(2)}`,
            'calculation',
          );
        } else {
          const rmbTotal = totalAmount * exchangeRate;
          document.getElementById('receivable_left').value =
            rmbTotal.toFixed(2);
          document.getElementById('foreign_currency_amount').value =
            totalAmount.toFixed(2);
          linkageEngine.log(
            `主表单汇总(外汇): 外汇总额 = ${totalAmount.toFixed(2)}, 应收金额 = ${rmbTotal.toFixed(2)}`,
            'calculation',
          );
        }
      }

      // 测试函数
      function runAllTests() {
        linkageEngine.log('开始运行所有测试...', 'info');

        // 测试1: 订单类型联动
        linkageEngine.log('=== 测试1: 订单类型联动 ===', 'info');
        document.getElementById('type').value = '3';
        handleTypeChange();

        setTimeout(() => {
          // 测试2: 货币联动
          linkageEngine.log('=== 测试2: 货币联动 ===', 'info');
          document.getElementById('currency').value = '美元';
          handleCurrencyChange();

          setTimeout(() => {
            // 测试3: 计算功能
            linkageEngine.log('=== 测试3: 计算功能 ===', 'info');
            document.getElementById('foreign_price_1').value = '1000';
            document.getElementById('quantity_1').value = '3';
            calculateRowTotal(1);

            linkageEngine.log('所有测试完成!', 'info');
          }, 1000);
        }, 1000);
      }

      function clearLog() {
        document.getElementById('testLog').innerHTML =
          '<div class="log-entry">日志已清空...</div>';
      }

      function resetForm() {
        document.getElementById('type').value = '';
        document.getElementById('currency').value = '人民币';
        document.getElementById('exchange_rate').value = '1.000000';

        // 重置所有字段
        handleTypeChange();
        handleCurrencyChange();

        linkageEngine.log('表单已重置', 'info');
      }

      function openEditModal(rowId) {
        linkageEngine.log(`打开编辑弹窗: 产品行 ${rowId}`, 'info');
        // 这里可以添加实际的弹窗逻辑
      }

      function deleteRow(rowId) {
        linkageEngine.log(`删除产品行: ${rowId}`, 'info');
      }

      function addNewRow() {
        linkageEngine.log('新增产品行', 'info');
      }

      function saveModalData() {
        linkageEngine.log('保存弹窗数据', 'info');
      }

      function closeModal() {
        linkageEngine.log('关闭弹窗', 'info');
      }

      // 高级测试场景
      function testComplexScenarios() {
        linkageEngine.log('=== 开始复杂场景测试 ===', 'info');

        // 场景1: 销售订单 + 美元货币
        linkageEngine.log('场景1: 销售订单 + 美元货币', 'info');
        document.getElementById('type').value = '3';
        handleTypeChange();

        setTimeout(() => {
          document.getElementById('project_number').value = 'proj_001';
          handleProjectChange();

          setTimeout(() => {
            document.getElementById('exchange_rate').value = '7.200000';
            handleExchangeRateChange();

            setTimeout(() => {
              // 设置外汇价格并计算
              document.getElementById('foreign_price_1').value = '500';
              document.getElementById('quantity_1').value = '5';
              calculateRowTotal(1);

              linkageEngine.log(
                '场景1测试完成: 应该显示项目字段、外汇相关字段，并正确计算',
                'info',
              );

              // 场景2: 切换回采购订单 + 人民币
              setTimeout(() => {
                linkageEngine.log('场景2: 采购订单 + 人民币', 'info');
                document.getElementById('type').value = '2';
                handleTypeChange();

                setTimeout(() => {
                  document.getElementById('currency').value = '人民币';
                  handleCurrencyChange();

                  setTimeout(() => {
                    document.getElementById('unit_price_1').value = '3000';
                    document.getElementById('quantity_1').value = '4';
                    calculateRowTotal(1);

                    linkageEngine.log(
                      '场景2测试完成: 应该隐藏项目字段、禁用客户字段、显示人民币相关字段',
                      'info',
                    );
                    linkageEngine.log('=== 复杂场景测试完成 ===', 'info');
                  }, 500);
                }, 500);
              }, 1000);
            }, 500);
          }, 500);
        }, 500);
      }

      function testLinkageAssignment() {
        linkageEngine.log('=== 测试联动赋值功能 ===', 'info');

        // 测试项目选择联动赋值
        document.getElementById('type').value = '3';
        handleTypeChange();

        setTimeout(() => {
          linkageEngine.log('选择项目: ERP系统开发项目', 'info');
          document.getElementById('project_number').value = 'proj_001';
          handleProjectChange();

          const projectName = document.getElementById('project_name').value;
          const clientId = document.getElementById('client_id').value;

          if (projectName === 'ERP系统开发' && clientId === 'client_001') {
            linkageEngine.log(
              '✅ 联动赋值成功: 项目名称和客户已自动填充',
              'linkage',
            );
          } else {
            linkageEngine.log(
              '❌ 联动赋值失败: 项目名称或客户未正确填充',
              'error',
            );
          }

          // 测试汇率选择联动赋值
          setTimeout(() => {
            linkageEngine.log('选择汇率: 美元-7.200000', 'info');
            document.getElementById('exchange_rate').value = '7.200000';
            handleExchangeRateChange();

            const currency = document.getElementById('currency').value;
            if (currency === '美元') {
              linkageEngine.log(
                '✅ 汇率联动赋值成功: 货币已自动设置为美元',
                'linkage',
              );
            } else {
              linkageEngine.log('❌ 汇率联动赋值失败: 货币未正确设置', 'error');
            }
          }, 1000);
        }, 500);
      }

      function testCalculationAccuracy() {
        linkageEngine.log('=== 测试计算精度 ===', 'info');

        // 测试人民币计算
        document.getElementById('currency').value = '人民币';
        handleCurrencyChange();

        document.getElementById('unit_price_1').value = '1234.56';
        document.getElementById('quantity_1').value = '3.5';
        calculateRowTotal(1);

        const expectedRMB = (1234.56 * 3.5).toFixed(2);
        const actualRMB = document.getElementById('total_amount_1').value;

        if (actualRMB === expectedRMB) {
          linkageEngine.log(`✅ 人民币计算正确: ${actualRMB}`, 'calculation');
        } else {
          linkageEngine.log(
            `❌ 人民币计算错误: 期望 ${expectedRMB}, 实际 ${actualRMB}`,
            'error',
          );
        }

        // 测试外汇计算
        setTimeout(() => {
          document.getElementById('currency').value = '美元';
          handleCurrencyChange();

          document.getElementById('foreign_price_1').value = '567.89';
          document.getElementById('quantity_1').value = '2.3';
          calculateRowTotal(1);

          const expectedForeign = (567.89 * 2.3).toFixed(2);
          const actualForeign = document.getElementById('total_amount_1').value;

          if (actualForeign === expectedForeign) {
            linkageEngine.log(
              `✅ 外汇计算正确: ${actualForeign}`,
              'calculation',
            );
          } else {
            linkageEngine.log(
              `❌ 外汇计算错误: 期望 ${expectedForeign}, 实际 ${actualForeign}`,
              'error',
            );
          }

          // 测试主表单汇总计算
          setTimeout(() => {
            const exchangeRate = 7.2;
            const foreignTotal = parseFloat(actualForeign);
            const expectedMainTotal = (foreignTotal * exchangeRate).toFixed(2);
            const actualMainTotal =
              document.getElementById('receivable_left').value;

            if (actualMainTotal === expectedMainTotal) {
              linkageEngine.log(
                `✅ 主表单汇总计算正确: ${actualMainTotal}`,
                'calculation',
              );
            } else {
              linkageEngine.log(
                `❌ 主表单汇总计算错误: 期望 ${expectedMainTotal}, 实际 ${actualMainTotal}`,
                'error',
              );
            }
          }, 500);
        }, 1000);
      }

      function testModalLinkage() {
        linkageEngine.log('=== 测试弹窗表单联动 ===', 'info');

        // 设置主表单为美元
        document.getElementById('currency').value = '美元';
        handleCurrencyChange();

        // 检查弹窗表单是否正确联动
        const modalUnitPriceVisible = !document
          .getElementById('modal_unit_price_group')
          .classList.contains('hidden');
        const modalForeignPriceVisible = !document
          .getElementById('modal_foreign_price_group')
          .classList.contains('hidden');
        const modalTotalAmountVisible = !document
          .getElementById('modal_total_amount_group')
          .classList.contains('hidden');
        // 总价字段始终显示，不需要检查

        if (!modalUnitPriceVisible && modalForeignPriceVisible) {
          linkageEngine.log(
            '✅ 弹窗表单联动正确: 美元模式下显示外汇字段',
            'linkage',
          );
        } else {
          linkageEngine.log('❌ 弹窗表单联动错误: 字段显示状态不正确', 'error');
        }

        // 测试弹窗计算
        document.getElementById('modal_foreign_price').value = '100';
        document.getElementById('modal_quantity').value = '5';
        calculateModalTotal();

        const expectedModalTotal = (100 * 5).toFixed(2);
        const actualModalTotal =
          document.getElementById('modal_total_amount').value;

        if (actualModalTotal === expectedModalTotal) {
          linkageEngine.log(
            `✅ 弹窗计算正确: ${actualModalTotal}`,
            'calculation',
          );
        } else {
          linkageEngine.log(
            `❌ 弹窗计算错误: 期望 ${expectedModalTotal}, 实际 ${actualModalTotal}`,
            'error',
          );
        }

        // 切换回人民币测试
        setTimeout(() => {
          document.getElementById('currency').value = '人民币';
          handleCurrencyChange();

          const modalUnitPriceVisible2 = !document
            .getElementById('modal_unit_price_group')
            .classList.contains('hidden');
          const modalForeignPriceVisible2 = !document
            .getElementById('modal_foreign_price_group')
            .classList.contains('hidden');

          if (modalUnitPriceVisible2 && !modalForeignPriceVisible2) {
            linkageEngine.log(
              '✅ 弹窗表单切换联动正确: 人民币模式下显示人民币字段',
              'linkage',
            );
          } else {
            linkageEngine.log(
              '❌ 弹窗表单切换联动错误: 字段显示状态不正确',
              'error',
            );
          }
        }, 1000);
      }

      function runComprehensiveTest() {
        linkageEngine.log('🚀 开始全面测试...', 'info');

        testLinkageAssignment();

        setTimeout(() => {
          testCalculationAccuracy();
        }, 3000);

        setTimeout(() => {
          testModalLinkage();
        }, 6000);

        setTimeout(() => {
          testComplexScenarios();
        }, 9000);

        setTimeout(() => {
          linkageEngine.log('🎉 全面测试完成!', 'info');
          linkageEngine.log('请查看上方日志了解测试结果', 'info');
        }, 15000);
      }

      // 页面加载完成后初始化
      document.addEventListener('DOMContentLoaded', function () {
        initializeLinkageRules();
        linkageEngine.log('联动测试页面初始化完成', 'info');
        linkageEngine.log(
          '请尝试修改订单类型、货币等字段来测试联动效果',
          'info',
        );
        linkageEngine.log('或点击下方按钮运行自动化测试', 'info');
      });
    </script>
  </body>
</html>
