# PHP后端统一配置指南

## 1. 概述

本文档提供PHP后端字段配置的统一指南，包括基础字段配置、计算字段、联动赋值等功能的实现方法。

## 2. 基础字段配置

### 2.1 字段结构

```php
<?php
$field = [
    'field' => 'field_name',
    'title' => '字段标题',
    'type' => 'field_type',
    'config' => [
        // 字段配置
    ],
];
```

### 2.2 常用字段类型

- `text`: 文本输入
- `number`: 数字输入
- `select`: 下拉选择
- `apiSelect`: API数据选择
- `calculated`: 计算字段
- `edittable`: 可编辑表格

## 3. 计算字段配置

### 3.1 基础计算类型

#### 3.1.1 求和计算 (sum)

```php
<?php
$sumField = [
    'field' => 'total_amount',
    'title' => '总金额',
    'type' => 'calculated',
    'config' => [
        'readonly' => true,
        'calculation' => [
            'type' => 'sum',
            'sourceFields' => ['amount1', 'amount2', 'amount3'],
            'triggerFields' => ['amount1', 'amount2', 'amount3'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];
```

#### 3.1.2 乘积计算 (product)

```php
<?php
$productField = [
    'field' => 'total_price',
    'title' => '总价',
    'type' => 'calculated',
    'config' => [
        'readonly' => true,
        'calculation' => [
            'type' => 'product',
            'sourceFields' => ['unit_price', 'quantity'],
            'triggerFields' => ['unit_price', 'quantity'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];
```

#### 3.1.3 表格数据汇总

```php
<?php
$tableSumField = [
    'field' => 'order_total',
    'title' => '订单总额',
    'type' => 'calculated',
    'config' => [
        'readonly' => true,
        'calculation' => [
            'type' => 'sum',
            'tableFields' => ['amount'], // 表格中的字段
            'triggerFields' => ['order_items'], // 表格数据字段
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];
```

### 3.2 条件计算

```php
<?php
$conditionalField = [
    'field' => 'final_amount',
    'title' => '最终金额',
    'type' => 'calculated',
    'config' => [
        'readonly' => true,
        'calculation' => [
            'type' => 'product',
            'sourceFields' => ['base_amount', 'rate'],
            'triggerFields' => ['base_amount', 'rate', 'currency'],
            'precision' => 2,
            'defaultValue' => 0,
            'conditions' => [
                [
                    'condition' => [
                        'field' => 'currency',
                        'operator' => 'equals',
                        'value' => 'USD',
                    ],
                    'calculation' => [
                        'type' => 'product',
                        'sourceFields' => ['base_amount', 'usd_rate'],
                        'precision' => 2,
                        'defaultValue' => 0,
                    ],
                ],
            ],
        ],
    ],
];
```

## 4. 联动赋值配置

### 4.1 基础联动赋值

```php
<?php
$apiSelectField = [
    'field' => 'product_id',
    'title' => '产品',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/products',
        'labelField' => 'name',
        'valueField' => 'id',
        'linkageAssignment' => [
            'targetFields' => [
                [
                    'field' => 'product_name',
                    'valueMapping' => 'name',
                ],
                [
                    'field' => 'unit_price',
                    'valueMapping' => 'price',
                ],
            ],
        ],
    ],
];
```

### 4.2 复杂联动赋值

```php
<?php
$complexLinkageField = [
    'field' => 'customer_id',
    'title' => '客户',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/customers',
        'labelField' => 'name',
        'valueField' => 'id',
        'linkageAssignment' => [
            'targetFields' => [
                [
                    'field' => 'customer_name',
                    'valueMapping' => 'name',
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'customer_address',
                    'valueMapping' => 'address',
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'credit_limit',
                    'valueMapping' => 'credit_limit',
                    'clearOnEmpty' => false,
                ],
            ],
        ],
    ],
];
```

## 5. 计算字段配置错误排查

### 5.1 常见配置错误

#### 5.1.1 计算类型与数据源不匹配

**错误示例**（来自测试数据文件）：
```php
<?php
// 错误的应收金额配置
$receivableField = [
    'field' => 'receivable_left',
    'title' => '应收金额',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            // 默认使用product计算，但sourceFields与业务逻辑不符
            'type' => 'product',
            'sourceFields' => ['foreign_currency_amount', 'exchange_rate'],
            'triggerFields' => ['foreign_currency_amount', 'exchange_rate'],
            'conditions' => [
                [
                    'condition' => [
                        'field' => 'currency',
                        'operator' => 'equals',
                        'value' => '人民币',
                    ],
                    'calculation' => [
                        // 条件内使用sum汇总表格数据，与默认计算类型冲突
                        'type' => 'sum',
                        'tableFields' => ['total_amount'],
                        'triggerFields' => ['item_request'],
                    ],
                ],
            ],
        ],
    ],
];
```

**问题分析**：
1. 默认计算使用 `product` 类型，但条件计算使用 `sum` 类型
2. 默认计算使用 `sourceFields`，条件计算使用 `tableFields`
3. 触发字段不一致，导致计算逻辑混乱

**正确配置**：
```php
<?php
// 正确的应收金额配置
$receivableField = [
    'field' => 'receivable_left',
    'title' => '应收金额',
    'type' => 'calculated',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            // 默认计算：外汇金额 × 汇率
            'type' => 'product',
            'sourceFields' => ['foreign_currency_amount', 'exchange_rate'],
            'triggerFields' => ['foreign_currency_amount', 'exchange_rate', 'currency'],
            'precision' => 2,
            'defaultValue' => 0,
            'conditions' => [
                [
                    'condition' => [
                        'field' => 'currency',
                        'operator' => 'equals',
                        'value' => '人民币',
                    ],
                    'calculation' => [
                        // 人民币情况：直接汇总表格中的人民币金额
                        'type' => 'sum',
                        'tableFields' => ['total_amount'],
                        'triggerFields' => ['item_request'],
                        'precision' => 2,
                        'defaultValue' => 0,
                    ],
                ],
            ],
        ],
    ],
];
```

#### 5.1.2 触发字段配置不当

**错误示例**：
```php
// 触发字段与计算逻辑不匹配
'calculation' => [
    'type' => 'sum',
    'tableFields' => ['amount'],
    'triggerFields' => ['unrelated_field'], // 错误：与tableFields无关
]
```

**正确配置**：
```php
// 触发字段应该包含表格数据字段
'calculation' => [
    'type' => 'sum',
    'tableFields' => ['amount'],
    'triggerFields' => ['order_items'], // 正确：表格数据字段
]
```

#### 5.1.2 计算字段依赖链问题

**问题描述**：
计算字段之间存在依赖关系时，如果被依赖的字段没有正确返回或计算，会导致整个计算链失败。

**典型案例**（来自测试数据文件）：
```php
<?php
// 应收金额字段依赖 foreign_currency_amount
$receivableField = [
    'field' => 'receivable_left',
    'calculation' => [
        'type' => 'product',
        'sourceFields' => ['foreign_currency_amount', 'exchange_rate'], // 依赖计算字段
    ],
];

// foreign_currency_amount 本身也是计算字段
$foreignCurrencyField = [
    'field' => 'foreign_currency_amount',
    'type' => 'calculated',
    'calculation' => [
        'type' => 'sum',
        'tableFields' => ['foreign_currency_total'], // 依赖表格数据
        'triggerFields' => ['item_request'],
    ],
];
```

**问题分析**：
1. `receivable_left` 依赖 `foreign_currency_amount` 字段
2. `foreign_currency_amount` 是计算字段，需要汇总表格数据
3. 如果接口没有返回 `foreign_currency_amount` 的初始值，计算链会断裂
4. 表格数据变化时，需要重新计算整个依赖链

**解决方案**：

**方案一：确保后端返回计算字段初始值**
```php
<?php
// 后端应该返回所有计算字段的当前值
$response = [
    'foreign_currency_amount' => 0, // 必须返回，即使是0
    'receivable_left' => 0,         // 必须返回，即使是0
    'exchange_rate' => 1.0,
    'item_request' => [
        // 表格数据
    ],
];
```

**方案二：重新设计计算逻辑，避免依赖链**
```php
<?php
// 直接计算应收金额，不依赖中间计算字段
$receivableField = [
    'field' => 'receivable_left',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'formula',
            'formula' => 'SUM(foreign_currency_total) * exchange_rate',
            'tableFields' => ['foreign_currency_total'],
            'sourceFields' => ['exchange_rate'],
            'triggerFields' => ['item_request', 'exchange_rate', 'currency'],
            'conditions' => [
                [
                    'condition' => [
                        'field' => 'currency',
                        'operator' => 'equals',
                        'value' => '人民币',
                    ],
                    'calculation' => [
                        'type' => 'sum',
                        'tableFields' => ['total_amount'],
                        'triggerFields' => ['item_request'],
                    ],
                ],
            ],
        ],
    ],
];
```

**方案三：分步计算，确保依赖顺序**
```php
<?php
// 1. 先定义基础汇总字段
$foreignCurrencyField = [
    'field' => 'foreign_currency_amount',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'sum',
            'tableFields' => ['foreign_currency_total'],
            'triggerFields' => ['item_request'],
            'realtime' => true, // 确保实时计算
        ],
    ],
];

// 2. 再定义依赖字段，确保触发顺序
$receivableField = [
    'field' => 'receivable_left',
    'type' => 'calculated',
    'config' => [
        'calculation' => [
            'type' => 'product',
            'sourceFields' => ['foreign_currency_amount', 'exchange_rate'],
            'triggerFields' => [
                'foreign_currency_amount', // 依赖字段
                'exchange_rate',
                'item_request', // 间接依赖
            ],
            'realtime' => false, // 避免过于频繁计算
        ],
    ],
];
```

### 5.2 排查步骤

1. **检查计算字段依赖链**
   - 识别字段之间的依赖关系
   - 确保被依赖的字段先于依赖字段定义
   - 验证接口是否返回所有计算字段的初始值

2. **检查计算类型一致性**
   - 确保默认计算和条件计算使用合适的类型
   - `sourceFields` 用于表单字段计算
   - `tableFields` 用于表格数据汇总

3. **验证数据源配置**
   - `sourceFields` 中的字段必须存在于表单中
   - `tableFields` 中的字段必须存在于表格列中
   - 字段名称必须完全匹配

4. **确认触发字段**
   - 表格汇总计算的 `triggerFields` 应包含表格数据字段
   - 表单字段计算的 `triggerFields` 应包含相关的表单字段
   - 条件字段也应包含在 `triggerFields` 中
   - 依赖字段的 `triggerFields` 应包含被依赖字段

5. **测试计算逻辑**
   - 在不同条件下测试计算结果
   - 检查数据类型转换是否正确
   - 验证精度设置是否合理
   - 测试依赖链的计算顺序

### 5.3 调试技巧

1. **启用实时计算**：
   ```php
   'calculation' => [
       'realtime' => true, // 便于调试
       // ... 其他配置
   ]
   ```

2. **简化计算逻辑**：
   - 先移除 `conditions`，测试默认计算
   - 逐步添加条件，定位问题

3. **检查数据格式**：
   - 确保数值字段返回数字类型
   - 检查表格数据结构是否正确

## 6. editPermission 权限控制

### 6.1 权限类型

- `'both'`: 新增和编辑时都可编辑（默认）
- `'add-only'`: 仅新增时可编辑
- `'edit-only'`: 仅编辑时可编辑
- `'none'`: 始终不可编辑

### 6.2 使用场景

#### 6.2.1 创建时间字段（仅编辑时显示）

```php
<?php
$createdAtField = [
    'field' => 'created_at',
    'title' => '创建时间',
    'type' => 'datetime',
    'config' => [
        'editPermission' => 'edit-only',
        'readonly' => true,
    ],
];
```

#### 6.2.2 订单号字段（仅新增时可编辑）

```php
<?php
$orderNumberField = [
    'field' => 'order_number',
    'title' => '订单号',
    'type' => 'text',
    'config' => [
        'editPermission' => 'add-only',
    ],
];
```

## 7. 最佳实践和注意事项

### 7.1 性能优化建议

- 合理使用 `realtime` 参数，避免过于频繁的计算
- 对于复杂计算，考虑在后端预处理数据
- 使用适当的 `precision` 设置，避免不必要的精度计算

### 7.2 常见问题解决

- **计算结果不准确**：检查 `sourceFields` 和 `tableFields` 的数据类型
- **计算不触发**：确认 `triggerFields` 配置正确
- **性能问题**：减少 `realtime` 使用，优化计算逻辑
- **字段不显示**：检查 `editPermission` 配置和表单模式

### 7.3 配置检查清单

1. ✅ 字段名称是否正确
2. ✅ 计算类型是否与数据源匹配
3. ✅ 触发字段是否包含所有相关字段
4. ✅ 条件逻辑是否正确
5. ✅ 数据类型是否一致
6. ✅ 权限控制是否合理

## 8. 总结

本文档提供了PHP后端字段配置的完整指南，包括基础配置、计算字段、联动赋值和错误排查等内容。正确的配置是确保前端功能正常运行的关键，建议开发者在配置时仔细检查每个参数的正确性。