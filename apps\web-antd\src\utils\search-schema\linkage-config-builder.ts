/**
 * 联动配置构建器
 *
 * 提供简化的、类型安全的联动规则配置接口
 * 降低后端配置复杂度和调试成本
 */

import type {
  LinkageAction,
  SimpleCondition,
  SimpleLinkageRule,
} from './linkage-engine-v2';

// ==================== 配置构建器类型 ====================

/** 条件构建器 */
export interface ConditionBuilder {
  /** 字段等于某值 */
  equals(value: any): ConditionBuilder;
  /** 字段不等于某值 */
  notEquals(value: any): ConditionBuilder;
  /** 字段包含某值 */
  contains(value: any): ConditionBuilder;
  /** 字段为空 */
  isEmpty(): ConditionBuilder;
  /** 字段不为空 */
  isNotEmpty(): ConditionBuilder;
  /** 字段大于某值 */
  greaterThan(value: number): ConditionBuilder;
  /** 字段小于某值 */
  lessThan(value: number): ConditionBuilder;
  /** 字段在某个范围内 */
  inRange(min: number, max: number): ConditionBuilder;
  /** 字段在某个数组中 */
  inArray(values: any[]): ConditionBuilder;
  /** 自定义条件 */
  custom(operator: string, value?: any): ConditionBuilder;
  /** 与条件 */
  and(): ConditionBuilder;
  /** 或条件 */
  or(): ConditionBuilder;
  /** 构建条件 */
  build(): SimpleCondition | SimpleCondition[];
}

/** 动作构建器 */
export interface ActionBuilder {
  /** 显示字段 */
  show(): ActionBuilder;
  /** 隐藏字段 */
  hide(): ActionBuilder;
  /** 启用字段 */
  enable(): ActionBuilder;
  /** 禁用字段 */
  disable(): ActionBuilder;
  /** 设置为必填 */
  required(): ActionBuilder;
  /** 设置为可选 */
  optional(): ActionBuilder;
  /** 设置字段值 */
  setValue(value: any): ActionBuilder;
  /** 设置选项 */
  setOptions(options: Array<{ label: string; value: any }>): ActionBuilder;
  /** 设置组件属性 */
  setProps(props: Record<string, any>): ActionBuilder;
  /** 添加CSS类 */
  addClass(className: string): ActionBuilder;
  /** 移除CSS类 */
  removeClass(className: string): ActionBuilder;
  /** 自定义动作 */
  custom(type: string, payload?: any): ActionBuilder;
  /** 构建动作 */
  build(): LinkageAction | LinkageAction[];
}

/** 规则构建器 */
export interface RuleBuilder {
  /** 设置规则ID */
  id(id: string): RuleBuilder;
  /** 设置目标字段 */
  target(field: string): RuleBuilder;
  /** 设置触发条件 */
  when(field: string): ConditionBuilder;
  /** 设置执行动作 */
  then(): ActionBuilder;
  /** 设置优先级 */
  priority(level: number): RuleBuilder;
  /** 设置描述 */
  description(desc: string): RuleBuilder;
  /** 构建规则 */
  build(): SimpleLinkageRule;
}

// ==================== 具体实现 ====================

class ConditionBuilderImpl implements ConditionBuilder {
  private conditions: SimpleCondition[] = [];
  private currentField: string;
  private logicalOperator: 'and' | 'or' = 'and';

  constructor(field: string) {
    this.currentField = field;
  }

  and(): ConditionBuilder {
    this.logicalOperator = 'and';
    return this;
  }

  build(): SimpleCondition | SimpleCondition[] {
    if (this.conditions.length === 1) {
      return this.conditions[0];
    }
    return this.conditions;
  }

  contains(value: any): ConditionBuilder {
    this.addCondition('contains', value);
    return this;
  }

  custom(operator: string, value?: any): ConditionBuilder {
    this.addCondition(operator, value);
    return this;
  }

  equals(value: any): ConditionBuilder {
    this.addCondition('equals', value);
    return this;
  }

  greaterThan(value: number): ConditionBuilder {
    this.addCondition('greater_than', value);
    return this;
  }

  inArray(values: any[]): ConditionBuilder {
    this.addCondition('in_array', values);
    return this;
  }

  inRange(min: number, max: number): ConditionBuilder {
    this.addCondition('in_range', [min, max]);
    return this;
  }

  isEmpty(): ConditionBuilder {
    this.addCondition('is_empty');
    return this;
  }

  isNotEmpty(): ConditionBuilder {
    this.addCondition('is_not_empty');
    return this;
  }

  lessThan(value: number): ConditionBuilder {
    this.addCondition('less_than', value);
    return this;
  }

  notEquals(value: any): ConditionBuilder {
    this.addCondition('not_equals', value);
    return this;
  }

  or(): ConditionBuilder {
    this.logicalOperator = 'or';
    return this;
  }

  private addCondition(operator: string, value?: any): void {
    this.conditions.push({
      field: this.currentField,
      operator,
      value,
    });
  }
}

class ActionBuilderImpl implements ActionBuilder {
  private actions: LinkageAction[] = [];

  addClass(className: string): ActionBuilder {
    this.actions.push({ type: 'addClass', payload: className });
    return this;
  }

  build(): LinkageAction | LinkageAction[] {
    if (this.actions.length === 1) {
      return this.actions[0];
    }
    return this.actions;
  }

  custom(type: string, payload?: any): ActionBuilder {
    this.actions.push({ type, payload });
    return this;
  }

  disable(): ActionBuilder {
    this.actions.push({ type: 'disable' });
    return this;
  }

  enable(): ActionBuilder {
    this.actions.push({ type: 'enable' });
    return this;
  }

  hide(): ActionBuilder {
    this.actions.push({ type: 'hide' });
    return this;
  }

  optional(): ActionBuilder {
    this.actions.push({ type: 'required', payload: false });
    return this;
  }

  removeClass(className: string): ActionBuilder {
    this.actions.push({ type: 'removeClass', payload: className });
    return this;
  }

  required(): ActionBuilder {
    this.actions.push({ type: 'required', payload: true });
    return this;
  }

  setOptions(options: Array<{ label: string; value: any }>): ActionBuilder {
    this.actions.push({ type: 'setOptions', payload: options });
    return this;
  }

  setProps(props: Record<string, any>): ActionBuilder {
    this.actions.push({ type: 'setProps', payload: props });
    return this;
  }

  setValue(value: any): ActionBuilder {
    this.actions.push({ type: 'setValue', payload: value });
    return this;
  }

  show(): ActionBuilder {
    this.actions.push({ type: 'show' });
    return this;
  }
}

class RuleBuilderImpl implements RuleBuilder {
  private actionBuilder?: ActionBuilder;
  private conditionBuilder?: ConditionBuilder;
  private rule: Partial<SimpleLinkageRule> = {};

  build(): SimpleLinkageRule {
    if (this.actionBuilder) {
      this.rule.then = this.actionBuilder.build();
    }

    if (
      !this.rule.id ||
      !this.rule.target ||
      !this.rule.when ||
      !this.rule.then
    ) {
      throw new Error('规则构建不完整，缺少必要字段');
    }

    return this.rule as SimpleLinkageRule;
  }

  description(desc: string): RuleBuilder {
    this.rule.description = desc;
    return this;
  }

  id(id: string): RuleBuilder {
    this.rule.id = id;
    return this;
  }

  priority(level: number): RuleBuilder {
    this.rule.priority = level;
    return this;
  }

  target(field: string): RuleBuilder {
    this.rule.target = field;
    return this;
  }

  then(): ActionBuilder {
    if (this.conditionBuilder) {
      this.rule.when = this.conditionBuilder.build();
    }
    this.actionBuilder = new ActionBuilderImpl();
    return this.actionBuilder;
  }

  when(field: string): ConditionBuilder {
    this.conditionBuilder = new ConditionBuilderImpl(field);
    return this.conditionBuilder;
  }
}

// ==================== 配置构建器 ====================

/**
 * 联动配置构建器
 */
export class LinkageConfigBuilder {
  private metadata: Record<string, any> = {};
  private rules: SimpleLinkageRule[] = [];

  /**
   * 添加规则
   */
  addRule(rule: SimpleLinkageRule): LinkageConfigBuilder {
    this.rules.push(rule);
    return this;
  }

  /**
   * 批量添加规则
   */
  addRules(rules: SimpleLinkageRule[]): LinkageConfigBuilder {
    this.rules.push(...rules);
    return this;
  }

  /**
   * 构建配置
   */
  build(): {
    metadata: Record<string, any>;
    rules: SimpleLinkageRule[];
  } {
    return {
      rules: [...this.rules],
      metadata: { ...this.metadata },
    };
  }

  /**
   * 创建新规则
   */
  rule(): RuleBuilder {
    return new RuleBuilderImpl();
  }

  /**
   * 设置元数据
   */
  setMetadata(key: string, value: any): LinkageConfigBuilder {
    this.metadata[key] = value;
    return this;
  }

  /**
   * 验证配置
   */
  validate(): {
    errors: string[];
    isValid: boolean;
  } {
    const errors: string[] = [];

    // 检查规则ID唯一性
    const ruleIds = new Set<string>();
    this.rules.forEach((rule, index) => {
      if (ruleIds.has(rule.id)) {
        errors.push(`规则ID重复: ${rule.id}`);
      }
      ruleIds.add(rule.id);

      // 检查必要字段
      if (!rule.target) {
        errors.push(`规则 ${rule.id} 缺少目标字段`);
      }
      if (!rule.when) {
        errors.push(`规则 ${rule.id} 缺少触发条件`);
      }
      if (!rule.then) {
        errors.push(`规则 ${rule.id} 缺少执行动作`);
      }
    });

    // 检查循环依赖
    const dependencies = this.analyzeDependencies();
    if (dependencies.hasCycle) {
      errors.push('检测到循环依赖');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 分析依赖关系
   */
  private analyzeDependencies(): {
    dependencies: Map<string, string[]>;
    hasCycle: boolean;
  } {
    const dependencies = new Map<string, string[]>();

    // 构建依赖图
    this.rules.forEach((rule) => {
      const conditions = Array.isArray(rule.when) ? rule.when : [rule.when];
      const sourceFields = conditions.map((c) => c.field);
      dependencies.set(rule.target, sourceFields);
    });

    // 检测循环依赖（简单实现）
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (field: string): boolean => {
      if (recursionStack.has(field)) {
        return true;
      }
      if (visited.has(field)) {
        return false;
      }

      visited.add(field);
      recursionStack.add(field);

      const deps = dependencies.get(field) || [];
      for (const dep of deps) {
        if (hasCycle(dep)) {
          return true;
        }
      }

      recursionStack.delete(field);
      return false;
    };

    let cycleDetected = false;
    for (const field of dependencies.keys()) {
      if (hasCycle(field)) {
        cycleDetected = true;
        break;
      }
    }

    return {
      hasCycle: cycleDetected,
      dependencies,
    };
  }
}

// ==================== 预设规则模板 ====================

/**
 * 常用规则模板
 */
export const LinkageTemplates = {
  /**
   * 显示/隐藏模板
   */
  showHide(config: {
    description?: string;
    id: string;
    showWhen: any;
    target: string;
    triggerField: string;
  }): SimpleLinkageRule {
    return new LinkageConfigBuilder()
      .rule()
      .id(config.id)
      .target(config.target)
      .when(config.triggerField)
      .equals(config.showWhen)
      .then()
      .show()
      .description(
        config.description ||
          `当 ${config.triggerField} 等于 ${config.showWhen} 时显示 ${config.target}`,
      )
      .build();
  },

  /**
   * 启用/禁用模板
   */
  enableDisable(config: {
    description?: string;
    enableWhen: any;
    id: string;
    target: string;
    triggerField: string;
  }): SimpleLinkageRule {
    return new LinkageConfigBuilder()
      .rule()
      .id(config.id)
      .target(config.target)
      .when(config.triggerField)
      .equals(config.enableWhen)
      .then()
      .enable()
      .description(
        config.description ||
          `当 ${config.triggerField} 等于 ${config.enableWhen} 时启用 ${config.target}`,
      )
      .build();
  },

  /**
   * 必填/可选模板
   */
  requiredOptional(config: {
    description?: string;
    id: string;
    requiredWhen: any;
    target: string;
    triggerField: string;
  }): SimpleLinkageRule {
    return new LinkageConfigBuilder()
      .rule()
      .id(config.id)
      .target(config.target)
      .when(config.triggerField)
      .equals(config.requiredWhen)
      .then()
      .required()
      .description(
        config.description ||
          `当 ${config.triggerField} 等于 ${config.requiredWhen} 时 ${config.target} 为必填`,
      )
      .build();
  },

  /**
   * 选项联动模板
   */
  optionsLinkage(config: {
    description?: string;
    id: string;
    optionsMap: Record<any, Array<{ label: string; value: any }>>;
    target: string;
    triggerField: string;
  }): SimpleLinkageRule[] {
    return Object.entries(config.optionsMap).map(([triggerValue, options]) => {
      return new LinkageConfigBuilder()
        .rule()
        .id(`${config.id}_${triggerValue}`)
        .target(config.target)
        .when(config.triggerField)
        .equals(triggerValue)
        .then()
        .setOptions(options)
        .description(
          config.description ||
            `当 ${config.triggerField} 等于 ${triggerValue} 时更新 ${config.target} 选项`,
        )
        .build();
    });
  },

  /**
   * 值计算模板
   */
  valueCalculation(config: {
    calculator: (values: Record<string, any>) => any;
    description?: string;
    id: string;
    target: string;
    triggerFields: string[];
  }): SimpleLinkageRule {
    return {
      id: config.id,
      target: config.target,
      when: config.triggerFields.map((field) => ({
        field,
        operator: 'is_not_empty',
      })),
      then: {
        type: 'calculate',
        payload: config.calculator,
      },
      description:
        config.description ||
        `根据 ${config.triggerFields.join(', ')} 计算 ${config.target} 的值`,
    };
  },
};

// ==================== 导出工具函数 ====================

/**
 * 创建配置构建器
 */
export function createLinkageConfig(): LinkageConfigBuilder {
  return new LinkageConfigBuilder();
}

/**
 * 从JSON配置创建规则
 */
export function fromJsonConfig(jsonConfig: any): SimpleLinkageRule[] {
  // 这里可以实现从JSON配置转换为规则的逻辑
  // 用于兼容现有的后端配置格式
  return [];
}

/**
 * 导出为JSON配置
 */
export function toJsonConfig(rules: SimpleLinkageRule[]): any {
  // 这里可以实现将规则转换为JSON配置的逻辑
  // 用于与后端交互
  return {
    version: '2.0',
    rules: rules.map((rule) => ({
      id: rule.id,
      target: rule.target,
      when: rule.when,
      then: rule.then,
      priority: rule.priority,
      description: rule.description,
    })),
  };
}
