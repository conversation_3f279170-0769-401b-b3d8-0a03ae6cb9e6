// Excel导入组件相关类型定义

/**
 * Excel导入组件属性接口
 */
export interface ExcelImportProps {
  // 允许的文件类型
  accept?: string;
  // 最大文件大小（MB）
  maxSize?: number;
  // 表格列配置
  columns?: any[];
  // 数据验证函数
  validateData?: (data: any[]) => ValidationResult;
  // 数据转换函数
  transformData?: (data: any[]) => any[];
}

/**
 * 数据验证结果接口
 */
export interface ValidationResult {
  // 是否验证通过
  valid: boolean;
  // 错误信息列表
  errors: string[];
}

/**
 * 导入结果接口
 */
export interface ImportResult {
  // 是否成功
  success: boolean;
  // 结果消息
  message: string;
  // 成功导入的数据条数
  successCount: number;
  // 失败的数据条数
  errorCount: number;
  // 错误信息列表
  errors: string[];
}

/**
 * Excel工作表数据接口
 */
export interface ExcelSheetData {
  // 工作表名称
  sheetName: string;
  // 原始数据（二维数组）
  rawData: any[][];
  // 表头
  headers: string[];
  // 数据行
  dataRows: any[][];
}

/**
 * Excel导入步骤枚举
 */
export enum ImportStep {
  // 数据预览
  DATA_PREVIEW = 1,
  // 文件选择
  FILE_SELECTION = 0,
  // 导入完成
  IMPORT_COMPLETE = 2,
}

/**
 * Excel导入组件事件接口
 */
export interface ExcelImportEvents {
  // 导入成功事件
  success: (data: any[]) => void;
  // 导入失败事件
  error: (error: string) => void;
  // 步骤变化事件
  stepChange: (step: ImportStep) => void;
  // 文件选择事件
  fileSelect: (file: File) => void;
  // 工作表切换事件
  sheetChange: (sheetName: string) => void;
}

/**
 * Excel导入组件实例接口
 */
export interface ExcelImportInstance {
  // 打开弹窗
  open: () => void;
  // 关闭弹窗
  close: () => void;
  // Modal API
  modalApi: any;
  // 重置状态
  reset: () => void;
  // 获取当前数据
  getData: () => any[];
  // 获取当前步骤
  getCurrentStep: () => ImportStep;
}

/**
 * 默认的数据验证函数
 */
export const defaultValidateData = (data: any[]): ValidationResult => {
  const errors: string[] = [];

  if (!Array.isArray(data)) {
    errors.push('数据格式错误：不是有效的数组');
    return { valid: false, errors };
  }

  if (data.length === 0) {
    errors.push('没有可导入的数据');
    return { valid: false, errors };
  }

  // 检查每行数据
  data.forEach((row, index) => {
    if (!row || typeof row !== 'object') {
      errors.push(`第 ${index + 1} 行数据格式错误`);
    }
  });

  return {
    valid: errors.length === 0,
    errors,
  };
};

/**
 * 默认的数据转换函数
 */
export const defaultTransformData = (data: any[]): any[] => {
  return data.map((row, index) => ({
    ...row,
    _index: index + 1,
    _id: `import_${Date.now()}_${index}`,
  }));
};
