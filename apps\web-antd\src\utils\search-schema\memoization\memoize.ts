/**
 * Memoization优化机制
 * 缓存函数调用结果，避免重复计算
 */

/**
 * Memoization配置接口
 */
interface MemoizeOptions {
  maxSize?: number;           // 最大缓存条目数
  ttl?: number;              // 缓存过期时间（毫秒）
  keyGenerator?: (...args: any[]) => string; // 自定义键生成器
  enableStats?: boolean;     // 是否启用统计
}

/**
 * 缓存条目接口
 */
interface CacheEntry<T> {
  value: T;
  timestamp: number;
  accessCount: number;
}

/**
 * Memoization统计信息
 */
interface MemoizeStats {
  hitCount: number;
  missCount: number;
  hitRate: number;
  cacheSize: number;
  totalCalls: number;
}

/**
 * WeakMap基础的Memoization
 * 适用于对象参数的函数，自动垃圾回收
 */
export function memoizeWeak<T extends (...args: any[]) => any>(
  fn: T,
  options: Omit<MemoizeOptions, 'maxSize' | 'ttl'> = {},
): T & { clearCache: () => void; getStats: () => MemoizeStats } {
  const cache = new WeakMap<object, any>();
  let stats: MemoizeStats = {
    hitCount: 0,
    missCount: 0,
    hitRate: 0,
    cacheSize: 0,
    totalCalls: 0,
  };

  const memoized = ((...args: any[]) => {
    stats.totalCalls++;
    
    // 只有当第一个参数是对象时才使用WeakMap缓存
    if (args.length > 0 && typeof args[0] === 'object' && args[0] !== null) {
      const key = args[0];
      
      if (cache.has(key)) {
        stats.hitCount++;
        stats.hitRate = (stats.hitCount / stats.totalCalls) * 100;
        return cache.get(key);
      }
      
      const result = fn(...args);
      cache.set(key, result);
      stats.missCount++;
      stats.hitRate = (stats.hitCount / stats.totalCalls) * 100;
      return result;
    }
    
    // 如果第一个参数不是对象，直接调用原函数
    stats.missCount++;
    stats.hitRate = (stats.hitCount / stats.totalCalls) * 100;
    return fn(...args);
  }) as T & { clearCache: () => void; getStats: () => MemoizeStats };

  memoized.clearCache = () => {
    // WeakMap没有clear方法，只能重新创建
    // cache = new WeakMap(); // 这里不能重新赋值，因为cache是const
    stats = {
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      cacheSize: 0,
      totalCalls: 0,
    };
  };

  memoized.getStats = () => ({ ...stats });

  return memoized;
}

/**
 * Map基础的Memoization
 * 适用于基本类型参数的函数，支持TTL和大小限制
 */
export function memoize<T extends (...args: any[]) => any>(
  fn: T,
  options: MemoizeOptions = {},
): T & { clearCache: () => void; getStats: () => MemoizeStats } {
  const {
    maxSize = 100,
    ttl = 5 * 60 * 1000, // 5分钟
    keyGenerator = (...args) => JSON.stringify(args),
    enableStats = true,
  } = options;

  const cache = new Map<string, CacheEntry<any>>();
  let stats: MemoizeStats = {
    hitCount: 0,
    missCount: 0,
    hitRate: 0,
    cacheSize: 0,
    totalCalls: 0,
  };

  const memoized = ((...args: any[]) => {
    if (enableStats) {
      stats.totalCalls++;
    }
    
    let key: string;
    try {
      key = keyGenerator(...args);
    } catch (error) {
      // 如果键生成失败，直接调用原函数
      if (enableStats) {
        stats.missCount++;
        stats.hitRate = (stats.hitCount / stats.totalCalls) * 100;
      }
      return fn(...args);
    }

    // 检查缓存
    const cached = cache.get(key);
    if (cached) {
      // 检查是否过期
      if (Date.now() - cached.timestamp <= ttl) {
        cached.accessCount++;
        if (enableStats) {
          stats.hitCount++;
          stats.hitRate = (stats.hitCount / stats.totalCalls) * 100;
        }
        return cached.value;
      } else {
        // 过期，删除缓存
        cache.delete(key);
      }
    }

    // 缓存未命中，调用原函数
    const result = fn(...args);
    
    // 如果缓存已满，删除最旧的条目（LRU策略）
    if (cache.size >= maxSize) {
      const firstKey = cache.keys().next().value;
      if (firstKey) {
        cache.delete(firstKey);
      }
    }

    // 添加到缓存
    cache.set(key, {
      value: result,
      timestamp: Date.now(),
      accessCount: 1,
    });

    if (enableStats) {
      stats.missCount++;
      stats.cacheSize = cache.size;
      stats.hitRate = (stats.hitCount / stats.totalCalls) * 100;
    }

    return result;
  }) as T & { clearCache: () => void; getStats: () => MemoizeStats };

  memoized.clearCache = () => {
    cache.clear();
    if (enableStats) {
      stats = {
        hitCount: 0,
        missCount: 0,
        hitRate: 0,
        cacheSize: 0,
        totalCalls: 0,
      };
    }
  };

  memoized.getStats = () => ({ ...stats, cacheSize: cache.size });

  return memoized;
}

/**
 * 异步函数的Memoization
 * 支持Promise结果的缓存
 */
export function memoizeAsync<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: MemoizeOptions = {},
): T & { clearCache: () => void; getStats: () => MemoizeStats } {
  const {
    maxSize = 50,
    ttl = 5 * 60 * 1000,
    keyGenerator = (...args) => JSON.stringify(args),
    enableStats = true,
  } = options;

  const cache = new Map<string, CacheEntry<Promise<any>>>();
  const pendingRequests = new Map<string, Promise<any>>();
  let stats: MemoizeStats = {
    hitCount: 0,
    missCount: 0,
    hitRate: 0,
    cacheSize: 0,
    totalCalls: 0,
  };

  const memoized = (async (...args: any[]) => {
    if (enableStats) {
      stats.totalCalls++;
    }
    
    let key: string;
    try {
      key = keyGenerator(...args);
    } catch (error) {
      if (enableStats) {
        stats.missCount++;
        stats.hitRate = (stats.hitCount / stats.totalCalls) * 100;
      }
      return fn(...args);
    }

    // 检查缓存
    const cached = cache.get(key);
    if (cached) {
      if (Date.now() - cached.timestamp <= ttl) {
        cached.accessCount++;
        if (enableStats) {
          stats.hitCount++;
          stats.hitRate = (stats.hitCount / stats.totalCalls) * 100;
        }
        return cached.value;
      } else {
        cache.delete(key);
      }
    }

    // 检查是否有正在进行的请求
    const pending = pendingRequests.get(key);
    if (pending) {
      if (enableStats) {
        stats.hitCount++;
        stats.hitRate = (stats.hitCount / stats.totalCalls) * 100;
      }
      return pending;
    }

    // 创建新的Promise
    const promise = fn(...args);
    pendingRequests.set(key, promise);

    try {
      const result = await promise;
      
      // 如果缓存已满，删除最旧的条目
      if (cache.size >= maxSize) {
        const firstKey = cache.keys().next().value;
        if (firstKey) {
          cache.delete(firstKey);
        }
      }

      // 添加到缓存
      cache.set(key, {
        value: Promise.resolve(result),
        timestamp: Date.now(),
        accessCount: 1,
      });

      if (enableStats) {
        stats.missCount++;
        stats.cacheSize = cache.size;
        stats.hitRate = (stats.hitCount / stats.totalCalls) * 100;
      }

      return result;
    } catch (error) {
      // 如果请求失败，不缓存结果
      if (enableStats) {
        stats.missCount++;
        stats.hitRate = (stats.hitCount / stats.totalCalls) * 100;
      }
      throw error;
    } finally {
      // 清除正在进行的请求
      pendingRequests.delete(key);
    }
  }) as T & { clearCache: () => void; getStats: () => MemoizeStats };

  memoized.clearCache = () => {
    cache.clear();
    pendingRequests.clear();
    if (enableStats) {
      stats = {
        hitCount: 0,
        missCount: 0,
        hitRate: 0,
        cacheSize: 0,
        totalCalls: 0,
      };
    }
  };

  memoized.getStats = () => ({ ...stats, cacheSize: cache.size });

  return memoized;
}

/**
 * 基于时间的Memoization
 * 在指定时间内返回相同结果，适用于时间敏感的函数
 */
export function memoizeTime<T extends (...args: any[]) => any>(
  fn: T,
  timeWindow = 1000, // 1秒
): T & { clearCache: () => void; getStats: () => MemoizeStats } {
  let lastResult: any;
  let lastCallTime = 0;
  let stats: MemoizeStats = {
    hitCount: 0,
    missCount: 0,
    hitRate: 0,
    cacheSize: 0,
    totalCalls: 0,
  };

  const memoized = ((...args: any[]) => {
    stats.totalCalls++;
    const now = Date.now();
    
    if (now - lastCallTime < timeWindow) {
      stats.hitCount++;
      stats.hitRate = (stats.hitCount / stats.totalCalls) * 100;
      return lastResult;
    }
    
    lastResult = fn(...args);
    lastCallTime = now;
    stats.missCount++;
    stats.hitRate = (stats.hitCount / stats.totalCalls) * 100;
    
    return lastResult;
  }) as T & { clearCache: () => void; getStats: () => MemoizeStats };

  memoized.clearCache = () => {
    lastResult = undefined;
    lastCallTime = 0;
    stats = {
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      cacheSize: 0,
      totalCalls: 0,
    };
  };

  memoized.getStats = () => ({ ...stats });

  return memoized;
}

/**
 * Memoization管理器
 * 统一管理所有memoized函数
 */
export class MemoizeManager {
  private static instance: MemoizeManager;
  private memoizedFunctions = new Map<string, any>();

  private constructor() {}

  static getInstance(): MemoizeManager {
    if (!MemoizeManager.instance) {
      MemoizeManager.instance = new MemoizeManager();
    }
    return MemoizeManager.instance;
  }

  /**
   * 注册memoized函数
   */
  register(name: string, memoizedFn: any): void {
    this.memoizedFunctions.set(name, memoizedFn);
  }

  /**
   * 获取memoized函数
   */
  get(name: string): any {
    return this.memoizedFunctions.get(name);
  }

  /**
   * 清除指定函数的缓存
   */
  clearCache(name: string): void {
    const fn = this.memoizedFunctions.get(name);
    if (fn && typeof fn.clearCache === 'function') {
      fn.clearCache();
    }
  }

  /**
   * 清除所有缓存
   */
  clearAllCaches(): void {
    for (const fn of this.memoizedFunctions.values()) {
      if (typeof fn.clearCache === 'function') {
        fn.clearCache();
      }
    }
  }

  /**
   * 获取所有函数的统计信息
   */
  getAllStats(): Record<string, MemoizeStats> {
    const stats: Record<string, MemoizeStats> = {};
    for (const [name, fn] of this.memoizedFunctions.entries()) {
      if (typeof fn.getStats === 'function') {
        stats[name] = fn.getStats();
      }
    }
    return stats;
  }

  /**
   * 获取总体统计信息
   */
  getTotalStats(): MemoizeStats {
    const allStats = this.getAllStats();
    const totalStats: MemoizeStats = {
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      cacheSize: 0,
      totalCalls: 0,
    };

    for (const stats of Object.values(allStats)) {
      totalStats.hitCount += stats.hitCount;
      totalStats.missCount += stats.missCount;
      totalStats.cacheSize += stats.cacheSize;
      totalStats.totalCalls += stats.totalCalls;
    }

    if (totalStats.totalCalls > 0) {
      totalStats.hitRate = (totalStats.hitCount / totalStats.totalCalls) * 100;
    }

    return totalStats;
  }
}

// 导出全局管理器实例
export const memoizeManager = MemoizeManager.getInstance();

/**
 * 便捷函数：创建并注册memoized函数
 */
export function createMemoized<T extends (...args: any[]) => any>(
  name: string,
  fn: T,
  options?: MemoizeOptions,
): T & { clearCache: () => void; getStats: () => MemoizeStats } {
  const memoizedFn = memoize(fn, options);
  memoizeManager.register(name, memoizedFn);
  return memoizedFn;
}

/**
 * 便捷函数：创建并注册异步memoized函数
 */
export function createMemoizedAsync<T extends (...args: any[]) => Promise<any>>(
  name: string,
  fn: T,
  options?: MemoizeOptions,
): T & { clearCache: () => void; getStats: () => MemoizeStats } {
  const memoizedFn = memoizeAsync(fn, options);
  memoizeManager.register(name, memoizedFn);
  return memoizedFn;
}