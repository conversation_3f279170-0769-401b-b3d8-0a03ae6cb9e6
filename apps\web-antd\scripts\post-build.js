import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 确保 version.json 文件存在于 dist 目录
const distPath = path.resolve(__dirname, '../dist');
const versionPath = path.join(distPath, 'version.json');

if (!fs.existsSync(versionPath)) {
  const versionInfo = {
    version: process.env.npm_package_version || '1.0.0',
    buildTime: Date.now(),
    timestamp: new Date().toISOString()
  };
  
  fs.writeFileSync(versionPath, JSON.stringify(versionInfo, null, 2));
  console.log('✅ version.json 文件已生成');
} else {
  console.log('✅ version.json 文件已存在');
}