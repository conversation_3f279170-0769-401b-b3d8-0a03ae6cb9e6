/**
 * 联动引擎V2集成示例
 * 本文件展示了如何将联动引擎V2集成到不同的表单系统中
 */

import { LinkageEngineV2 } from './linkage-engine-v2';
import { RuleBuilder } from './linkage-config-builder';
import { SimpleLinkageRule } from './linkage-engine-v2';
import { ref, watch, onUnmounted } from 'vue';
import { FormSchema } from '@/components/Form';

// ======== 1. Vben表单集成 ========

/**
 * Vben表单集成示例
 * 展示如何在Vben表单中使用联动引擎V2
 */
export function useVbenFormLinkage(schemas: FormSchema[]) {
  const engine = new LinkageEngineV2();
  const formModel = ref<Record<string, any>>({});
  const fieldStates = ref<Record<string, any>>({});
  
  // 从schema中提取联动规则
  const extractLinkageRules = (schemas: FormSchema[]): SimpleLinkageRule[] => {
    const rules: SimpleLinkageRule[] = [];
    
    schemas.forEach(schema => {
      if (schema.linkageRules) {
        rules.push(...schema.linkageRules);
      }
      
      // 兼容旧版本的dependencies配置
      if (schema.dependencies) {
        const convertedRules = convertDependenciesToRules(schema.field, schema.dependencies);
        rules.push(...convertedRules);
      }
    });
    
    return rules;
  };
  
  // 转换旧版本dependencies为新规则
  const convertDependenciesToRules = (field: string, dependencies: any): SimpleLinkageRule[] => {
    const rules: SimpleLinkageRule[] = [];
    
    if (dependencies.show) {
      rules.push({
        id: `${field}-show-rule`,
        triggerFields: Object.keys(dependencies.show),
        conditions: Object.entries(dependencies.show).map(([key, value]) => ({
          field: key,
          operator: 'equals',
          value
        })),
        actions: [
          { field, property: 'visible', value: true }
        ]
      });
    }
    
    if (dependencies.required) {
      rules.push({
        id: `${field}-required-rule`,
        triggerFields: Object.keys(dependencies.required),
        conditions: Object.entries(dependencies.required).map(([key, value]) => ({
          field: key,
          operator: 'equals',
          value
        })),
        actions: [
          { field, property: 'required', value: true }
        ]
      });
    }
    
    return rules;
  };
  
  // 初始化联动引擎
  const initEngine = () => {
    const rules = extractLinkageRules(schemas);
    engine.setRules(rules);
    
    // 监听字段状态变化
    engine.onStateChange((states) => {
      fieldStates.value = states;
    });
    
    // 设置初始表单值
    engine.setValues(formModel.value);
  };
  
  // 监听表单值变化
  watch(
    formModel,
    (newValues) => {
      engine.updateValues(newValues);
    },
    { deep: true }
  );
  
  // 获取动态schema
  const getDynamicSchemas = (): FormSchema[] => {
    return schemas.map(schema => {
      const fieldState = fieldStates.value[schema.field] || {};
      
      return {
        ...schema,
        show: fieldState.visible !== false,
        required: fieldState.required === true,
        disabled: fieldState.disabled === true,
        componentProps: {
          ...schema.componentProps,
          ...fieldState.componentProps,
          options: fieldState.options || schema.componentProps?.options
        }
      };
    });
  };
  
  // 清理资源
  onUnmounted(() => {
    engine.destroy();
  });
  
  return {
    formModel,
    fieldStates,
    getDynamicSchemas,
    initEngine,
    engine
  };
}

// ======== 2. Vue3 Composition API集成 ========

/**
 * Vue3 Composition API集成示例
 * 展示如何在Vue3组件中使用联动引擎
 */
export function useFormLinkage(initialValues: Record<string, any> = {}) {
  const engine = new LinkageEngineV2();
  const formData = ref(initialValues);
  const fieldStates = ref<Record<string, any>>({});
  const isLoading = ref(false);
  
  // 设置联动规则
  const setRules = (rules: SimpleLinkageRule[]) => {
    engine.setRules(rules);
    engine.setValues(formData.value);
  };
  
  // 添加单个规则
  const addRule = (rule: SimpleLinkageRule) => {
    const currentRules = engine.getRules();
    engine.setRules([...currentRules, rule]);
  };
  
  // 更新字段值
  const updateField = (field: string, value: any) => {
    formData.value[field] = value;
    engine.updateValues({ [field]: value });
  };
  
  // 批量更新字段值
  const updateFields = (updates: Record<string, any>) => {
    Object.assign(formData.value, updates);
    engine.updateValues(updates);
  };
  
  // 获取字段状态
  const getFieldState = (field: string) => {
    return fieldStates.value[field] || {
      visible: true,
      disabled: false,
      required: false
    };
  };
  
  // 验证表单
  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    Object.keys(formData.value).forEach(field => {
      const state = getFieldState(field);
      const value = formData.value[field];
      
      // 检查必填字段
      if (state.required && (!value || value === '')) {
        errors[field] = '此字段为必填项';
      }
      
      // 检查自定义验证规则
      if (state.validator && typeof state.validator === 'function') {
        const result = state.validator(value, formData.value);
        if (result !== true) {
          errors[field] = result;
        }
      }
    });
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  };
  
  // 重置表单
  const resetForm = () => {
    formData.value = { ...initialValues };
    engine.setValues(formData.value);
  };
  
  // 监听状态变化
  engine.onStateChange((states) => {
    fieldStates.value = states;
  });
  
  // 启用调试模式（开发环境）
  if (process.env.NODE_ENV === 'development') {
    engine.enableDebugging({ level: 'info', logToConsole: true });
  }
  
  // 清理资源
  onUnmounted(() => {
    engine.destroy();
  });
  
  return {
    formData,
    fieldStates,
    isLoading,
    setRules,
    addRule,
    updateField,
    updateFields,
    getFieldState,
    validateForm,
    resetForm,
    engine
  };
}

// ======== 3. React Hook集成 ========

/**
 * React Hook集成示例
 * 展示如何在React中使用联动引擎
 */
export function useReactFormLinkage(initialValues: Record<string, any> = {}) {
  // 注意：这是TypeScript代码，在Vue项目中仅作为参考
  // 实际使用时需要在React项目中实现
  
  /*
  import { useState, useEffect, useCallback, useRef } from 'react';
  
  const [formData, setFormData] = useState(initialValues);
  const [fieldStates, setFieldStates] = useState({});
  const engineRef = useRef(new LinkageEngineV2());
  
  const updateField = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    engineRef.current.updateValues({ [field]: value });
  }, []);
  
  const setRules = useCallback((rules: SimpleLinkageRule[]) => {
    engineRef.current.setRules(rules);
    engineRef.current.setValues(formData);
  }, [formData]);
  
  useEffect(() => {
    const unsubscribe = engineRef.current.onStateChange((states) => {
      setFieldStates(states);
    });
    
    return unsubscribe;
  }, []);
  
  useEffect(() => {
    return () => {
      engineRef.current.destroy();
    };
  }, []);
  
  return {
    formData,
    fieldStates,
    updateField,
    setRules,
    engine: engineRef.current
  };
  */
}

// ======== 4. Element Plus表单集成 ========

/**
 * Element Plus表单集成示例
 * 展示如何在Element Plus表单中使用联动引擎
 */
export function useElementPlusFormLinkage() {
  const engine = new LinkageEngineV2();
  const formRef = ref();
  const formModel = ref<Record<string, any>>({});
  const fieldStates = ref<Record<string, any>>({});
  const formRules = ref<Record<string, any>>({});
  
  // 设置联动规则
  const setLinkageRules = (rules: SimpleLinkageRule[]) => {
    engine.setRules(rules);
    engine.setValues(formModel.value);
  };
  
  // 监听状态变化并更新表单规则
  engine.onStateChange((states) => {
    fieldStates.value = states;
    
    // 动态更新表单验证规则
    const newRules: Record<string, any> = {};
    Object.keys(states).forEach(field => {
      const state = states[field];
      if (state.required) {
        newRules[field] = [
          { required: true, message: `${field}为必填项`, trigger: 'blur' }
        ];
      }
    });
    formRules.value = newRules;
  });
  
  // 监听表单值变化
  watch(
    formModel,
    (newValues) => {
      engine.updateValues(newValues);
    },
    { deep: true }
  );
  
  // 获取字段是否显示
  const isFieldVisible = (field: string) => {
    const state = fieldStates.value[field];
    return state ? state.visible !== false : true;
  };
  
  // 获取字段是否禁用
  const isFieldDisabled = (field: string) => {
    const state = fieldStates.value[field];
    return state ? state.disabled === true : false;
  };
  
  // 获取字段选项
  const getFieldOptions = (field: string) => {
    const state = fieldStates.value[field];
    return state?.options || [];
  };
  
  // 验证表单
  const validateForm = async () => {
    if (!formRef.value) return false;
    
    try {
      await formRef.value.validate();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      return false;
    }
  };
  
  // 清理资源
  onUnmounted(() => {
    engine.destroy();
  });
  
  return {
    formRef,
    formModel,
    formRules,
    fieldStates,
    setLinkageRules,
    isFieldVisible,
    isFieldDisabled,
    getFieldOptions,
    validateForm,
    engine
  };
}

// ======== 5. Ant Design Vue表单集成 ========

/**
 * Ant Design Vue表单集成示例
 * 展示如何在Ant Design Vue表单中使用联动引擎
 */
export function useAntdFormLinkage() {
  const engine = new LinkageEngineV2();
  const formRef = ref();
  const formState = ref<Record<string, any>>({});
  const fieldStates = ref<Record<string, any>>({});
  
  // 设置联动规则
  const setLinkageRules = (rules: SimpleLinkageRule[]) => {
    engine.setRules(rules);
    engine.setValues(formState.value);
  };
  
  // 监听状态变化
  engine.onStateChange((states) => {
    fieldStates.value = states;
  });
  
  // 监听表单值变化
  watch(
    formState,
    (newValues) => {
      engine.updateValues(newValues);
    },
    { deep: true }
  );
  
  // 创建动态表单项配置
  const createFormItemConfig = (field: string) => {
    const state = fieldStates.value[field] || {};
    
    return {
      name: field,
      rules: state.required ? [
        { required: true, message: `请输入${field}` }
      ] : [],
      style: {
        display: state.visible === false ? 'none' : 'block'
      }
    };
  };
  
  // 创建动态组件属性
  const createComponentProps = (field: string, baseProps: any = {}) => {
    const state = fieldStates.value[field] || {};
    
    return {
      ...baseProps,
      disabled: state.disabled === true,
      options: state.options || baseProps.options,
      ...state.componentProps
    };
  };
  
  // 验证表单
  const validateForm = async () => {
    if (!formRef.value) return false;
    
    try {
      await formRef.value.validate();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      return false;
    }
  };
  
  // 重置表单
  const resetForm = () => {
    if (formRef.value) {
      formRef.value.resetFields();
    }
    engine.setValues(formState.value);
  };
  
  // 清理资源
  onUnmounted(() => {
    engine.destroy();
  });
  
  return {
    formRef,
    formState,
    fieldStates,
    setLinkageRules,
    createFormItemConfig,
    createComponentProps,
    validateForm,
    resetForm,
    engine
  };
}

// ======== 6. 表格表单集成 ========

/**
 * 表格表单集成示例
 * 展示如何在表格编辑中使用联动引擎
 */
export function useTableFormLinkage() {
  const engines = ref<Map<string, LinkageEngineV2>>(new Map());
  const tableData = ref<any[]>([]);
  const fieldStates = ref<Record<string, Record<string, any>>>({});
  
  // 为每行创建独立的联动引擎
  const createRowEngine = (rowId: string, rules: SimpleLinkageRule[]) => {
    const engine = new LinkageEngineV2();
    engine.setRules(rules);
    
    // 监听该行的状态变化
    engine.onStateChange((states) => {
      fieldStates.value[rowId] = states;
    });
    
    engines.value.set(rowId, engine);
    return engine;
  };
  
  // 更新行数据
  const updateRowData = (rowId: string, field: string, value: any) => {
    const engine = engines.value.get(rowId);
    if (engine) {
      engine.updateValues({ [field]: value });
    }
    
    // 更新表格数据
    const rowIndex = tableData.value.findIndex(row => row.id === rowId);
    if (rowIndex !== -1) {
      tableData.value[rowIndex][field] = value;
    }
  };
  
  // 添加新行
  const addRow = (rowData: any, rules: SimpleLinkageRule[]) => {
    const rowId = rowData.id || `row_${Date.now()}`;
    tableData.value.push({ ...rowData, id: rowId });
    
    const engine = createRowEngine(rowId, rules);
    engine.setValues(rowData);
  };
  
  // 删除行
  const removeRow = (rowId: string) => {
    const engine = engines.value.get(rowId);
    if (engine) {
      engine.destroy();
      engines.value.delete(rowId);
    }
    
    delete fieldStates.value[rowId];
    
    const rowIndex = tableData.value.findIndex(row => row.id === rowId);
    if (rowIndex !== -1) {
      tableData.value.splice(rowIndex, 1);
    }
  };
  
  // 获取行字段状态
  const getRowFieldState = (rowId: string, field: string) => {
    const rowStates = fieldStates.value[rowId];
    return rowStates?.[field] || {
      visible: true,
      disabled: false,
      required: false
    };
  };
  
  // 批量计算（如合计）
  const calculateSummary = (field: string, calculator: (values: any[]) => any) => {
    const values = tableData.value.map(row => row[field]);
    return calculator(values);
  };
  
  // 清理所有资源
  const cleanup = () => {
    engines.value.forEach(engine => engine.destroy());
    engines.value.clear();
    fieldStates.value = {};
  };
  
  // 清理资源
  onUnmounted(() => {
    cleanup();
  });
  
  return {
    tableData,
    fieldStates,
    createRowEngine,
    updateRowData,
    addRow,
    removeRow,
    getRowFieldState,
    calculateSummary,
    cleanup
  };
}

// ======== 7. 异步数据加载集成 ========

/**
 * 异步数据加载集成示例
 * 展示如何处理异步数据加载的联动
 */
export function useAsyncFormLinkage() {
  const engine = new LinkageEngineV2();
  const formData = ref<Record<string, any>>({});
  const fieldStates = ref<Record<string, any>>({});
  const loadingStates = ref<Record<string, boolean>>({});
  
  // 设置异步数据加载规则
  const setAsyncRules = (rules: SimpleLinkageRule[]) => {
    // 为异步规则添加加载状态处理
    const enhancedRules = rules.map(rule => ({
      ...rule,
      actions: rule.actions.map(action => {
        if (action.asyncLoader) {
          return {
            ...action,
            valueFunction: async (values: Record<string, any>) => {
              const field = action.field;
              loadingStates.value[field] = true;
              
              try {
                const result = await action.asyncLoader!(values);
                return result;
              } catch (error) {
                console.error(`异步加载失败 (${field}):`, error);
                return action.defaultValue || null;
              } finally {
                loadingStates.value[field] = false;
              }
            }
          };
        }
        return action;
      })
    }));
    
    engine.setRules(enhancedRules);
  };
  
  // 监听状态变化
  engine.onStateChange((states) => {
    fieldStates.value = states;
  });
  
  // 监听表单值变化
  watch(
    formData,
    (newValues) => {
      engine.updateValues(newValues);
    },
    { deep: true }
  );
  
  // 获取字段是否正在加载
  const isFieldLoading = (field: string) => {
    return loadingStates.value[field] === true;
  };
  
  // 手动触发异步加载
  const triggerAsyncLoad = async (field: string, loader: (values: any) => Promise<any>) => {
    loadingStates.value[field] = true;
    
    try {
      const result = await loader(formData.value);
      
      // 更新字段状态
      const currentStates = { ...fieldStates.value };
      if (!currentStates[field]) {
        currentStates[field] = {};
      }
      
      if (Array.isArray(result)) {
        currentStates[field].options = result;
      } else {
        Object.assign(currentStates[field], result);
      }
      
      fieldStates.value = currentStates;
    } catch (error) {
      console.error(`手动异步加载失败 (${field}):`, error);
    } finally {
      loadingStates.value[field] = false;
    }
  };
  
  // 清理资源
  onUnmounted(() => {
    engine.destroy();
  });
  
  return {
    formData,
    fieldStates,
    loadingStates,
    setAsyncRules,
    isFieldLoading,
    triggerAsyncLoad,
    engine
  };
}

// ======== 8. 完整的表单组件示例 ========

/**
 * 完整的表单组件示例
 * 展示如何创建一个完整的联动表单组件
 */
export function createLinkageForm(config: {
  fields: Array<{
    name: string;
    label: string;
    component: string;
    props?: any;
    rules?: any[];
  }>;
  linkageRules: SimpleLinkageRule[];
  initialValues?: Record<string, any>;
}) {
  const { fields, linkageRules, initialValues = {} } = config;
  
  return {
    setup() {
      const {
        formData,
        fieldStates,
        updateField,
        validateForm,
        resetForm
      } = useFormLinkage(initialValues);
      
      // 设置联动规则
      const { setRules } = useFormLinkage();
      setRules(linkageRules);
      
      // 渲染字段
      const renderField = (field: any) => {
        const state = fieldStates.value[field.name] || {};
        
        if (state.visible === false) {
          return null;
        }
        
        const props = {
          ...field.props,
          ...state.componentProps,
          disabled: state.disabled,
          options: state.options || field.props?.options,
          value: formData.value[field.name],
          'onUpdate:value': (value: any) => updateField(field.name, value)
        };
        
        return h(field.component, props);
      };
      
      return {
        formData,
        fieldStates,
        fields,
        renderField,
        validateForm,
        resetForm
      };
    },
    
    render() {
      return h('form', {
        class: 'linkage-form'
      }, [
        this.fields.map((field: any) => 
          h('div', {
            key: field.name,
            class: 'form-item'
          }, [
            h('label', field.label),
            this.renderField(field)
          ])
        )
      ]);
    }
  };
}

// 扩展联动动作类型，支持异步加载
declare module './linkage-engine-v2' {
  interface LinkageAction {
    asyncLoader?: (values: Record<string, any>) => Promise<any>;
    defaultValue?: any;
  }
}