/**
 * 联动引擎性能监控和优化工具
 * 
 * 提供性能分析、监控和优化建议
 */

import type { LinkageEngineV2, SimpleLinkageRule } from './linkage-engine-v2';

// ==================== 性能监控类型 ====================

/** 性能指标 */
export interface PerformanceMetrics {
  /** 总执行时间（毫秒） */
  totalExecutionTime: number;
  /** 规则执行次数 */
  ruleExecutionCount: number;
  /** 缓存命中率 */
  cacheHitRate: number;
  /** 平均响应时间（毫秒） */
  averageResponseTime: number;
  /** 最慢的规则 */
  slowestRules: Array<{ ruleId: string; executionTime: number }>;
  /** 内存使用情况 */
  memoryUsage: {
    cacheSize: number;
    fieldStatesSize: number;
    listenersSize: number;
  };
}

/** 性能事件 */
interface PerformanceEvent {
  type: 'rule_execution' | 'cache_hit' | 'cache_miss' | 'batch_update';
  timestamp: number;
  duration?: number;
  ruleId?: string;
  fieldName?: string;
  metadata?: any;
}

/** 优化建议 */
export interface OptimizationSuggestion {
  type: 'cache' | 'rule' | 'debounce' | 'batch';
  severity: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  solution: string;
  impact: string;
}

// ==================== 性能监控器 ====================

export class LinkagePerformanceMonitor {
  private events: PerformanceEvent[] = [];
  private ruleExecutionTimes = new Map<string, number[]>();
  private cacheHits = 0;
  private cacheMisses = 0;
  private isEnabled = false;
  private maxEvents = 1000; // 最大事件数量

  /**
   * 启用性能监控
   */
  enable(): void {
    this.isEnabled = true;
    this.reset();
  }

  /**
   * 禁用性能监控
   */
  disable(): void {
    this.isEnabled = false;
  }

  /**
   * 重置监控数据
   */
  reset(): void {
    this.events = [];
    this.ruleExecutionTimes.clear();
    this.cacheHits = 0;
    this.cacheMisses = 0;
  }

  /**
   * 记录性能事件
   */
  recordEvent(event: Omit<PerformanceEvent, 'timestamp'>): void {
    if (!this.isEnabled) return;

    const fullEvent: PerformanceEvent = {
      ...event,
      timestamp: performance.now()
    };

    this.events.push(fullEvent);

    // 限制事件数量
    if (this.events.length > this.maxEvents) {
      this.events.shift();
    }

    // 更新统计数据
    this.updateStats(fullEvent);
  }

  /**
   * 记录规则执行时间
   */
  recordRuleExecution(ruleId: string, duration: number): void {
    if (!this.isEnabled) return;

    if (!this.ruleExecutionTimes.has(ruleId)) {
      this.ruleExecutionTimes.set(ruleId, []);
    }

    const times = this.ruleExecutionTimes.get(ruleId)!;
    times.push(duration);

    // 限制记录数量
    if (times.length > 100) {
      times.shift();
    }

    this.recordEvent({
      type: 'rule_execution',
      duration,
      ruleId
    });
  }

  /**
   * 记录缓存命中
   */
  recordCacheHit(fieldName: string): void {
    if (!this.isEnabled) return;

    this.cacheHits++;
    this.recordEvent({
      type: 'cache_hit',
      fieldName
    });
  }

  /**
   * 记录缓存未命中
   */
  recordCacheMiss(fieldName: string): void {
    if (!this.isEnabled) return;

    this.cacheMisses++;
    this.recordEvent({
      type: 'cache_miss',
      fieldName
    });
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    const ruleExecutions = this.events.filter(e => e.type === 'rule_execution');
    const totalExecutionTime = ruleExecutions.reduce((sum, e) => sum + (e.duration || 0), 0);
    const averageResponseTime = ruleExecutions.length > 0 
      ? totalExecutionTime / ruleExecutions.length 
      : 0;

    // 计算最慢的规则
    const ruleAvgTimes = new Map<string, number>();
    for (const [ruleId, times] of this.ruleExecutionTimes) {
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      ruleAvgTimes.set(ruleId, avgTime);
    }

    const slowestRules = Array.from(ruleAvgTimes.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([ruleId, executionTime]) => ({ ruleId, executionTime }));

    return {
      totalExecutionTime,
      ruleExecutionCount: ruleExecutions.length,
      cacheHitRate: this.getCacheHitRate(),
      averageResponseTime,
      slowestRules,
      memoryUsage: {
        cacheSize: 0, // 需要从引擎获取
        fieldStatesSize: 0, // 需要从引擎获取
        listenersSize: 0 // 需要从引擎获取
      }
    };
  }

  /**
   * 获取优化建议
   */
  getOptimizationSuggestions(rules: SimpleLinkageRule[]): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];
    const metrics = this.getMetrics();

    // 缓存命中率建议
    if (metrics.cacheHitRate < 0.7) {
      suggestions.push({
        type: 'cache',
        severity: 'high',
        title: '缓存命中率过低',
        description: `当前缓存命中率为 ${(metrics.cacheHitRate * 100).toFixed(1)}%，低于推荐的70%`,
        solution: '考虑优化缓存策略，增加缓存有效期或改进缓存键生成算法',
        impact: '提升缓存命中率可显著减少重复计算，提升性能30-50%'
      });
    }

    // 响应时间建议
    if (metrics.averageResponseTime > 10) {
      suggestions.push({
        type: 'rule',
        severity: 'medium',
        title: '平均响应时间过长',
        description: `平均响应时间为 ${metrics.averageResponseTime.toFixed(2)}ms，超过推荐的10ms`,
        solution: '优化规则逻辑，减少复杂计算，或考虑异步处理',
        impact: '优化响应时间可提升用户体验，减少界面卡顿'
      });
    }

    // 规则数量建议
    if (rules.length > 50) {
      suggestions.push({
        type: 'rule',
        severity: 'medium',
        title: '规则数量过多',
        description: `当前有 ${rules.length} 条规则，可能影响性能`,
        solution: '考虑合并相似规则，或使用规则分组策略',
        impact: '减少规则数量可降低计算复杂度，提升整体性能'
      });
    }

    // 防抖建议
    const batchEvents = this.events.filter(e => e.type === 'batch_update');
    if (batchEvents.length > metrics.ruleExecutionCount * 0.1) {
      suggestions.push({
        type: 'debounce',
        severity: 'low',
        title: '频繁的批量更新',
        description: '检测到频繁的批量更新操作，可能需要调整防抖策略',
        solution: '增加防抖延迟时间，或优化触发条件',
        impact: '减少不必要的更新可提升性能和用户体验'
      });
    }

    return suggestions;
  }

  /**
   * 生成性能报告
   */
  generateReport(engine: LinkageEngineV2, rules: SimpleLinkageRule[]): string {
    const metrics = this.getMetrics();
    const suggestions = this.getOptimizationSuggestions(rules);
    const debugInfo = engine.getDebugInfo();

    let report = '# 联动引擎性能报告\n\n';

    // 基本指标
    report += '## 性能指标\n\n';
    report += `- 总执行时间: ${metrics.totalExecutionTime.toFixed(2)}ms\n`;
    report += `- 规则执行次数: ${metrics.ruleExecutionCount}\n`;
    report += `- 缓存命中率: ${(metrics.cacheHitRate * 100).toFixed(1)}%\n`;
    report += `- 平均响应时间: ${metrics.averageResponseTime.toFixed(2)}ms\n\n`;

    // 内存使用
    report += '## 内存使用\n\n';
    report += `- 规则数量: ${debugInfo.rulesCount}\n`;
    report += `- 字段状态数量: ${debugInfo.fieldStatesCount}\n`;
    report += `- 缓存大小: ${debugInfo.cacheSize}\n`;
    report += `- 活跃监听器: ${debugInfo.listeners.length}\n\n`;

    // 最慢的规则
    if (metrics.slowestRules.length > 0) {
      report += '## 最慢的规则\n\n';
      metrics.slowestRules.forEach((rule, index) => {
        report += `${index + 1}. ${rule.ruleId}: ${rule.executionTime.toFixed(2)}ms\n`;
      });
      report += '\n';
    }

    // 优化建议
    if (suggestions.length > 0) {
      report += '## 优化建议\n\n';
      suggestions.forEach((suggestion, index) => {
        report += `### ${index + 1}. ${suggestion.title} (${suggestion.severity})\n\n`;
        report += `**问题**: ${suggestion.description}\n\n`;
        report += `**解决方案**: ${suggestion.solution}\n\n`;
        report += `**预期影响**: ${suggestion.impact}\n\n`;
      });
    }

    return report;
  }

  /**
   * 更新统计数据
   */
  private updateStats(event: PerformanceEvent): void {
    // 这里可以添加更多统计逻辑
  }

  /**
   * 获取缓存命中率
   */
  private getCacheHitRate(): number {
    const total = this.cacheHits + this.cacheMisses;
    return total > 0 ? this.cacheHits / total : 0;
  }
}

// ==================== 性能优化工具 ====================

/**
 * 性能优化器
 */
export class LinkageOptimizer {
  /**
   * 优化规则顺序
   * 将高频触发的规则放在前面，减少不必要的计算
   */
  static optimizeRuleOrder(rules: SimpleLinkageRule[], executionStats: Map<string, number>): SimpleLinkageRule[] {
    return [...rules].sort((a, b) => {
      const aFreq = executionStats.get(a.id) || 0;
      const bFreq = executionStats.get(b.id) || 0;
      
      // 高频规则优先，同频率按优先级排序
      if (aFreq !== bFreq) {
        return bFreq - aFreq;
      }
      
      return (b.priority || 0) - (a.priority || 0);
    });
  }

  /**
   * 分析规则依赖关系
   * 识别可以并行执行的规则组
   */
  static analyzeRuleDependencies(rules: SimpleLinkageRule[]): {
    independent: SimpleLinkageRule[][];
    dependent: SimpleLinkageRule[];
  } {
    const targetFields = new Set<string>();
    const sourceFields = new Set<string>();
    
    // 收集所有目标字段和源字段
    rules.forEach(rule => {
      targetFields.add(rule.target);
      
      const conditions = Array.isArray(rule.when) ? rule.when : [rule.when];
      conditions.forEach(condition => {
        sourceFields.add(condition.field);
      });
    });

    // 找出有依赖关系的规则
    const dependent: SimpleLinkageRule[] = [];
    const independent: SimpleLinkageRule[] = [];

    rules.forEach(rule => {
      const conditions = Array.isArray(rule.when) ? rule.when : [rule.when];
      const hasTargetDependency = conditions.some(condition => 
        targetFields.has(condition.field)
      );
      
      if (hasTargetDependency) {
        dependent.push(rule);
      } else {
        independent.push(rule);
      }
    });

    // 将独立规则按目标字段分组
    const independentGroups: SimpleLinkageRule[][] = [];
    const groupedByTarget = new Map<string, SimpleLinkageRule[]>();
    
    independent.forEach(rule => {
      if (!groupedByTarget.has(rule.target)) {
        groupedByTarget.set(rule.target, []);
      }
      groupedByTarget.get(rule.target)!.push(rule);
    });
    
    independentGroups.push(...Array.from(groupedByTarget.values()));

    return {
      independent: independentGroups,
      dependent
    };
  }

  /**
   * 建议最优的防抖时间
   */
  static suggestOptimalDebounceTime(events: PerformanceEvent[]): number {
    const ruleExecutions = events.filter(e => e.type === 'rule_execution');
    
    if (ruleExecutions.length === 0) {
      return 100; // 默认值
    }

    // 计算平均执行时间
    const avgExecutionTime = ruleExecutions.reduce((sum, e) => sum + (e.duration || 0), 0) / ruleExecutions.length;
    
    // 防抖时间应该是平均执行时间的2-3倍
    const suggestedTime = Math.max(50, Math.min(500, avgExecutionTime * 2.5));
    
    return Math.round(suggestedTime);
  }

  /**
   * 检测性能瓶颈
   */
  static detectBottlenecks(metrics: PerformanceMetrics): string[] {
    const bottlenecks: string[] = [];

    if (metrics.cacheHitRate < 0.5) {
      bottlenecks.push('缓存命中率过低，考虑优化缓存策略');
    }

    if (metrics.averageResponseTime > 20) {
      bottlenecks.push('平均响应时间过长，需要优化规则逻辑');
    }

    if (metrics.slowestRules.length > 0 && metrics.slowestRules[0].executionTime > 50) {
      bottlenecks.push(`规则 ${metrics.slowestRules[0].ruleId} 执行时间过长`);
    }

    return bottlenecks;
  }
}

// ==================== 导出工具函数 ====================

/**
 * 创建性能监控器
 */
export function createPerformanceMonitor(): LinkagePerformanceMonitor {
  return new LinkagePerformanceMonitor();
}

/**
 * 包装联动引擎以添加性能监控
 */
export function withPerformanceMonitoring(engine: LinkageEngineV2): {
  engine: LinkageEngineV2;
  monitor: LinkagePerformanceMonitor;
} {
  const monitor = createPerformanceMonitor();
  monitor.enable();
  
  // 这里可以添加引擎方法的包装逻辑
  // 由于需要修改引擎内部实现，这里只返回监控器
  
  return { engine, monitor };
}