<script setup lang="ts">
import {
  DeleteOutlined,
  DownloadOutlined,
  DownOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  SettingOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue';
import { Button, Dropdown, Menu } from 'ant-design-vue';

// 按钮配置接口
export interface ToolsButtonItem {
  /** 按钮标题 */
  title: string;
  /** 按钮类型/代码 */
  type: string;
  /** 按钮唯一标识 */
  key: string;
  /** 自定义图标 */
  icon?: string;
  /** 子菜单项（用于下拉菜单） */
  children?: ToolsButtonItem[];
}

interface Props {
  /** 按钮配置数组 */
  buttons?: ToolsButtonItem[];
  /** 按钮间距 */
  gap?: number;
}

interface Emits {
  (
    e: 'click',
    buttonKey: string,
    buttonCode: string,
    row: any,
    button: any,
  ): void;
}

const { buttons = [], gap = 8 } = defineProps<Props>();

const emit = defineEmits<Emits>();

// 调试信息
// ToolsButton buttons

// 预设图标映射
const iconMap = {
  add: PlusOutlined,
  edit: EditOutlined,
  delete: DeleteOutlined,
  view: EyeOutlined,
  setting: SettingOutlined,
  import: UploadOutlined,
  download_template: DownloadOutlined,
  upload: UploadOutlined,
  download: DownloadOutlined,
};

// 获取按钮图标
function getButtonIcon(item: ToolsButtonItem) {
  if (item.icon && iconMap[item.icon as keyof typeof iconMap]) {
    return iconMap[item.icon as keyof typeof iconMap];
  }
  // 根据 type 自动匹配图标
  if (iconMap[item.type as keyof typeof iconMap]) {
    return iconMap[item.type as keyof typeof iconMap];
  }
  return null;
}

// 处理按钮点击
function handleClick(item: ToolsButtonItem) {
  // 适配现有的 handleButtonClick 函数签名
  // handleButtonClick(buttonKey: string, buttonCode: string, row: any, button: BackendButton)
  const buttonKey = item.key;
  const buttonCode = item.type;
  const row = null; // 工具栏按钮没有行数据
  const button = item; // 传递整个按钮对象

  // 不再传递 buttonTitle 参数
  emit('click', buttonKey, buttonCode, row, button);
}

// 处理下拉菜单点击
function handleMenuClick(item: ToolsButtonItem, menuItem: ToolsButtonItem) {
  const buttonKey = menuItem.key;
  const buttonCode = menuItem.type;
  const row = null; // 工具栏按钮没有行数据
  const button = menuItem; // 传递菜单项对象

  emit('click', buttonKey, buttonCode, row, button);
}
</script>

<template>
  <div class="tools-button-group" :style="{ gap: `${gap}px` }">
    <template v-for="item in buttons" :key="item.key">
      <!-- 普通按钮 -->
      <Button
        v-if="!item.children || item.children.length === 0"
        type="primary"
        @click="handleClick(item)"
      >
        <template #icon>
          <component :is="getButtonIcon(item)" v-if="getButtonIcon(item)" />
        </template>
        {{ item.title }}
      </Button>

      <!-- 下拉菜单按钮 -->
      <Dropdown v-else>
        <Button type="primary">
          <template #icon>
            <component :is="getButtonIcon(item)" v-if="getButtonIcon(item)" />
          </template>
          {{ item.title }}
          <DownOutlined />
        </Button>
        <template #overlay>
          <Menu>
            <Menu.Item
              v-for="menuItem in item.children"
              :key="menuItem.key"
              @click="handleMenuClick(item, menuItem)"
            >
              <template #icon>
                <component
                  :is="getButtonIcon(menuItem)"
                  v-if="getButtonIcon(menuItem)"
                />
              </template>
              {{ menuItem.title }}
            </Menu.Item>
          </Menu>
        </template>
      </Dropdown>
    </template>
  </div>
</template>

<style scoped>
.tools-button-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
</style>
