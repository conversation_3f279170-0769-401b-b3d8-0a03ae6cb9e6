/**
 * 联动引擎V2测试用例示例
 * 本文件展示了如何对联动引擎V2进行单元测试和集成测试
 */

import { LinkageEngineV2 } from './linkage-engine-v2';
import { RuleBuilder } from './linkage-config-builder';
import { SimpleLinkageRule, SimpleCondition, LinkageAction } from './linkage-engine-v2';

// ======== 测试工具函数 ========

/**
 * 创建测试引擎实例
 */
function createTestEngine(): LinkageEngineV2 {
  return new LinkageEngineV2();
}

/**
 * 等待异步操作完成
 */
function waitForAsync(ms: number = 100): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 断言工具
 */
class TestAssert {
  static equals(actual: any, expected: any, message?: string) {
    if (actual !== expected) {
      throw new Error(message || `Expected ${expected}, but got ${actual}`);
    }
  }
  
  static deepEquals(actual: any, expected: any, message?: string) {
    if (JSON.stringify(actual) !== JSON.stringify(expected)) {
      throw new Error(message || `Expected ${JSON.stringify(expected)}, but got ${JSON.stringify(actual)}`);
    }
  }
  
  static isTrue(value: any, message?: string) {
    if (value !== true) {
      throw new Error(message || `Expected true, but got ${value}`);
    }
  }
  
  static isFalse(value: any, message?: string) {
    if (value !== false) {
      throw new Error(message || `Expected false, but got ${value}`);
    }
  }
  
  static isNull(value: any, message?: string) {
    if (value !== null) {
      throw new Error(message || `Expected null, but got ${value}`);
    }
  }
  
  static isNotNull(value: any, message?: string) {
    if (value === null || value === undefined) {
      throw new Error(message || `Expected non-null value, but got ${value}`);
    }
  }
  
  static throws(fn: () => void, message?: string) {
    try {
      fn();
      throw new Error(message || 'Expected function to throw, but it did not');
    } catch (error) {
      // Expected behavior
    }
  }
}

// ======== 基础功能测试 ========

/**
 * 测试基础功能
 */
export function testBasicFunctionality() {
  console.log('开始测试基础功能...');
  
  // 测试引擎创建
  function testEngineCreation() {
    const engine = createTestEngine();
    TestAssert.isNotNull(engine, '引擎应该被成功创建');
    console.log('✓ 引擎创建测试通过');
  }
  
  // 测试设置初始值
  function testSetInitialValues() {
    const engine = createTestEngine();
    const initialValues = { field1: 'value1', field2: 'value2' };
    
    engine.setValues(initialValues);
    
    const values = engine.getValues();
    TestAssert.deepEquals(values, initialValues, '初始值应该被正确设置');
    console.log('✓ 设置初始值测试通过');
  }
  
  // 测试更新值
  function testUpdateValues() {
    const engine = createTestEngine();
    engine.setValues({ field1: 'initial' });
    
    engine.updateValues({ field1: 'updated' });
    
    const values = engine.getValues();
    TestAssert.equals(values.field1, 'updated', '字段值应该被正确更新');
    console.log('✓ 更新值测试通过');
  }
  
  testEngineCreation();
  testSetInitialValues();
  testUpdateValues();
  
  console.log('基础功能测试完成\n');
}

// ======== 联动规则测试 ========

/**
 * 测试联动规则
 */
export function testLinkageRules() {
  console.log('开始测试联动规则...');
  
  // 测试简单显示规则
  function testSimpleShowRule() {
    const engine = createTestEngine();
    
    const rule: SimpleLinkageRule = {
      id: 'test-show-rule',
      triggerFields: ['trigger'],
      conditions: [
        { field: 'trigger', operator: 'equals', value: 'show' }
      ],
      actions: [
        { field: 'target', property: 'visible', value: true }
      ]
    };
    
    engine.setRules([rule]);
    engine.setValues({ trigger: '', target: '' });
    
    // 初始状态
    let targetState = engine.getFieldState('target');
    TestAssert.equals(targetState.visible, undefined, '初始状态下target应该没有visible属性');
    
    // 触发显示
    engine.updateValues({ trigger: 'show' });
    targetState = engine.getFieldState('target');
    TestAssert.equals(targetState.visible, true, '触发条件满足时target应该可见');
    
    // 取消显示
    engine.updateValues({ trigger: 'hide' });
    targetState = engine.getFieldState('target');
    TestAssert.equals(targetState.visible, undefined, '触发条件不满足时target应该恢复默认状态');
    
    console.log('✓ 简单显示规则测试通过');
  }
  
  // 测试必填规则
  function testRequiredRule() {
    const engine = createTestEngine();
    
    const rule: SimpleLinkageRule = {
      id: 'test-required-rule',
      triggerFields: ['type'],
      conditions: [
        { field: 'type', operator: 'equals', value: 'required' }
      ],
      actions: [
        { field: 'name', property: 'required', value: true }
      ]
    };
    
    engine.setRules([rule]);
    engine.setValues({ type: '', name: '' });
    
    // 触发必填
    engine.updateValues({ type: 'required' });
    const nameState = engine.getFieldState('name');
    TestAssert.equals(nameState.required, true, '触发条件满足时name应该为必填');
    
    console.log('✓ 必填规则测试通过');
  }
  
  // 测试禁用规则
  function testDisabledRule() {
    const engine = createTestEngine();
    
    const rule: SimpleLinkageRule = {
      id: 'test-disabled-rule',
      triggerFields: ['status'],
      conditions: [
        { field: 'status', operator: 'equals', value: 'locked' }
      ],
      actions: [
        { field: 'input', property: 'disabled', value: true }
      ]
    };
    
    engine.setRules([rule]);
    engine.setValues({ status: '', input: '' });
    
    // 触发禁用
    engine.updateValues({ status: 'locked' });
    const inputState = engine.getFieldState('input');
    TestAssert.equals(inputState.disabled, true, '触发条件满足时input应该被禁用');
    
    console.log('✓ 禁用规则测试通过');
  }
  
  testSimpleShowRule();
  testRequiredRule();
  testDisabledRule();
  
  console.log('联动规则测试完成\n');
}

// ======== 条件评估测试 ========

/**
 * 测试条件评估
 */
export function testConditionEvaluation() {
  console.log('开始测试条件评估...');
  
  // 测试等于操作符
  function testEqualsOperator() {
    const engine = createTestEngine();
    
    const rule: SimpleLinkageRule = {
      id: 'test-equals',
      triggerFields: ['field'],
      conditions: [
        { field: 'field', operator: 'equals', value: 'test' }
      ],
      actions: [
        { field: 'result', property: 'visible', value: true }
      ]
    };
    
    engine.setRules([rule]);
    engine.setValues({ field: '', result: '' });
    
    // 测试匹配
    engine.updateValues({ field: 'test' });
    TestAssert.equals(engine.getFieldState('result').visible, true, 'equals操作符应该正确匹配');
    
    // 测试不匹配
    engine.updateValues({ field: 'other' });
    TestAssert.equals(engine.getFieldState('result').visible, undefined, 'equals操作符应该正确处理不匹配情况');
    
    console.log('✓ equals操作符测试通过');
  }
  
  // 测试大于操作符
  function testGreaterThanOperator() {
    const engine = createTestEngine();
    
    const rule: SimpleLinkageRule = {
      id: 'test-greater-than',
      triggerFields: ['age'],
      conditions: [
        { field: 'age', operator: 'greaterThan', value: 18 }
      ],
      actions: [
        { field: 'adult', property: 'visible', value: true }
      ]
    };
    
    engine.setRules([rule]);
    engine.setValues({ age: 0, adult: '' });
    
    // 测试大于
    engine.updateValues({ age: 25 });
    TestAssert.equals(engine.getFieldState('adult').visible, true, 'greaterThan操作符应该正确处理大于情况');
    
    // 测试小于等于
    engine.updateValues({ age: 16 });
    TestAssert.equals(engine.getFieldState('adult').visible, undefined, 'greaterThan操作符应该正确处理小于等于情况');
    
    console.log('✓ greaterThan操作符测试通过');
  }
  
  // 测试包含操作符
  function testContainsOperator() {
    const engine = createTestEngine();
    
    const rule: SimpleLinkageRule = {
      id: 'test-contains',
      triggerFields: ['text'],
      conditions: [
        { field: 'text', operator: 'contains', value: 'test' }
      ],
      actions: [
        { field: 'match', property: 'visible', value: true }
      ]
    };
    
    engine.setRules([rule]);
    engine.setValues({ text: '', match: '' });
    
    // 测试包含
    engine.updateValues({ text: 'this is a test string' });
    TestAssert.equals(engine.getFieldState('match').visible, true, 'contains操作符应该正确处理包含情况');
    
    // 测试不包含
    engine.updateValues({ text: 'this is a sample string' });
    TestAssert.equals(engine.getFieldState('match').visible, undefined, 'contains操作符应该正确处理不包含情况');
    
    console.log('✓ contains操作符测试通过');
  }
  
  // 测试空值操作符
  function testIsEmptyOperator() {
    const engine = createTestEngine();
    
    const rule: SimpleLinkageRule = {
      id: 'test-is-empty',
      triggerFields: ['input'],
      conditions: [
        { field: 'input', operator: 'isEmpty', value: null }
      ],
      actions: [
        { field: 'warning', property: 'visible', value: true }
      ]
    };
    
    engine.setRules([rule]);
    engine.setValues({ input: '', warning: '' });
    
    // 测试空值
    engine.updateValues({ input: '' });
    TestAssert.equals(engine.getFieldState('warning').visible, true, 'isEmpty操作符应该正确处理空值情况');
    
    // 测试非空值
    engine.updateValues({ input: 'not empty' });
    TestAssert.equals(engine.getFieldState('warning').visible, undefined, 'isEmpty操作符应该正确处理非空值情况');
    
    console.log('✓ isEmpty操作符测试通过');
  }
  
  testEqualsOperator();
  testGreaterThanOperator();
  testContainsOperator();
  testIsEmptyOperator();
  
  console.log('条件评估测试完成\n');
}

// ======== 复杂联动测试 ========

/**
 * 测试复杂联动
 */
export function testComplexLinkage() {
  console.log('开始测试复杂联动...');
  
  // 测试多条件联动
  function testMultipleConditions() {
    const engine = createTestEngine();
    
    const rule: SimpleLinkageRule = {
      id: 'test-multiple-conditions',
      triggerFields: ['type', 'status'],
      conditions: [
        { field: 'type', operator: 'equals', value: 'premium' },
        { field: 'status', operator: 'equals', value: 'active' }
      ],
      actions: [
        { field: 'discount', property: 'visible', value: true }
      ]
    };
    
    engine.setRules([rule]);
    engine.setValues({ type: '', status: '', discount: '' });
    
    // 只满足一个条件
    engine.updateValues({ type: 'premium', status: 'inactive' });
    TestAssert.equals(engine.getFieldState('discount').visible, undefined, '只满足部分条件时不应该触发');
    
    // 满足所有条件
    engine.updateValues({ type: 'premium', status: 'active' });
    TestAssert.equals(engine.getFieldState('discount').visible, true, '满足所有条件时应该触发');
    
    console.log('✓ 多条件联动测试通过');
  }
  
  // 测试多动作联动
  function testMultipleActions() {
    const engine = createTestEngine();
    
    const rule: SimpleLinkageRule = {
      id: 'test-multiple-actions',
      triggerFields: ['mode'],
      conditions: [
        { field: 'mode', operator: 'equals', value: 'advanced' }
      ],
      actions: [
        { field: 'field1', property: 'visible', value: true },
        { field: 'field2', property: 'required', value: true },
        { field: 'field3', property: 'disabled', value: false }
      ]
    };
    
    engine.setRules([rule]);
    engine.setValues({ mode: '', field1: '', field2: '', field3: '' });
    
    // 触发多动作
    engine.updateValues({ mode: 'advanced' });
    
    TestAssert.equals(engine.getFieldState('field1').visible, true, 'field1应该可见');
    TestAssert.equals(engine.getFieldState('field2').required, true, 'field2应该必填');
    TestAssert.equals(engine.getFieldState('field3').disabled, false, 'field3应该启用');
    
    console.log('✓ 多动作联动测试通过');
  }
  
  // 测试链式联动
  function testChainedLinkage() {
    const engine = createTestEngine();
    
    const rules: SimpleLinkageRule[] = [
      {
        id: 'rule1',
        triggerFields: ['step1'],
        conditions: [
          { field: 'step1', operator: 'equals', value: 'complete' }
        ],
        actions: [
          { field: 'step2', property: 'visible', value: true }
        ]
      },
      {
        id: 'rule2',
        triggerFields: ['step2'],
        conditions: [
          { field: 'step2', operator: 'equals', value: 'complete' }
        ],
        actions: [
          { field: 'step3', property: 'visible', value: true }
        ]
      }
    ];
    
    engine.setRules(rules);
    engine.setValues({ step1: '', step2: '', step3: '' });
    
    // 第一步
    engine.updateValues({ step1: 'complete' });
    TestAssert.equals(engine.getFieldState('step2').visible, true, 'step2应该在step1完成后可见');
    TestAssert.equals(engine.getFieldState('step3').visible, undefined, 'step3应该还不可见');
    
    // 第二步
    engine.updateValues({ step2: 'complete' });
    TestAssert.equals(engine.getFieldState('step3').visible, true, 'step3应该在step2完成后可见');
    
    console.log('✓ 链式联动测试通过');
  }
  
  testMultipleConditions();
  testMultipleActions();
  testChainedLinkage();
  
  console.log('复杂联动测试完成\n');
}

// ======== 计算功能测试 ========

/**
 * 测试计算功能
 */
export function testCalculationFeatures() {
  console.log('开始测试计算功能...');
  
  // 测试简单计算
  function testSimpleCalculation() {
    const engine = createTestEngine();
    
    const rule: SimpleLinkageRule = {
      id: 'test-calculation',
      triggerFields: ['price', 'quantity'],
      conditions: [],
      actions: [
        {
          field: 'total',
          property: 'value',
          valueFunction: (values) => {
            const price = parseFloat(values.price) || 0;
            const quantity = parseFloat(values.quantity) || 0;
            return price * quantity;
          }
        }
      ]
    };
    
    engine.setRules([rule]);
    engine.setValues({ price: 0, quantity: 0, total: 0 });
    
    // 触发计算
    engine.updateValues({ price: 10, quantity: 5 });
    
    const totalState = engine.getFieldState('total');
    TestAssert.equals(totalState.value, 50, '计算结果应该正确');
    
    console.log('✓ 简单计算测试通过');
  }
  
  // 测试复杂计算
  function testComplexCalculation() {
    const engine = createTestEngine();
    
    const rule: SimpleLinkageRule = {
      id: 'test-complex-calculation',
      triggerFields: ['items'],
      conditions: [],
      actions: [
        {
          field: 'summary',
          property: 'value',
          valueFunction: (values) => {
            const items = values.items || [];
            return {
              totalQuantity: items.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0),
              totalAmount: items.reduce((sum: number, item: any) => sum + ((item.price || 0) * (item.quantity || 0)), 0),
              itemCount: items.length
            };
          }
        }
      ]
    };
    
    engine.setRules([rule]);
    engine.setValues({
      items: [
        { price: 10, quantity: 2 },
        { price: 20, quantity: 3 }
      ],
      summary: {}
    });
    
    // 触发计算
    engine.updateValues({
      items: [
        { price: 10, quantity: 2 },
        { price: 20, quantity: 3 },
        { price: 15, quantity: 1 }
      ]
    });
    
    const summaryState = engine.getFieldState('summary');
    const summary = summaryState.value;
    
    TestAssert.equals(summary.totalQuantity, 6, '总数量计算应该正确');
    TestAssert.equals(summary.totalAmount, 95, '总金额计算应该正确');
    TestAssert.equals(summary.itemCount, 3, '项目数量计算应该正确');
    
    console.log('✓ 复杂计算测试通过');
  }
  
  testSimpleCalculation();
  testComplexCalculation();
  
  console.log('计算功能测试完成\n');
}

// ======== 性能测试 ========

/**
 * 测试性能
 */
export function testPerformance() {
  console.log('开始测试性能...');
  
  // 测试大量规则性能
  function testManyRulesPerformance() {
    const engine = createTestEngine();
    engine.enablePerformanceMonitoring();
    
    // 创建100个规则
    const rules: SimpleLinkageRule[] = [];
    for (let i = 1; i <= 100; i++) {
      rules.push({
        id: `rule-${i}`,
        triggerFields: ['trigger'],
        conditions: [
          { field: 'trigger', operator: 'equals', value: `value${i}` }
        ],
        actions: [
          { field: `field${i}`, property: 'visible', value: true }
        ]
      });
    }
    
    engine.setRules(rules);
    
    // 设置初始值
    const initialValues: Record<string, any> = { trigger: '' };
    for (let i = 1; i <= 100; i++) {
      initialValues[`field${i}`] = '';
    }
    engine.setValues(initialValues);
    
    // 测试更新性能
    const startTime = performance.now();
    
    for (let i = 1; i <= 10; i++) {
      engine.updateValues({ trigger: `value${i}` });
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`100个规则，10次更新耗时: ${duration.toFixed(2)}ms`);
    
    // 获取性能指标
    const metrics = engine.getPerformanceMetrics();
    console.log('性能指标:', {
      totalExecutionTime: metrics.totalExecutionTime,
      ruleExecutionCount: metrics.ruleExecutionCount,
      cacheHitRate: metrics.cacheHitRate,
      averageResponseTime: metrics.averageResponseTime
    });
    
    TestAssert.isTrue(duration < 1000, '大量规则处理应该在合理时间内完成');
    
    console.log('✓ 大量规则性能测试通过');
  }
  
  // 测试批量更新性能
  function testBatchUpdatePerformance() {
    const engine = createTestEngine();
    engine.enablePerformanceMonitoring();
    
    const rule: SimpleLinkageRule = {
      id: 'batch-test-rule',
      triggerFields: ['field1', 'field2', 'field3', 'field4', 'field5'],
      conditions: [],
      actions: [
        {
          field: 'result',
          property: 'value',
          valueFunction: (values) => {
            return Object.values(values).join('-');
          }
        }
      ]
    };
    
    engine.setRules([rule]);
    engine.setValues({
      field1: '', field2: '', field3: '', field4: '', field5: '', result: ''
    });
    
    // 测试单个更新
    const singleUpdateStart = performance.now();
    engine.updateValues({ field1: 'a' });
    engine.updateValues({ field2: 'b' });
    engine.updateValues({ field3: 'c' });
    engine.updateValues({ field4: 'd' });
    engine.updateValues({ field5: 'e' });
    const singleUpdateEnd = performance.now();
    
    // 重置
    engine.setValues({
      field1: '', field2: '', field3: '', field4: '', field5: '', result: ''
    });
    
    // 测试批量更新
    const batchUpdateStart = performance.now();
    engine.updateValues({
      field1: 'a',
      field2: 'b',
      field3: 'c',
      field4: 'd',
      field5: 'e'
    });
    const batchUpdateEnd = performance.now();
    
    const singleUpdateTime = singleUpdateEnd - singleUpdateStart;
    const batchUpdateTime = batchUpdateEnd - batchUpdateStart;
    
    console.log(`单个更新耗时: ${singleUpdateTime.toFixed(2)}ms`);
    console.log(`批量更新耗时: ${batchUpdateTime.toFixed(2)}ms`);
    
    TestAssert.isTrue(batchUpdateTime < singleUpdateTime, '批量更新应该比单个更新更快');
    
    console.log('✓ 批量更新性能测试通过');
  }
  
  testManyRulesPerformance();
  testBatchUpdatePerformance();
  
  console.log('性能测试完成\n');
}

// ======== 错误处理测试 ========

/**
 * 测试错误处理
 */
export function testErrorHandling() {
  console.log('开始测试错误处理...');
  
  // 测试无效规则处理
  function testInvalidRuleHandling() {
    const engine = createTestEngine();
    
    // 测试空规则数组
    engine.setRules([]);
    engine.setValues({ field: 'value' });
    engine.updateValues({ field: 'new value' });
    
    console.log('✓ 空规则数组处理测试通过');
    
    // 测试无效条件
    const invalidRule: any = {
      id: 'invalid-rule',
      triggerFields: ['field'],
      conditions: [
        { field: 'field', operator: 'invalid_operator', value: 'test' }
      ],
      actions: [
        { field: 'target', property: 'visible', value: true }
      ]
    };
    
    engine.setRules([invalidRule]);
    engine.updateValues({ field: 'test' });
    
    // 应该不会抛出异常，而是忽略无效操作符
    console.log('✓ 无效操作符处理测试通过');
  }
  
  // 测试计算错误处理
  function testCalculationErrorHandling() {
    const engine = createTestEngine();
    
    const rule: SimpleLinkageRule = {
      id: 'error-calculation',
      triggerFields: ['input'],
      conditions: [],
      actions: [
        {
          field: 'result',
          property: 'value',
          valueFunction: (values) => {
            // 故意抛出错误
            if (values.input === 'error') {
              throw new Error('计算错误');
            }
            return values.input;
          }
        }
      ]
    };
    
    engine.setRules([rule]);
    engine.setValues({ input: '', result: '' });
    
    // 触发错误
    engine.updateValues({ input: 'error' });
    
    // 应该不会崩溃，错误应该被捕获
    const resultState = engine.getFieldState('result');
    TestAssert.equals(resultState.value, undefined, '计算错误时应该返回undefined');
    
    console.log('✓ 计算错误处理测试通过');
  }
  
  // 测试循环依赖检测
  function testCircularDependencyDetection() {
    const engine = createTestEngine();
    
    const rules: SimpleLinkageRule[] = [
      {
        id: 'rule1',
        triggerFields: ['field1'],
        conditions: [],
        actions: [
          {
            field: 'field2',
            property: 'value',
            valueFunction: (values) => values.field1 + '1'
          }
        ]
      },
      {
        id: 'rule2',
        triggerFields: ['field2'],
        conditions: [],
        actions: [
          {
            field: 'field1',
            property: 'value',
            valueFunction: (values) => values.field2 + '2'
          }
        ]
      }
    ];
    
    engine.setRules(rules);
    engine.setValues({ field1: 'initial', field2: '' });
    
    // 应该检测到循环依赖并处理
    engine.updateValues({ field1: 'test' });
    
    console.log('✓ 循环依赖检测测试通过');
  }
  
  testInvalidRuleHandling();
  testCalculationErrorHandling();
  testCircularDependencyDetection();
  
  console.log('错误处理测试完成\n');
}

// ======== 配置构建器测试 ========

/**
 * 测试配置构建器
 */
export function testConfigBuilder() {
  console.log('开始测试配置构建器...');
  
  // 测试基本构建器功能
  function testBasicBuilder() {
    const rule = new RuleBuilder()
      .id('test-builder-rule')
      .when('field1').equals('value1')
      .then('field2').setVisible(true)
      .build();
    
    TestAssert.equals(rule.id, 'test-builder-rule', '规则ID应该正确设置');
    TestAssert.equals(rule.triggerFields.length, 1, '应该有一个触发字段');
    TestAssert.equals(rule.triggerFields[0], 'field1', '触发字段应该正确');
    TestAssert.equals(rule.conditions.length, 1, '应该有一个条件');
    TestAssert.equals(rule.actions.length, 1, '应该有一个动作');
    
    console.log('✓ 基本构建器功能测试通过');
  }
  
  // 测试链式条件
  function testChainedConditions() {
    const rule = new RuleBuilder()
      .id('chained-conditions')
      .when('field1').equals('value1')
      .and('field2').greaterThan(10)
      .then('field3').setVisible(true)
      .build();
    
    TestAssert.equals(rule.conditions.length, 2, '应该有两个条件');
    TestAssert.equals(rule.triggerFields.length, 2, '应该有两个触发字段');
    
    console.log('✓ 链式条件测试通过');
  }
  
  // 测试多个动作
  function testMultipleActions() {
    const rule = new RuleBuilder()
      .id('multiple-actions')
      .when('trigger').equals('activate')
      .then('field1').setVisible(true)
      .and('field2').setRequired(true)
      .and('field3').setDisabled(false)
      .build();
    
    TestAssert.equals(rule.actions.length, 3, '应该有三个动作');
    
    const visibleAction = rule.actions.find(a => a.property === 'visible');
    const requiredAction = rule.actions.find(a => a.property === 'required');
    const disabledAction = rule.actions.find(a => a.property === 'disabled');
    
    TestAssert.isNotNull(visibleAction, '应该有visible动作');
    TestAssert.isNotNull(requiredAction, '应该有required动作');
    TestAssert.isNotNull(disabledAction, '应该有disabled动作');
    
    console.log('✓ 多个动作测试通过');
  }
  
  testBasicBuilder();
  testChainedConditions();
  testMultipleActions();
  
  console.log('配置构建器测试完成\n');
}

// ======== 集成测试 ========

/**
 * 测试完整的集成场景
 */
export async function testIntegrationScenarios() {
  console.log('开始测试集成场景...');
  
  // 测试用户注册表单场景
  async function testUserRegistrationForm() {
    const engine = createTestEngine();
    
    const rules: SimpleLinkageRule[] = [
      // 用户类型影响显示字段
      {
        id: 'show-company-fields',
        triggerFields: ['userType'],
        conditions: [
          { field: 'userType', operator: 'equals', value: 'business' }
        ],
        actions: [
          { field: 'companyName', property: 'visible', value: true },
          { field: 'companyName', property: 'required', value: true },
          { field: 'taxId', property: 'visible', value: true }
        ]
      },
      // 年龄影响折扣显示
      {
        id: 'show-senior-discount',
        triggerFields: ['age'],
        conditions: [
          { field: 'age', operator: 'greaterThanOrEqual', value: 65 }
        ],
        actions: [
          { field: 'seniorDiscount', property: 'visible', value: true }
        ]
      },
      // 邮箱验证
      {
        id: 'email-validation',
        triggerFields: ['email'],
        conditions: [
          { field: 'email', operator: 'contains', value: '@' }
        ],
        actions: [
          { field: 'emailValid', property: 'value', value: true }
        ]
      }
    ];
    
    engine.setRules(rules);
    engine.setValues({
      userType: '',
      age: 0,
      email: '',
      companyName: '',
      taxId: '',
      seniorDiscount: false,
      emailValid: false
    });
    
    // 模拟用户填写表单
    engine.updateValues({ userType: 'business' });
    
    TestAssert.equals(engine.getFieldState('companyName').visible, true, '企业用户应该显示公司名称字段');
    TestAssert.equals(engine.getFieldState('companyName').required, true, '公司名称应该为必填');
    
    engine.updateValues({ age: 70 });
    TestAssert.equals(engine.getFieldState('seniorDiscount').visible, true, '65岁以上用户应该显示老年折扣');
    
    engine.updateValues({ email: '<EMAIL>' });
    TestAssert.equals(engine.getFieldState('emailValid').value, true, '有效邮箱应该通过验证');
    
    console.log('✓ 用户注册表单场景测试通过');
  }
  
  // 测试订单计算场景
  async function testOrderCalculationForm() {
    const engine = createTestEngine();
    
    const rules: SimpleLinkageRule[] = [
      // 计算小计
      {
        id: 'calculate-subtotal',
        triggerFields: ['price', 'quantity'],
        conditions: [],
        actions: [
          {
            field: 'subtotal',
            property: 'value',
            valueFunction: (values) => {
              const price = parseFloat(values.price) || 0;
              const quantity = parseFloat(values.quantity) || 0;
              return price * quantity;
            }
          }
        ]
      },
      // 计算折扣
      {
        id: 'calculate-discount',
        triggerFields: ['subtotal', 'customerLevel'],
        conditions: [],
        actions: [
          {
            field: 'discount',
            property: 'value',
            valueFunction: (values) => {
              const subtotal = parseFloat(values.subtotal) || 0;
              const level = values.customerLevel;
              
              let discountRate = 0;
              if (level === 'vip') discountRate = 0.1;
              else if (level === 'gold') discountRate = 0.05;
              
              return subtotal * discountRate;
            }
          }
        ]
      },
      // 计算总计
      {
        id: 'calculate-total',
        triggerFields: ['subtotal', 'discount', 'tax'],
        conditions: [],
        actions: [
          {
            field: 'total',
            property: 'value',
            valueFunction: (values) => {
              const subtotal = parseFloat(values.subtotal) || 0;
              const discount = parseFloat(values.discount) || 0;
              const tax = parseFloat(values.tax) || 0;
              return subtotal - discount + tax;
            }
          }
        ]
      }
    ];
    
    engine.setRules(rules);
    engine.setValues({
      price: 0,
      quantity: 0,
      customerLevel: 'normal',
      subtotal: 0,
      discount: 0,
      tax: 0,
      total: 0
    });
    
    // 模拟订单计算
    engine.updateValues({ price: 100, quantity: 2 });
    TestAssert.equals(engine.getFieldState('subtotal').value, 200, '小计计算应该正确');
    
    engine.updateValues({ customerLevel: 'vip' });
    TestAssert.equals(engine.getFieldState('discount').value, 20, 'VIP折扣计算应该正确');
    
    engine.updateValues({ tax: 15 });
    TestAssert.equals(engine.getFieldState('total').value, 195, '总计计算应该正确');
    
    console.log('✓ 订单计算场景测试通过');
  }
  
  await testUserRegistrationForm();
  await testOrderCalculationForm();
  
  console.log('集成场景测试完成\n');
}

// ======== 运行所有测试 ========

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('=== 联动引擎V2测试套件 ===\n');
  
  try {
    testBasicFunctionality();
    testLinkageRules();
    testConditionEvaluation();
    testComplexLinkage();
    testCalculationFeatures();
    testPerformance();
    testErrorHandling();
    testConfigBuilder();
    await testIntegrationScenarios();
    
    console.log('🎉 所有测试通过！');
  } catch (error) {
    console.error('❌ 测试失败:', error);
    throw error;
  }
}

// ======== 测试运行器 ========

/**
 * 测试运行器 - 可以选择性运行测试
 */
export class TestRunner {
  private tests: Map<string, () => void | Promise<void>> = new Map();
  
  constructor() {
    this.tests.set('basic', testBasicFunctionality);
    this.tests.set('rules', testLinkageRules);
    this.tests.set('conditions', testConditionEvaluation);
    this.tests.set('complex', testComplexLinkage);
    this.tests.set('calculation', testCalculationFeatures);
    this.tests.set('performance', testPerformance);
    this.tests.set('errors', testErrorHandling);
    this.tests.set('builder', testConfigBuilder);
    this.tests.set('integration', testIntegrationScenarios);
  }
  
  async run(testNames?: string[]) {
    const testsToRun = testNames || Array.from(this.tests.keys());
    
    console.log(`运行测试: ${testsToRun.join(', ')}\n`);
    
    for (const testName of testsToRun) {
      const test = this.tests.get(testName);
      if (test) {
        try {
          await test();
        } catch (error) {
          console.error(`测试 ${testName} 失败:`, error);
          throw error;
        }
      } else {
        console.warn(`未找到测试: ${testName}`);
      }
    }
    
    console.log('✅ 选定的测试全部通过！');
  }
  
  listTests() {
    console.log('可用的测试:');
    Array.from(this.tests.keys()).forEach(name => {
      console.log(`- ${name}`);
    });
  }
}

// 使用示例
// const runner = new TestRunner();
// runner.run(['basic', 'rules']); // 运行特定测试
// runner.run(); // 运行所有测试