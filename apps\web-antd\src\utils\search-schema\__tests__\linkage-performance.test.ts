/**
 * 联动引擎性能监控器单元测试
 * 
 * 测试性能监控、优化和报告功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  LinkagePerformanceMonitor,
  type PerformanceMetrics,
  type PerformanceConfig
} from '../linkage-performance';

// ==================== 基础功能测试 ====================

describe('LinkagePerformanceMonitor - 基础功能', () => {
  let monitor: LinkagePerformanceMonitor;

  beforeEach(() => {
    monitor = new LinkagePerformanceMonitor();
  });

  afterEach(() => {
    monitor.destroy();
  });

  it('应该正确初始化性能监控器', () => {
    expect(monitor).toBeDefined();
    
    const metrics = monitor.getMetrics();
    expect(metrics.totalExecutions).toBe(0);
    expect(metrics.averageExecutionTime).toBe(0);
    expect(metrics.maxExecutionTime).toBe(0);
    expect(metrics.minExecutionTime).toBe(Infinity);
  });

  it('应该正确记录执行时间', () => {
    const ruleId = 'test_rule';
    const executionTime = 50;
    
    monitor.recordExecution(ruleId, executionTime);
    
    const metrics = monitor.getMetrics();
    expect(metrics.totalExecutions).toBe(1);
    expect(metrics.averageExecutionTime).toBe(50);
    expect(metrics.maxExecutionTime).toBe(50);
    expect(metrics.minExecutionTime).toBe(50);
  });

  it('应该正确计算平均执行时间', () => {
    monitor.recordExecution('rule1', 100);
    monitor.recordExecution('rule2', 200);
    monitor.recordExecution('rule3', 300);
    
    const metrics = monitor.getMetrics();
    expect(metrics.totalExecutions).toBe(3);
    expect(metrics.averageExecutionTime).toBe(200);
    expect(metrics.maxExecutionTime).toBe(300);
    expect(metrics.minExecutionTime).toBe(100);
  });

  it('应该正确记录规则级别的性能指标', () => {
    monitor.recordExecution('rule1', 100);
    monitor.recordExecution('rule1', 150);
    monitor.recordExecution('rule2', 200);
    
    const ruleMetrics = monitor.getRuleMetrics('rule1');
    expect(ruleMetrics?.executionCount).toBe(2);
    expect(ruleMetrics?.averageTime).toBe(125);
    expect(ruleMetrics?.maxTime).toBe(150);
    expect(ruleMetrics?.minTime).toBe(100);
    
    const rule2Metrics = monitor.getRuleMetrics('rule2');
    expect(rule2Metrics?.executionCount).toBe(1);
    expect(rule2Metrics?.averageTime).toBe(200);
  });
});

// ==================== 性能阈值测试 ====================

describe('LinkagePerformanceMonitor - 性能阈值', () => {
  let monitor: LinkagePerformanceMonitor;
  let mockWarningCallback: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockWarningCallback = vi.fn();
    
    const config: PerformanceConfig = {
      warningThreshold: 100,
      errorThreshold: 200,
      onWarning: mockWarningCallback,
      onError: mockWarningCallback
    };
    
    monitor = new LinkagePerformanceMonitor(config);
  });

  afterEach(() => {
    monitor.destroy();
  });

  it('应该在超过警告阈值时触发警告', () => {
    monitor.recordExecution('slow_rule', 150);
    
    expect(mockWarningCallback).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'warning',
        ruleId: 'slow_rule',
        executionTime: 150,
        threshold: 100
      })
    );
  });

  it('应该在超过错误阈值时触发错误', () => {
    monitor.recordExecution('very_slow_rule', 250);
    
    expect(mockWarningCallback).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'error',
        ruleId: 'very_slow_rule',
        executionTime: 250,
        threshold: 200
      })
    );
  });

  it('应该正确识别性能瓶颈', () => {
    // 记录多次执行，其中一些超过阈值
    monitor.recordExecution('fast_rule', 50);
    monitor.recordExecution('medium_rule', 120);
    monitor.recordExecution('slow_rule', 180);
    monitor.recordExecution('very_slow_rule', 300);
    
    const bottlenecks = monitor.getBottlenecks();
    expect(bottlenecks).toHaveLength(2); // medium_rule 和 slow_rule 超过警告阈值
    expect(bottlenecks[0].ruleId).toBe('very_slow_rule');
    expect(bottlenecks[1].ruleId).toBe('slow_rule');
  });
});

// ==================== 内存使用监控测试 ====================

describe('LinkagePerformanceMonitor - 内存监控', () => {
  let monitor: LinkagePerformanceMonitor;

  beforeEach(() => {
    monitor = new LinkagePerformanceMonitor({
      enableMemoryMonitoring: true,
      memoryWarningThreshold: 50 * 1024 * 1024, // 50MB
      memoryErrorThreshold: 100 * 1024 * 1024   // 100MB
    });
  });

  afterEach(() => {
    monitor.destroy();
  });

  it('应该正确监控内存使用', () => {
    // 模拟内存使用
    monitor.recordMemoryUsage(30 * 1024 * 1024); // 30MB
    
    const metrics = monitor.getMetrics();
    expect(metrics.memoryUsage).toBeDefined();
    expect(metrics.memoryUsage?.current).toBe(30 * 1024 * 1024);
  });

  it('应该正确记录内存峰值', () => {
    monitor.recordMemoryUsage(20 * 1024 * 1024);
    monitor.recordMemoryUsage(40 * 1024 * 1024);
    monitor.recordMemoryUsage(30 * 1024 * 1024);
    
    const metrics = monitor.getMetrics();
    expect(metrics.memoryUsage?.peak).toBe(40 * 1024 * 1024);
    expect(metrics.memoryUsage?.current).toBe(30 * 1024 * 1024);
  });

  it('应该在内存使用过高时发出警告', () => {
    const mockWarning = vi.fn();
    monitor = new LinkagePerformanceMonitor({
      enableMemoryMonitoring: true,
      memoryWarningThreshold: 50 * 1024 * 1024,
      onMemoryWarning: mockWarning
    });
    
    monitor.recordMemoryUsage(60 * 1024 * 1024);
    
    expect(mockWarning).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'memory_warning',
        currentUsage: 60 * 1024 * 1024,
        threshold: 50 * 1024 * 1024
      })
    );
  });
});

// ==================== 缓存性能测试 ====================

describe('LinkagePerformanceMonitor - 缓存性能', () => {
  let monitor: LinkagePerformanceMonitor;

  beforeEach(() => {
    monitor = new LinkagePerformanceMonitor({
      enableCacheMonitoring: true
    });
  });

  afterEach(() => {
    monitor.destroy();
  });

  it('应该正确记录缓存命中率', () => {
    monitor.recordCacheHit('condition_cache');
    monitor.recordCacheHit('condition_cache');
    monitor.recordCacheMiss('condition_cache');
    
    const cacheStats = monitor.getCacheStats('condition_cache');
    expect(cacheStats?.hits).toBe(2);
    expect(cacheStats?.misses).toBe(1);
    expect(cacheStats?.hitRate).toBeCloseTo(0.67, 2);
  });

  it('应该正确计算总体缓存性能', () => {
    // 条件缓存
    monitor.recordCacheHit('condition_cache');
    monitor.recordCacheHit('condition_cache');
    monitor.recordCacheMiss('condition_cache');
    
    // 结果缓存
    monitor.recordCacheHit('result_cache');
    monitor.recordCacheMiss('result_cache');
    monitor.recordCacheMiss('result_cache');
    
    const overallStats = monitor.getOverallCacheStats();
    expect(overallStats.totalHits).toBe(3);
    expect(overallStats.totalMisses).toBe(3);
    expect(overallStats.overallHitRate).toBe(0.5);
  });

  it('应该在缓存命中率过低时发出警告', () => {
    const mockWarning = vi.fn();
    monitor = new LinkagePerformanceMonitor({
      enableCacheMonitoring: true,
      cacheHitRateThreshold: 0.8,
      onCacheWarning: mockWarning
    });
    
    // 模拟低命中率
    monitor.recordCacheHit('low_hit_cache');
    monitor.recordCacheMiss('low_hit_cache');
    monitor.recordCacheMiss('low_hit_cache');
    monitor.recordCacheMiss('low_hit_cache');
    monitor.recordCacheMiss('low_hit_cache');
    
    expect(mockWarning).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'cache_warning',
        cacheKey: 'low_hit_cache',
        hitRate: 0.2,
        threshold: 0.8
      })
    );
  });
});

// ==================== 性能报告测试 ====================

describe('LinkagePerformanceMonitor - 性能报告', () => {
  let monitor: LinkagePerformanceMonitor;

  beforeEach(() => {
    monitor = new LinkagePerformanceMonitor({
      enableMemoryMonitoring: true,
      enableCacheMonitoring: true
    });
    
    // 模拟一些性能数据
    monitor.recordExecution('rule1', 100);
    monitor.recordExecution('rule1', 150);
    monitor.recordExecution('rule2', 200);
    monitor.recordExecution('rule3', 50);
    
    monitor.recordMemoryUsage(40 * 1024 * 1024);
    
    monitor.recordCacheHit('condition_cache');
    monitor.recordCacheMiss('condition_cache');
  });

  afterEach(() => {
    monitor.destroy();
  });

  it('应该生成完整的性能报告', () => {
    const report = monitor.generateReport();
    
    expect(report.summary).toBeDefined();
    expect(report.summary.totalExecutions).toBe(4);
    expect(report.summary.averageExecutionTime).toBe(125);
    
    expect(report.rulePerformance).toHaveLength(3);
    expect(report.rulePerformance[0].ruleId).toBe('rule2'); // 最慢的规则
    
    expect(report.memoryUsage).toBeDefined();
    expect(report.cachePerformance).toBeDefined();
  });

  it('应该正确识别性能问题', () => {
    // 添加一些性能问题
    monitor.recordExecution('slow_rule', 500); // 慢规则
    monitor.recordMemoryUsage(80 * 1024 * 1024); // 高内存使用
    
    const report = monitor.generateReport();
    
    expect(report.issues).toBeDefined();
    expect(report.issues.length).toBeGreaterThan(0);
    
    const slowRuleIssue = report.issues.find(issue => 
      issue.type === 'slow_execution' && issue.ruleId === 'slow_rule'
    );
    expect(slowRuleIssue).toBeDefined();
  });

  it('应该提供性能优化建议', () => {
    // 模拟各种性能问题
    monitor.recordExecution('frequent_rule', 100);
    monitor.recordExecution('frequent_rule', 100);
    monitor.recordExecution('frequent_rule', 100);
    monitor.recordExecution('frequent_rule', 100);
    monitor.recordExecution('frequent_rule', 100);
    
    monitor.recordCacheMiss('poor_cache');
    monitor.recordCacheMiss('poor_cache');
    monitor.recordCacheMiss('poor_cache');
    monitor.recordCacheHit('poor_cache');
    
    const report = monitor.generateReport();
    
    expect(report.recommendations).toBeDefined();
    expect(report.recommendations.length).toBeGreaterThan(0);
    
    const cacheRecommendation = report.recommendations.find(rec => 
      rec.type === 'cache_optimization'
    );
    expect(cacheRecommendation).toBeDefined();
  });
});

// ==================== 实时监控测试 ====================

describe('LinkagePerformanceMonitor - 实时监控', () => {
  let monitor: LinkagePerformanceMonitor;
  let mockCallback: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockCallback = vi.fn();
    monitor = new LinkagePerformanceMonitor({
      enableRealTimeMonitoring: true,
      realTimeInterval: 100, // 100ms间隔
      onRealTimeUpdate: mockCallback
    });
  });

  afterEach(() => {
    monitor.destroy();
  });

  it('应该定期发送实时性能更新', async () => {
    monitor.recordExecution('test_rule', 100);
    
    // 等待实时更新
    await new Promise(resolve => setTimeout(resolve, 150));
    
    expect(mockCallback).toHaveBeenCalledWith(
      expect.objectContaining({
        timestamp: expect.any(Number),
        metrics: expect.objectContaining({
          totalExecutions: 1,
          averageExecutionTime: 100
        })
      })
    );
  });

  it('应该在停止监控时清理定时器', () => {
    const spy = vi.spyOn(global, 'clearInterval');
    
    monitor.destroy();
    
    expect(spy).toHaveBeenCalled();
    spy.mockRestore();
  });
});

// ==================== 性能优化测试 ====================

describe('LinkagePerformanceMonitor - 性能优化', () => {
  let monitor: LinkagePerformanceMonitor;

  beforeEach(() => {
    monitor = new LinkagePerformanceMonitor({
      enableOptimization: true,
      optimizationThreshold: 100
    });
  });

  afterEach(() => {
    monitor.destroy();
  });

  it('应该自动启用性能优化', () => {
    // 记录多次慢执行
    for (let i = 0; i < 10; i++) {
      monitor.recordExecution('slow_rule', 150);
    }
    
    const optimizations = monitor.getActiveOptimizations();
    expect(optimizations.length).toBeGreaterThan(0);
    
    const debounceOpt = optimizations.find(opt => opt.type === 'debounce');
    expect(debounceOpt).toBeDefined();
  });

  it('应该建议缓存优化', () => {
    // 模拟频繁的缓存未命中
    for (let i = 0; i < 20; i++) {
      monitor.recordCacheMiss('frequent_miss_cache');
    }
    
    const suggestions = monitor.getOptimizationSuggestions();
    const cacheSuggestion = suggestions.find(s => s.type === 'cache_improvement');
    
    expect(cacheSuggestion).toBeDefined();
    expect(cacheSuggestion?.priority).toBe('high');
  });

  it('应该建议规则重构', () => {
    // 模拟复杂规则的慢执行
    for (let i = 0; i < 5; i++) {
      monitor.recordExecution('complex_rule', 300);
    }
    
    const suggestions = monitor.getOptimizationSuggestions();
    const refactorSuggestion = suggestions.find(s => s.type === 'rule_refactoring');
    
    expect(refactorSuggestion).toBeDefined();
    expect(refactorSuggestion?.targetRule).toBe('complex_rule');
  });
});

// ==================== 数据导出导入测试 ====================

describe('LinkagePerformanceMonitor - 数据导出导入', () => {
  let monitor: LinkagePerformanceMonitor;

  beforeEach(() => {
    monitor = new LinkagePerformanceMonitor();
    
    // 添加一些测试数据
    monitor.recordExecution('rule1', 100);
    monitor.recordExecution('rule2', 200);
    monitor.recordCacheHit('cache1');
    monitor.recordCacheMiss('cache1');
  });

  afterEach(() => {
    monitor.destroy();
  });

  it('应该正确导出性能数据', () => {
    const exportedData = monitor.exportData();
    
    expect(exportedData).toBeDefined();
    expect(exportedData.metrics).toBeDefined();
    expect(exportedData.ruleMetrics).toBeDefined();
    expect(exportedData.cacheStats).toBeDefined();
    expect(exportedData.timestamp).toBeDefined();
    
    expect(exportedData.metrics.totalExecutions).toBe(2);
  });

  it('应该正确导入性能数据', () => {
    const exportedData = monitor.exportData();
    
    const newMonitor = new LinkagePerformanceMonitor();
    newMonitor.importData(exportedData);
    
    const importedMetrics = newMonitor.getMetrics();
    expect(importedMetrics.totalExecutions).toBe(2);
    expect(importedMetrics.averageExecutionTime).toBe(150);
    
    const rule1Metrics = newMonitor.getRuleMetrics('rule1');
    expect(rule1Metrics?.executionCount).toBe(1);
    expect(rule1Metrics?.averageTime).toBe(100);
    
    newMonitor.destroy();
  });

  it('应该正确合并导入的数据', () => {
    // 在当前监控器中添加更多数据
    monitor.recordExecution('rule3', 300);
    
    const exportedData = monitor.exportData();
    
    // 创建新监控器并添加一些数据
    const newMonitor = new LinkagePerformanceMonitor();
    newMonitor.recordExecution('rule4', 400);
    
    // 导入数据（应该合并）
    newMonitor.importData(exportedData, { merge: true });
    
    const mergedMetrics = newMonitor.getMetrics();
    expect(mergedMetrics.totalExecutions).toBe(4); // 3个原有 + 1个新增
    
    newMonitor.destroy();
  });
});

// ==================== 集成测试 ====================

describe('LinkagePerformanceMonitor - 集成测试', () => {
  it('应该在真实场景中正确工作', () => {
    const mockWarning = vi.fn();
    const mockRealTimeUpdate = vi.fn();
    
    const monitor = new LinkagePerformanceMonitor({
      warningThreshold: 100,
      errorThreshold: 200,
      enableMemoryMonitoring: true,
      enableCacheMonitoring: true,
      enableRealTimeMonitoring: true,
      realTimeInterval: 50,
      onWarning: mockWarning,
      onError: mockWarning,
      onRealTimeUpdate: mockRealTimeUpdate
    });
    
    // 模拟真实的联动引擎执行
    const rules = ['user_validation', 'price_calculation', 'shipping_update'];
    const executionTimes = [50, 150, 80, 250, 90, 120];
    
    rules.forEach((rule, index) => {
      executionTimes.forEach(time => {
        monitor.recordExecution(rule, time + index * 10);
      });
    });
    
    // 模拟缓存使用
    monitor.recordCacheHit('condition_cache');
    monitor.recordCacheHit('condition_cache');
    monitor.recordCacheMiss('condition_cache');
    
    monitor.recordCacheHit('result_cache');
    monitor.recordCacheMiss('result_cache');
    
    // 模拟内存使用
    monitor.recordMemoryUsage(45 * 1024 * 1024);
    
    // 验证结果
    const metrics = monitor.getMetrics();
    expect(metrics.totalExecutions).toBe(18); // 3 rules * 6 times
    
    const report = monitor.generateReport();
    expect(report.summary.totalExecutions).toBe(18);
    expect(report.rulePerformance).toHaveLength(3);
    
    // 应该有一些警告（因为有执行时间超过100ms的）
    expect(mockWarning).toHaveBeenCalled();
    
    monitor.destroy();
  });
});