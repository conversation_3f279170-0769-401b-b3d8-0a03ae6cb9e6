/**
 * 联动引擎使用示例
 * 
 * 展示新联动引擎的各种使用场景和最佳实践
 */

import { createLinkageEngine, LinkageBuilder } from './linkage-engine-v2';
import { createLinkageConfig, LinkageTemplates } from './linkage-config-builder';
import { createDebugger, withDebugging } from './linkage-debug';
import { createPerformanceMonitor, withPerformanceMonitoring } from './linkage-performance';
import type { SimpleLinkageRule } from './linkage-engine-v2';

// ==================== 基础使用示例 ====================

/**
 * 示例1: 基础显示/隐藏联动
 */
export function basicShowHideExample() {
  // 创建引擎
  const engine = createLinkageEngine();
  
  // 定义规则：当用户类型为企业时显示企业信息字段
  const rules = [
    LinkageTemplates.showHide({
      id: 'show_company_info',
      target: 'companyName',
      triggerField: 'userType',
      showWhen: 'enterprise',
      description: '企业用户显示公司名称字段'
    }),
    
    LinkageTemplates.showHide({
      id: 'show_tax_number',
      target: 'taxNumber',
      triggerField: 'userType',
      showWhen: 'enterprise',
      description: '企业用户显示税号字段'
    })
  ];
  
  // 设置规则
  engine.setRules(rules);
  
  // 监听字段状态变化
  engine.onFieldStateChange((fieldName, state) => {
    console.log(`字段 ${fieldName} 状态变化:`, state);
    // 在这里更新表单字段的显示状态
  });
  
  // 模拟用户选择
  engine.updateValues({ userType: 'enterprise' });
  
  return engine;
}

/**
 * 示例2: 必填字段联动
 */
export function requiredFieldExample() {
  const engine = createLinkageEngine();
  
  const rules = [
    // 当选择快递配送时，收货地址为必填
    LinkageTemplates.requiredOptional({
      id: 'require_address_for_delivery',
      target: 'deliveryAddress',
      triggerField: 'deliveryMethod',
      requiredWhen: 'express',
      description: '快递配送时收货地址为必填'
    }),
    
    // 当选择自提时，自提点为必填
    LinkageTemplates.requiredOptional({
      id: 'require_pickup_point',
      target: 'pickupPoint',
      triggerField: 'deliveryMethod',
      requiredWhen: 'pickup',
      description: '自提时自提点为必填'
    })
  ];
  
  engine.setRules(rules);
  
  // 监听状态变化
  engine.onFieldStateChange((fieldName, state) => {
    if (state.required !== undefined) {
      console.log(`字段 ${fieldName} 必填状态: ${state.required}`);
    }
  });
  
  return engine;
}

/**
 * 示例3: 选项联动
 */
export function optionsLinkageExample() {
  const engine = createLinkageEngine();
  
  // 省市联动
  const cityOptionsRules = LinkageTemplates.optionsLinkage({
    id: 'province_city_linkage',
    target: 'city',
    triggerField: 'province',
    optionsMap: {
      '北京': [
        { label: '朝阳区', value: 'chaoyang' },
        { label: '海淀区', value: 'haidian' },
        { label: '西城区', value: 'xicheng' }
      ],
      '上海': [
        { label: '浦东新区', value: 'pudong' },
        { label: '黄浦区', value: 'huangpu' },
        { label: '徐汇区', value: 'xuhui' }
      ],
      '广东': [
        { label: '广州市', value: 'guangzhou' },
        { label: '深圳市', value: 'shenzhen' },
        { label: '珠海市', value: 'zhuhai' }
      ]
    },
    description: '省份城市联动选择'
  });
  
  engine.setRules(cityOptionsRules);
  
  // 监听选项变化
  engine.onFieldStateChange((fieldName, state) => {
    if (state.options) {
      console.log(`字段 ${fieldName} 选项更新:`, state.options);
    }
  });
  
  return engine;
}

// ==================== 高级使用示例 ====================

/**
 * 示例4: 复杂条件联动
 */
export function complexConditionExample() {
  const engine = createLinkageEngine();
  
  // 使用流式API创建复杂规则
  const rules = [
    // 年龄大于18且选择了驾驶时显示驾照字段
    new LinkageBuilder()
      .rule()
      .id('show_license_for_adult_driver')
      .target('driverLicense')
      .when('age').greaterThan(18)
      .and()
      .when('needDriving').equals(true)
      .then().show().required()
      .description('成年且需要驾驶时显示并要求填写驾照')
      .build(),
    
    // 收入在某个范围内时显示贷款选项
    new LinkageBuilder()
      .rule()
      .id('show_loan_options')
      .target('loanAmount')
      .when('monthlyIncome').inRange(5000, 50000)
      .then().show().setProps({ max: 100000 })
      .description('月收入5K-5W时显示贷款金额选项')
      .build(),
    
    // 多个条件的或关系
    new LinkageBuilder()
      .rule()
      .id('show_contact_for_vip_or_enterprise')
      .target('contactPerson')
      .when('userLevel').equals('VIP')
      .or()
      .when('userType').equals('enterprise')
      .then().show().required()
      .description('VIP用户或企业用户需要填写联系人')
      .build()
  ];
  
  engine.setRules(rules);
  
  return engine;
}

/**
 * 示例5: 值计算联动
 */
export function valueCalculationExample() {
  const engine = createLinkageEngine();
  
  const rules = [
    // 计算总价
    LinkageTemplates.valueCalculation({
      id: 'calculate_total_price',
      target: 'totalPrice',
      triggerFields: ['unitPrice', 'quantity', 'discount'],
      calculator: (values) => {
        const { unitPrice = 0, quantity = 0, discount = 0 } = values;
        const subtotal = unitPrice * quantity;
        return subtotal * (1 - discount / 100);
      },
      description: '根据单价、数量和折扣计算总价'
    }),
    
    // 计算年龄
    LinkageTemplates.valueCalculation({
      id: 'calculate_age',
      target: 'age',
      triggerFields: ['birthDate'],
      calculator: (values) => {
        const { birthDate } = values;
        if (!birthDate) return null;
        
        const birth = new Date(birthDate);
        const today = new Date();
        let age = today.getFullYear() - birth.getFullYear();
        
        const monthDiff = today.getMonth() - birth.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
          age--;
        }
        
        return age;
      },
      description: '根据出生日期计算年龄'
    })
  ];
  
  engine.setRules(rules);
  
  return engine;
}

/**
 * 示例6: 自定义操作符
 */
export function customOperatorExample() {
  const engine = createLinkageEngine();
  
  // 注册自定义操作符
  engine.registerOperator('is_weekend', (fieldValue) => {
    if (!fieldValue) return false;
    const date = new Date(fieldValue);
    const day = date.getDay();
    return day === 0 || day === 6; // 周日或周六
  });
  
  engine.registerOperator('is_business_hours', (fieldValue) => {
    if (!fieldValue) return false;
    const time = new Date(fieldValue);
    const hour = time.getHours();
    return hour >= 9 && hour <= 17; // 9:00-17:00
  });
  
  const rules = [
    // 周末时显示特殊提示
    new LinkageBuilder()
      .rule()
      .id('weekend_notice')
      .target('weekendNotice')
      .when('selectedDate').custom('is_weekend')
      .then().show().setValue('周末可能无法及时处理，请知悉')
      .description('周末时显示特殊提示')
      .build(),
    
    // 非工作时间禁用某些选项
    new LinkageBuilder()
      .rule()
      .id('disable_urgent_outside_hours')
      .target('urgentService')
      .when('appointmentTime').custom('is_business_hours')
      .then().enable()
      .description('工作时间内启用紧急服务选项')
      .build()
  ];
  
  engine.setRules(rules);
  
  return engine;
}

// ==================== 性能优化示例 ====================

/**
 * 示例7: 性能优化配置
 */
export function performanceOptimizedExample() {
  // 创建带性能监控的引擎
  const baseEngine = createLinkageEngine({
    debounceTime: 150, // 增加防抖时间
    enableCache: true,
    enableBatch: true,
    maxCacheSize: 200
  });
  
  const { engine, monitor } = withPerformanceMonitoring(baseEngine);
  monitor.enable();
  
  // 设置高优先级规则
  const highPriorityRules = [
    new LinkageBuilder()
      .rule()
      .id('critical_validation')
      .priority(10) // 最高优先级
      .target('criticalField')
      .when('trigger').equals('critical')
      .then().show().required()
      .build()
  ];
  
  // 设置普通优先级规则
  const normalRules = [
    LinkageTemplates.showHide({
      id: 'normal_rule_1',
      target: 'field1',
      triggerField: 'trigger1',
      showWhen: 'value1'
    }),
    LinkageTemplates.showHide({
      id: 'normal_rule_2',
      target: 'field2',
      triggerField: 'trigger2',
      showWhen: 'value2'
    })
  ];
  
  engine.setRules([...highPriorityRules, ...normalRules]);
  
  // 定期检查性能
  setInterval(() => {
    const metrics = monitor.getMetrics();
    if (metrics.averageResponseTime > 10) {
      console.warn('联动引擎响应时间过长:', metrics);
      
      // 获取优化建议
      const suggestions = monitor.getOptimizationSuggestions(engine.getRules());
      console.log('优化建议:', suggestions);
    }
  }, 30000);
  
  return { engine, monitor };
}

// ==================== 调试示例 ====================

/**
 * 示例8: 调试和错误处理
 */
export function debuggingExample() {
  // 创建带调试的引擎
  const baseEngine = createLinkageEngine();
  const { engine, debugger: linkageDebugger, errorHandler } = withDebugging(baseEngine, {
    enabled: true,
    level: 'debug',
    logToConsole: true,
    captureSnapshots: true
  });
  
  // 设置错误处理
  engine.onError((error) => {
    errorHandler.handleError(error);
    
    // 生成调试报告
    const report = linkageDebugger.generateReport();
    console.log('调试报告:', report);
  });
  
  const rules = [
    new LinkageBuilder()
      .rule()
      .id('debug_example_rule')
      .target('debugField')
      .when('debugTrigger').equals('test')
      .then().show().setValue('调试值')
      .description('用于调试的示例规则')
      .build()
  ];
  
  engine.setRules(rules);
  
  // 监听字段状态变化并记录快照
  engine.onFieldStateChange((fieldName, state) => {
    linkageDebugger.captureFieldSnapshot(
      fieldName,
      state.value,
      {
        visible: state.visible ?? true,
        disabled: state.disabled ?? false,
        required: state.required ?? false,
        options: state.options,
        props: state.props
      },
      '联动规则触发',
      '规则引擎'
    );
  });
  
  return { engine, debugger: linkageDebugger };
}

// ==================== 表格联动示例 ====================

/**
 * 示例9: 表格数据联动
 */
export function tableDataExample() {
  const engine = createLinkageEngine();
  
  // 注册表格数据处理器
  engine.registerTableProcessor('orderItems', (tableData, condition) => {
    // 处理表格数据，比如过滤、计算等
    return tableData.filter(item => {
      // 根据条件过滤数据
      return item.status === condition.value;
    });
  });
  
  const rules = [
    // 根据表格数据计算总金额
    LinkageTemplates.valueCalculation({
      id: 'calculate_order_total',
      target: 'orderTotal',
      triggerFields: ['orderItems'],
      calculator: (values) => {
        const { orderItems = [] } = values;
        return orderItems.reduce((total, item) => {
          return total + (item.price * item.quantity);
        }, 0);
      },
      description: '计算订单总金额'
    }),
    
    // 根据商品类型显示不同的配送选项
    new LinkageBuilder()
      .rule()
      .id('delivery_options_by_product_type')
      .target('deliveryOptions')
      .when('orderItems').custom('has_fragile_items')
      .then().setOptions([
        { label: '标准配送', value: 'standard' },
        { label: '小心轻放', value: 'fragile' }
      ])
      .description('有易碎商品时显示特殊配送选项')
      .build()
  ];
  
  // 注册自定义操作符检查是否有易碎商品
  engine.registerOperator('has_fragile_items', (tableData) => {
    if (!Array.isArray(tableData)) return false;
    return tableData.some(item => item.fragile === true);
  });
  
  engine.setRules(rules);
  
  return engine;
}

// ==================== 完整应用示例 ====================

/**
 * 示例10: 完整的表单联动应用
 */
export function completeFormExample() {
  // 创建完整配置的引擎
  const engine = createLinkageEngine({
    debounceTime: 100,
    enableCache: true,
    enableBatch: true
  });
  
  // 用户信息表单联动规则
  const userInfoRules = [
    // 用户类型联动
    LinkageTemplates.showHide({
      id: 'show_company_fields',
      target: 'companyInfo',
      triggerField: 'userType',
      showWhen: 'enterprise'
    }),
    
    // 年龄验证
    new LinkageBuilder()
      .rule()
      .id('age_validation')
      .target('ageWarning')
      .when('age').lessThan(18)
      .then().show().setValue('未成年用户需要监护人同意')
      .build(),
    
    // VIP用户特殊待遇
    new LinkageBuilder()
      .rule()
      .id('vip_benefits')
      .target('vipBenefits')
      .when('userLevel').equals('VIP')
      .then().show().setProps({ color: 'gold' })
      .build()
  ];
  
  // 地址信息联动规则
  const addressRules = [
    ...LinkageTemplates.optionsLinkage({
      id: 'province_city',
      target: 'city',
      triggerField: 'province',
      optionsMap: {
        '北京': [{ label: '朝阳区', value: 'chaoyang' }],
        '上海': [{ label: '浦东新区', value: 'pudong' }]
      }
    }),
    
    // 国际地址
    new LinkageBuilder()
      .rule()
      .id('international_address')
      .target('internationalFields')
      .when('country').notEquals('中国')
      .then().show()
      .build()
  ];
  
  // 订单信息联动规则
  const orderRules = [
    // 价格计算
    LinkageTemplates.valueCalculation({
      id: 'calculate_final_price',
      target: 'finalPrice',
      triggerFields: ['basePrice', 'discount', 'tax', 'shipping'],
      calculator: (values) => {
        const { basePrice = 0, discount = 0, tax = 0, shipping = 0 } = values;
        const discountedPrice = basePrice * (1 - discount / 100);
        return discountedPrice + tax + shipping;
      }
    }),
    
    // 配送方式联动
    new LinkageBuilder()
      .rule()
      .id('express_delivery_fields')
      .target('expressInfo')
      .when('deliveryMethod').equals('express')
      .then().show().required()
      .build()
  ];
  
  // 合并所有规则
  const allRules = [...userInfoRules, ...addressRules, ...orderRules];
  
  // 验证规则配置
  const config = createLinkageConfig().addRules(allRules).build();
  const validation = createLinkageConfig().addRules(allRules).validate();
  
  if (!validation.isValid) {
    console.error('规则配置错误:', validation.errors);
    return null;
  }
  
  engine.setRules(allRules);
  
  // 设置全局错误处理
  engine.onError((error) => {
    console.error('联动引擎错误:', error);
    // 这里可以添加错误上报逻辑
  });
  
  // 设置字段状态变化监听
  engine.onFieldStateChange((fieldName, state) => {
    console.log(`字段 ${fieldName} 状态更新:`, state);
    // 这里更新实际的表单字段
  });
  
  return engine;
}

// ==================== 导出所有示例 ====================

export const examples = {
  basic: {
    showHide: basicShowHideExample,
    requiredField: requiredFieldExample,
    optionsLinkage: optionsLinkageExample
  },
  advanced: {
    complexCondition: complexConditionExample,
    valueCalculation: valueCalculationExample,
    customOperator: customOperatorExample
  },
  optimization: {
    performance: performanceOptimizedExample,
    debugging: debuggingExample
  },
  integration: {
    tableData: tableDataExample,
    completeForm: completeFormExample
  }
};

// 使用示例
export function runExamples() {
  console.log('=== 联动引擎示例演示 ===');
  
  // 运行基础示例
  console.log('\n1. 基础显示/隐藏联动:');
  const basicEngine = examples.basic.showHide();
  
  // 运行复杂条件示例
  console.log('\n2. 复杂条件联动:');
  const complexEngine = examples.advanced.complexCondition();
  
  // 运行性能优化示例
  console.log('\n3. 性能优化配置:');
  const { engine: perfEngine, monitor } = examples.optimization.performance();
  
  // 运行完整应用示例
  console.log('\n4. 完整表单联动:');
  const completeEngine = examples.integration.completeForm();
  
  console.log('\n=== 示例演示完成 ===');
  
  return {
    basicEngine,
    complexEngine,
    perfEngine,
    completeEngine,
    monitor
  };
}