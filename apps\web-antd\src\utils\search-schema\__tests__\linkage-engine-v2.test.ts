/**
 * 联动引擎V2单元测试
 * 
 * 测试联动引擎的核心功能、性能和错误处理
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  createLinkageEngine,
  type LinkageEngineV2,
  type LinkageRule,
  type FieldState,
  type FormValues
} from '../linkage-engine-v2';
import { createDebugger } from '../linkage-debug';
import { LinkagePerformanceMonitor } from '../linkage-performance';

// ==================== 测试数据 ====================

const mockFormValues: FormValues = {
  category: 'electronics',
  subcategory: '',
  price: 1500,
  discount: 0,
  userType: 'vip',
  region: 'guangdong',
  city: '',
  visible: true
};

const mockFieldStates: Record<string, FieldState> = {
  category: { visible: true, disabled: false, required: false },
  subcategory: { visible: true, disabled: false, required: false },
  price: { visible: true, disabled: false, required: false },
  discount: { visible: false, disabled: false, required: false },
  userType: { visible: true, disabled: false, required: false },
  region: { visible: true, disabled: false, required: false },
  city: { visible: true, disabled: false, required: false }
};

const basicRules: LinkageRule[] = [
  {
    id: 'show_discount_for_high_price',
    target: 'discount',
    when: {
      field: 'price',
      operator: 'gt',
      value: 1000
    },
    then: {
      type: 'show'
    },
    description: '价格大于1000时显示折扣字段'
  },
  {
    id: 'update_subcategory_options',
    target: 'subcategory',
    when: {
      field: 'category',
      operator: 'eq',
      value: 'electronics'
    },
    then: {
      type: 'updateOptions',
      options: [
        { label: '手机', value: 'phone' },
        { label: '电脑', value: 'computer' }
      ]
    },
    description: '根据分类更新子分类选项'
  }
];

const complexRules: LinkageRule[] = [
  {
    id: 'complex_condition_rule',
    target: 'city',
    when: {
      type: 'and',
      conditions: [
        {
          field: 'region',
          operator: 'eq',
          value: 'guangdong'
        },
        {
          field: 'userType',
          operator: 'eq',
          value: 'vip'
        }
      ]
    },
    then: {
      type: 'updateOptions',
      options: [
        { label: '广州', value: 'guangzhou' },
        { label: '深圳', value: 'shenzhen' }
      ]
    },
    description: '广东省VIP用户显示特定城市选项'
  },
  {
    id: 'calculation_rule',
    target: 'finalPrice',
    when: {
      type: 'or',
      conditions: [
        {
          field: 'price',
          operator: 'isNotEmpty'
        },
        {
          field: 'discount',
          operator: 'isNotEmpty'
        }
      ]
    },
    then: {
      type: 'calculate',
      expression: (values) => {
        const price = Number(values.price) || 0;
        const discount = Number(values.discount) || 0;
        return price * (1 - discount);
      }
    },
    description: '计算最终价格'
  }
];

// ==================== 基础功能测试 ====================

describe('LinkageEngineV2 - 基础功能', () => {
  let engine: LinkageEngineV2;
  let mockOnFieldStateChange: ReturnType<typeof vi.fn>;
  let mockOnValueChange: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockOnFieldStateChange = vi.fn();
    mockOnValueChange = vi.fn();
    
    engine = createLinkageEngine({
      rules: basicRules,
      initialValues: mockFormValues,
      initialFieldStates: mockFieldStates,
      onFieldStateChange: mockOnFieldStateChange,
      onValueChange: mockOnValueChange
    });
  });

  afterEach(() => {
    engine.destroy();
  });

  it('应该正确初始化联动引擎', () => {
    expect(engine).toBeDefined();
    expect(engine.getRules()).toHaveLength(2);
    expect(engine.getFieldState('category')).toEqual(mockFieldStates.category);
  });

  it('应该正确添加和移除规则', () => {
    const newRule: LinkageRule = {
      id: 'test_rule',
      target: 'test_field',
      when: { field: 'test', operator: 'eq', value: 'test' },
      then: { type: 'show' },
      description: '测试规则'
    };

    engine.addRule(newRule);
    expect(engine.getRules()).toHaveLength(3);
    expect(engine.getRules().find(r => r.id === 'test_rule')).toBeDefined();

    engine.removeRule('test_rule');
    expect(engine.getRules()).toHaveLength(2);
    expect(engine.getRules().find(r => r.id === 'test_rule')).toBeUndefined();
  });

  it('应该正确更新字段值并触发联动', () => {
    // 更新价格，应该触发折扣字段显示
    engine.updateFieldValue('price', 1500);
    
    expect(mockOnFieldStateChange).toHaveBeenCalledWith(
      'discount',
      expect.objectContaining({ visible: true })
    );
  });

  it('应该正确更新字段状态', () => {
    const newState: FieldState = { visible: false, disabled: true, required: true };
    engine.updateFieldState('category', newState);
    
    expect(engine.getFieldState('category')).toEqual(newState);
    expect(mockOnFieldStateChange).toHaveBeenCalledWith('category', newState);
  });

  it('应该正确批量更新字段值', () => {
    const updates = {
      category: 'clothing',
      price: 500
    };
    
    engine.batchUpdateValues(updates);
    
    expect(engine.getFieldValue('category')).toBe('clothing');
    expect(engine.getFieldValue('price')).toBe(500);
  });
});

// ==================== 条件评估测试 ====================

describe('LinkageEngineV2 - 条件评估', () => {
  let engine: LinkageEngineV2;

  beforeEach(() => {
    engine = createLinkageEngine({
      rules: complexRules,
      initialValues: mockFormValues,
      initialFieldStates: mockFieldStates
    });
  });

  afterEach(() => {
    engine.destroy();
  });

  it('应该正确评估简单条件', () => {
    // 测试等于条件
    const result1 = engine.evaluateCondition({
      field: 'category',
      operator: 'eq',
      value: 'electronics'
    }, mockFormValues);
    expect(result1).toBe(true);

    // 测试大于条件
    const result2 = engine.evaluateCondition({
      field: 'price',
      operator: 'gt',
      value: 1000
    }, mockFormValues);
    expect(result2).toBe(true);

    // 测试小于条件
    const result3 = engine.evaluateCondition({
      field: 'price',
      operator: 'lt',
      value: 1000
    }, mockFormValues);
    expect(result3).toBe(false);
  });

  it('应该正确评估复合条件', () => {
    // 测试AND条件
    const andCondition = {
      type: 'and' as const,
      conditions: [
        { field: 'region', operator: 'eq' as const, value: 'guangdong' },
        { field: 'userType', operator: 'eq' as const, value: 'vip' }
      ]
    };
    
    const result1 = engine.evaluateCondition(andCondition, mockFormValues);
    expect(result1).toBe(true);

    // 测试OR条件
    const orCondition = {
      type: 'or' as const,
      conditions: [
        { field: 'region', operator: 'eq' as const, value: 'beijing' },
        { field: 'userType', operator: 'eq' as const, value: 'vip' }
      ]
    };
    
    const result2 = engine.evaluateCondition(orCondition, mockFormValues);
    expect(result2).toBe(true);
  });

  it('应该正确处理空值和未定义值', () => {
    const valuesWithEmpty = { ...mockFormValues, subcategory: '', city: undefined };
    
    // 测试isEmpty条件
    const result1 = engine.evaluateCondition({
      field: 'subcategory',
      operator: 'isEmpty'
    }, valuesWithEmpty);
    expect(result1).toBe(true);

    // 测试isNotEmpty条件
    const result2 = engine.evaluateCondition({
      field: 'category',
      operator: 'isNotEmpty'
    }, valuesWithEmpty);
    expect(result2).toBe(true);
  });

  it('应该正确处理数组条件', () => {
    // 测试in条件
    const result1 = engine.evaluateCondition({
      field: 'category',
      operator: 'in',
      value: ['electronics', 'clothing', 'food']
    }, mockFormValues);
    expect(result1).toBe(true);

    // 测试notIn条件
    const result2 = engine.evaluateCondition({
      field: 'category',
      operator: 'notIn',
      value: ['clothing', 'food']
    }, mockFormValues);
    expect(result2).toBe(true);
  });

  it('应该正确处理字符串条件', () => {
    const valuesWithText = { ...mockFormValues, description: 'This is a test description' };
    
    // 测试contains条件
    const result1 = engine.evaluateCondition({
      field: 'description',
      operator: 'contains',
      value: 'test'
    }, valuesWithText);
    expect(result1).toBe(true);

    // 测试startsWith条件
    const result2 = engine.evaluateCondition({
      field: 'description',
      operator: 'startsWith',
      value: 'This'
    }, valuesWithText);
    expect(result2).toBe(true);

    // 测试endsWith条件
    const result3 = engine.evaluateCondition({
      field: 'description',
      operator: 'endsWith',
      value: 'description'
    }, valuesWithText);
    expect(result3).toBe(true);
  });
});

// ==================== 动作执行测试 ====================

describe('LinkageEngineV2 - 动作执行', () => {
  let engine: LinkageEngineV2;
  let mockOnFieldStateChange: ReturnType<typeof vi.fn>;
  let mockOnValueChange: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockOnFieldStateChange = vi.fn();
    mockOnValueChange = vi.fn();
    
    engine = createLinkageEngine({
      rules: [],
      initialValues: mockFormValues,
      initialFieldStates: mockFieldStates,
      onFieldStateChange: mockOnFieldStateChange,
      onValueChange: mockOnValueChange
    });
  });

  afterEach(() => {
    engine.destroy();
  });

  it('应该正确执行显示/隐藏动作', () => {
    // 执行显示动作
    engine.executeAction('discount', { type: 'show' });
    expect(mockOnFieldStateChange).toHaveBeenCalledWith(
      'discount',
      expect.objectContaining({ visible: true })
    );

    // 执行隐藏动作
    engine.executeAction('discount', { type: 'hide' });
    expect(mockOnFieldStateChange).toHaveBeenCalledWith(
      'discount',
      expect.objectContaining({ visible: false })
    );
  });

  it('应该正确执行启用/禁用动作', () => {
    // 执行启用动作
    engine.executeAction('category', { type: 'enable' });
    expect(mockOnFieldStateChange).toHaveBeenCalledWith(
      'category',
      expect.objectContaining({ disabled: false })
    );

    // 执行禁用动作
    engine.executeAction('category', { type: 'disable' });
    expect(mockOnFieldStateChange).toHaveBeenCalledWith(
      'category',
      expect.objectContaining({ disabled: true })
    );
  });

  it('应该正确执行设置值动作', () => {
    engine.executeAction('subcategory', { type: 'setValue', value: 'phone' });
    expect(mockOnValueChange).toHaveBeenCalledWith('subcategory', 'phone');
  });

  it('应该正确执行清空值动作', () => {
    engine.executeAction('subcategory', { type: 'clearValue' });
    expect(mockOnValueChange).toHaveBeenCalledWith('subcategory', undefined);
  });

  it('应该正确执行更新选项动作', () => {
    const options = [
      { label: '选项1', value: '1' },
      { label: '选项2', value: '2' }
    ];
    
    engine.executeAction('subcategory', { type: 'updateOptions', options });
    expect(mockOnFieldStateChange).toHaveBeenCalledWith(
      'subcategory',
      expect.objectContaining({ options })
    );
  });

  it('应该正确执行计算动作', () => {
    const calculateFn = vi.fn().mockReturnValue(1200);
    
    engine.executeAction('finalPrice', {
      type: 'calculate',
      expression: calculateFn
    });
    
    expect(calculateFn).toHaveBeenCalledWith(mockFormValues);
    expect(mockOnValueChange).toHaveBeenCalledWith('finalPrice', 1200);
  });

  it('应该正确执行批量更新动作', () => {
    const updates = [
      { target: 'field1', action: { type: 'show' as const } },
      { target: 'field2', action: { type: 'hide' as const } },
      { target: 'field3', action: { type: 'setValue' as const, value: 'test' } }
    ];
    
    engine.executeAction('*', { type: 'batchUpdate', updates });
    
    expect(mockOnFieldStateChange).toHaveBeenCalledTimes(2);
    expect(mockOnValueChange).toHaveBeenCalledWith('field3', 'test');
  });
});

// ==================== 性能测试 ====================

describe('LinkageEngineV2 - 性能测试', () => {
  let engine: LinkageEngineV2;
  let performanceMonitor: LinkagePerformanceMonitor;

  beforeEach(() => {
    performanceMonitor = new LinkagePerformanceMonitor();
    
    // 创建大量规则进行性能测试
    const manyRules: LinkageRule[] = [];
    for (let i = 0; i < 100; i++) {
      manyRules.push({
        id: `rule_${i}`,
        target: `field_${i}`,
        when: {
          field: 'trigger',
          operator: 'eq',
          value: `value_${i}`
        },
        then: {
          type: 'show'
        },
        description: `规则 ${i}`
      });
    }

    engine = createLinkageEngine({
      rules: manyRules,
      initialValues: { trigger: 'value_50' },
      initialFieldStates: {},
      performanceMonitor
    });
  });

  afterEach(() => {
    engine.destroy();
  });

  it('应该在合理时间内处理大量规则', () => {
    const startTime = performance.now();
    
    // 触发所有规则评估
    engine.updateFieldValue('trigger', 'value_50');
    
    const endTime = performance.now();
    const executionTime = endTime - startTime;
    
    // 应该在100ms内完成
    expect(executionTime).toBeLessThan(100);
  });

  it('应该正确记录性能指标', () => {
    engine.updateFieldValue('trigger', 'value_25');
    
    const metrics = performanceMonitor.getMetrics();
    expect(metrics.totalExecutions).toBeGreaterThan(0);
    expect(metrics.averageExecutionTime).toBeGreaterThan(0);
  });

  it('应该支持防抖功能', async () => {
    const mockCallback = vi.fn();
    engine.onFieldStateChange(mockCallback);
    
    // 快速连续更新
    engine.updateFieldValue('trigger', 'value_1');
    engine.updateFieldValue('trigger', 'value_2');
    engine.updateFieldValue('trigger', 'value_3');
    
    // 等待防抖时间
    await new Promise(resolve => setTimeout(resolve, 350));
    
    // 应该只触发一次回调
    expect(mockCallback).toHaveBeenCalledTimes(1);
  });
});

// ==================== 错误处理测试 ====================

describe('LinkageEngineV2 - 错误处理', () => {
  let engine: LinkageEngineV2;
  let mockErrorHandler: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockErrorHandler = vi.fn();
    
    engine = createLinkageEngine({
      rules: [],
      initialValues: {},
      initialFieldStates: {},
      onError: mockErrorHandler
    });
  });

  afterEach(() => {
    engine.destroy();
  });

  it('应该正确处理无效的规则', () => {
    const invalidRule = {
      id: 'invalid_rule',
      target: '',
      when: null,
      then: null
    } as any;
    
    expect(() => engine.addRule(invalidRule)).toThrow();
  });

  it('应该正确处理计算函数中的错误', () => {
    const errorRule: LinkageRule = {
      id: 'error_rule',
      target: 'result',
      when: { field: 'trigger', operator: 'eq', value: 'error' },
      then: {
        type: 'calculate',
        expression: () => {
          throw new Error('计算错误');
        }
      },
      description: '错误规则'
    };
    
    engine.addRule(errorRule);
    engine.updateFieldValue('trigger', 'error');
    
    expect(mockErrorHandler).toHaveBeenCalledWith(
      expect.objectContaining({
        message: expect.stringContaining('计算错误')
      })
    );
  });

  it('应该正确处理循环依赖', () => {
    const rule1: LinkageRule = {
      id: 'rule1',
      target: 'field2',
      when: { field: 'field1', operator: 'eq', value: 'trigger' },
      then: { type: 'setValue', value: 'value2' },
      description: '规则1'
    };
    
    const rule2: LinkageRule = {
      id: 'rule2',
      target: 'field1',
      when: { field: 'field2', operator: 'eq', value: 'value2' },
      then: { type: 'setValue', value: 'trigger' },
      description: '规则2'
    };
    
    engine.addRule(rule1);
    engine.addRule(rule2);
    
    engine.updateFieldValue('field1', 'trigger');
    
    expect(mockErrorHandler).toHaveBeenCalledWith(
      expect.objectContaining({
        message: expect.stringContaining('循环依赖')
      })
    );
  });
});

// ==================== 调试功能测试 ====================

describe('LinkageEngineV2 - 调试功能', () => {
  let engine: LinkageEngineV2;
  let linkageDebugger: ReturnType<typeof createDebugger>;

  beforeEach(() => {
    linkageDebugger = createDebugger({ enabled: true });
    
    engine = createLinkageEngine({
      rules: basicRules,
      initialValues: mockFormValues,
      initialFieldStates: mockFieldStates,
      debugger: linkageDebugger
    });
  });

  afterEach(() => {
    engine.destroy();
  });

  it('应该正确记录调试信息', () => {
    engine.updateFieldValue('price', 1500);
    
    const debugInfo = engine.getDebugInfo();
    expect(debugInfo.executionHistory).toHaveLength(1);
    expect(debugInfo.ruleExecutionStats).toBeDefined();
  });

  it('应该正确生成调试报告', () => {
    engine.updateFieldValue('price', 1500);
    engine.updateFieldValue('category', 'electronics');
    
    const report = linkageDebugger.generateReport();
    expect(report.summary.totalRulesExecuted).toBeGreaterThan(0);
    expect(report.ruleStats).toBeDefined();
  });

  it('应该支持导出和导入调试数据', () => {
    engine.updateFieldValue('price', 1500);
    
    const exportedData = linkageDebugger.exportData();
    expect(exportedData).toBeDefined();
    expect(exportedData.events).toHaveLength(1);
    
    const newLinkageDebugger = createDebugger({ enabled: true });
    newLinkageDebugger.importData(exportedData);
    
    const importedEvents = newLinkageDebugger.getEvents();
    expect(importedEvents).toHaveLength(1);
  });
});

// ==================== 集成测试 ====================

describe('LinkageEngineV2 - 集成测试', () => {
  it('应该正确处理完整的表单联动场景', () => {
    const mockOnFieldStateChange = vi.fn();
    const mockOnValueChange = vi.fn();
    
    const engine = createLinkageEngine({
      rules: [
        {
          id: 'category_subcategory',
          target: 'subcategory',
          when: { field: 'category', operator: 'eq', value: 'electronics' },
          then: {
            type: 'updateOptions',
            options: [
              { label: '手机', value: 'phone' },
              { label: '电脑', value: 'computer' }
            ]
          },
          description: '分类联动'
        },
        {
          id: 'price_discount',
          target: 'discount',
          when: { field: 'price', operator: 'gt', value: 1000 },
          then: { type: 'show' },
          description: '价格联动'
        },
        {
          id: 'calculate_final_price',
          target: 'finalPrice',
          when: {
            type: 'and',
            conditions: [
              { field: 'price', operator: 'isNotEmpty' },
              { field: 'discount', operator: 'isNotEmpty' }
            ]
          },
          then: {
            type: 'calculate',
            expression: (values) => {
              const price = Number(values.price) || 0;
              const discount = Number(values.discount) || 0;
              return price * (1 - discount);
            }
          },
          description: '计算最终价格'
        }
      ],
      initialValues: {
        category: '',
        subcategory: '',
        price: 0,
        discount: 0,
        finalPrice: 0
      },
      initialFieldStates: {
        category: { visible: true, disabled: false, required: false },
        subcategory: { visible: true, disabled: false, required: false },
        price: { visible: true, disabled: false, required: false },
        discount: { visible: false, disabled: false, required: false },
        finalPrice: { visible: true, disabled: true, required: false }
      },
      onFieldStateChange: mockOnFieldStateChange,
      onValueChange: mockOnValueChange
    });

    // 步骤1：选择分类
    engine.updateFieldValue('category', 'electronics');
    expect(mockOnFieldStateChange).toHaveBeenCalledWith(
      'subcategory',
      expect.objectContaining({
        options: expect.arrayContaining([
          { label: '手机', value: 'phone' },
          { label: '电脑', value: 'computer' }
        ])
      })
    );

    // 步骤2：输入价格
    engine.updateFieldValue('price', 1500);
    expect(mockOnFieldStateChange).toHaveBeenCalledWith(
      'discount',
      expect.objectContaining({ visible: true })
    );

    // 步骤3：输入折扣
    engine.updateFieldValue('discount', 0.1);
    expect(mockOnValueChange).toHaveBeenCalledWith('finalPrice', 1350);

    engine.destroy();
  });
});